@echo off
REM APK Protection using dcc.py with selective DEX targeting
setlocal enabledelayedexpansion

REM Get the input APK path from the first argument
set "INPUT_APK=%~1"

REM Extract just the filename without path and extension for output naming
for %%F in ("%INPUT_APK%") do (
    set "APK_NAME=%%~nF"
)

REM Set output path to AmpedGems\_upload directory (relative to AutoProtector)
set "OUTPUT_DIR=%~dp0..\_upload"
set "OUTPUT_APK=%OUTPUT_DIR%\%APK_NAME%_protected.apk"

REM Ensure output directory exists
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM Use relative path to Python from AutoProtector directory
set "PYTHON_EXE=%~dp0..\work_files\Installation_Files\Amped_env\Scripts\python.exe"

REM Run dcc.py with selective DEX targeting (target latest DEX)
"%PYTHON_EXE%" dcc.py -a "%INPUT_APK%" -o "%OUTPUT_APK%" --target-dex last

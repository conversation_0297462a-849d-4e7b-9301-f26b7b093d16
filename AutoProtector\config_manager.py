"""
Configuration Manager for Enhanced DEX2C
Handles loading and validation of tool paths and settings
"""

import json
import os
import sys
from pathlib import Path


class ConfigManager:
    def __init__(self, config_file="config.json"):
        self.config_file = config_file
        self.base_dir = os.path.dirname(os.path.abspath(__file__))

        # Auto-detect installation directory
        self.installation_dir = self._detect_installation_directory()

        # Load configuration with auto-detection
        self.config = self._load_config_with_autodetect()
        self._validate_config()

    def _detect_installation_directory(self):
        """Auto-detect installation directory by looking for common patterns"""
        current_path = Path(self.base_dir)

        # Search patterns for installation directories
        search_patterns = [
            # Look for newupgrade directory structure
            "**/newupgrade",
            "**/work_files/Installation_Files",
            "**/work_files/Java",
            # Look for AmpedGems structure
            "**/AmpedGems",
            "**/work_files",
            # Look for common installation patterns
            "**/Installation_Files",
            "**/Amped_env",
            "**/Amp_Env"
        ]

        # Search up the directory tree
        for parent in [current_path] + list(current_path.parents):
            for pattern in search_patterns:
                matches = list(parent.glob(pattern))
                if matches:
                    # Found a potential installation directory
                    match = matches[0]
                    if "newupgrade" in str(match):
                        return str(match)
                    elif "work_files" in str(match):
                        return str(match.parent)
                    elif "Installation_Files" in str(match):
                        return str(match.parent.parent) if "work_files" in str(match.parent) else str(match.parent)
                    elif "Amped_env" in str(match) or "Amp_Env" in str(match):
                        return str(match.parent.parent) if "Installation_Files" in str(match.parent) else str(match.parent)

        return None

    def _auto_detect_tools(self):
        """Auto-detect tool paths based on installation directory"""
        auto_config = {}

        if not self.installation_dir:
            return auto_config

        install_path = Path(self.installation_dir)

        # Auto-detect Python virtual environment
        venv_paths = [
            install_path / "work_files" / "Installation_Files" / "Amped_env",
            install_path / "work_files" / "Installation_Files" / "Amp_Env",
            install_path / "Amped_env",
            install_path / "Amp_Env",
            install_path.parent / "Amp_Env"  # For cases where dex2c is in subdirectory
        ]

        for venv_path in venv_paths:
            python_exe = venv_path / "Scripts" / "python.exe"
            if python_exe.exists():
                auto_config["python_executable"] = str(python_exe)
                break

        # Auto-detect Java JDK
        java_paths = [
            install_path / "work_files" / "Java" / "jdk-21" / "bin" / "java.exe",
            install_path / "work_files" / "Java" / "jdk-17" / "bin" / "java.exe",
            install_path / "Java" / "jdk-21" / "bin" / "java.exe",
            install_path / "java" / "bin" / "java.exe"
        ]

        for java_path in java_paths:
            if java_path.exists():
                auto_config["java_executable"] = str(java_path)
                break

        # Auto-detect Android SDK/NDK
        sdk_paths = [
            install_path / "work_files" / "SDK",
            install_path / "SDK",
            install_path / "android-sdk"
        ]

        for sdk_path in sdk_paths:
            if sdk_path.exists():
                auto_config["android_sdk"] = str(sdk_path)
                # Look for NDK in SDK
                ndk_paths = [
                    sdk_path / "ndk-bundle",
                    sdk_path / "ndk" / "26.3.11579264",
                    sdk_path / "ndk" / "25.2.9519653"
                ]
                for ndk_path in ndk_paths:
                    if ndk_path.exists():
                        auto_config["ndk_directory"] = str(ndk_path)
                        break
                break

        return auto_config

    def _load_config_with_autodetect(self):
        """Load configuration with auto-detection fallback"""
        # First try to load existing config
        config_path = os.path.join(self.base_dir, self.config_file)

        if os.path.exists(config_path):
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                print(f"[OK] Loaded configuration from {self.config_file}")
            except json.JSONDecodeError as e:
                print(f"[WARNING] Invalid JSON in {self.config_file}: {e}")
                config = self._get_default_config()
        else:
            print(f"[INFO] Configuration file {self.config_file} not found, using auto-detection")
            config = self._get_default_config()

        # Auto-detect tools and merge with existing config
        auto_detected = self._auto_detect_tools()
        if auto_detected:
            print(f"[OK] Auto-detected installation at: {self.installation_dir}")
            config = self._merge_auto_detected_config(config, auto_detected)

        return config

    def _get_default_config(self):
        """Get default configuration structure"""
        return {
            "tools": {
                "python": {"executable": "..\\..\\work_files\\Installation_Files\\Amped_env\\Scripts\\python.exe", "description": "Python interpreter"},
                "java": {"executable": "..\\..\\work_files\\Java\\jdk-21\\bin\\java.exe", "description": "Java JDK"},
                "apktool": {"jar": "tools/apktool.jar", "description": "APK tool"},
                "zipalign": {"executable": "tools/zipalign.exe", "description": "APK alignment tool"},
                "apksigner": {"jar": "tools/apksigner.jar", "description": "APK signing tool"},
                "manifest_editor": {"jar": "tools/manifest-editor.jar", "description": "Manifest editor"}
            },
            "build": {
                "ndk": {"directory": "..\\..\\work_files\\SDK\\ndk\\26.3.11579264", "description": "Android NDK"},
                "architectures": ["arm64-v8a"],
                "optimization_level": "O2",
                "debug_symbols": False
            },
            "signing": {
                "keystore": "keystore/debug.keystore",
                "password": "android",
                "alias": "androiddebugkey",
                "alias_password": "android"
            },
            "paths": {
                "temp_directory": ".tmp",
                "output_directory": "output",
                "project_directory": "project",
                "source_archive": "project-source.zip"
            },
            "protection": {
                "default_filter": "filter.txt",
                "obfuscation": True,
                "dynamic_register": False,
                "skip_synthetic": False,
                "force_keep_libs": False
            },
            "logging": {
                "level": "INFO",
                "format": "[%(levelname)s] %(name)s: %(message)s",
                "file": None
            },
            "advanced": {
                "max_dex_methods": 65536,
                "compilation_threads": 4,
                "memory_limit_mb": 4096,
                "timeout_seconds": 3600
            }
        }

    def _merge_auto_detected_config(self, config, auto_detected):
        """Merge auto-detected paths into configuration"""
        if "python_executable" in auto_detected:
            config["tools"]["python"]["executable"] = auto_detected["python_executable"]
            print(f"  [OK] Python: {auto_detected['python_executable']}")

        if "java_executable" in auto_detected:
            config["tools"]["java"]["executable"] = auto_detected["java_executable"]
            print(f"  [OK] Java: {auto_detected['java_executable']}")

        if "ndk_directory" in auto_detected:
            config["build"]["ndk"]["directory"] = auto_detected["ndk_directory"]
            print(f"  [OK] NDK: {auto_detected['ndk_directory']}")

        if "android_sdk" in auto_detected:
            # Update zipalign path if SDK is found
            zipalign_path = Path(auto_detected["android_sdk"]) / "build-tools"
            if zipalign_path.exists():
                # Find latest build-tools version
                build_tools_versions = [d for d in zipalign_path.iterdir() if d.is_dir()]
                if build_tools_versions:
                    latest_version = sorted(build_tools_versions)[-1]
                    zipalign_exe = latest_version / "zipalign.exe"
                    if zipalign_exe.exists():
                        config["tools"]["zipalign"]["executable"] = str(zipalign_exe)
                        print(f"  [OK] zipalign: {zipalign_exe}")

        return config

    def _load_config(self):
        """Load configuration from JSON file"""
        config_path = os.path.join(self.base_dir, self.config_file)
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"Configuration file not found: {config_path}")
        
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except json.JSONDecodeError as e:
            raise ValueError(f"Invalid JSON in configuration file: {e}")
    
    def _resolve_path(self, path):
        """Resolve relative paths to absolute paths"""
        if os.path.isabs(path):
            return path
        return os.path.abspath(os.path.join(self.base_dir, path))
    
    def _validate_config(self):
        """Validate configuration and check if required tools exist"""
        errors = []
        
        # Check tool executables
        tools = self.config.get('tools', {})
        for tool_name, tool_config in tools.items():
            if 'executable' in tool_config:
                exe_path = self._resolve_path(tool_config['executable'])
                if not os.path.exists(exe_path):
                    errors.append(f"Tool '{tool_name}' executable not found: {exe_path}")
            elif 'jar' in tool_config:
                jar_path = self._resolve_path(tool_config['jar'])
                if not os.path.exists(jar_path):
                    errors.append(f"Tool '{tool_name}' JAR not found: {jar_path}")
        
        # Check NDK directory
        ndk_dir = self.get_ndk_directory()
        if not os.path.exists(ndk_dir):
            errors.append(f"NDK directory not found: {ndk_dir}")
        
        # Check keystore
        keystore_path = self.get_keystore_path()
        if not os.path.exists(keystore_path):
            errors.append(f"Keystore not found: {keystore_path}")
        
        if errors:
            print("Configuration validation warnings:")
            for error in errors:
                print(f"  - {error}")
            print("Some features may not work correctly.")
    
    # Tool path getters
    def get_python_executable(self):
        """Get Python executable path"""
        return self._resolve_path(self.config['tools']['python']['executable'])
    
    def get_java_executable(self):
        """Get Java executable path"""
        return self._resolve_path(self.config['tools']['java']['executable'])
    
    def get_apktool_jar(self):
        """Get Apktool JAR path"""
        return self._resolve_path(self.config['tools']['apktool']['jar'])
    
    def get_zipalign_executable(self):
        """Get zipalign executable path"""
        return self._resolve_path(self.config['tools']['zipalign']['executable'])
    
    def get_apksigner_jar(self):
        """Get apksigner JAR path"""
        return self._resolve_path(self.config['tools']['apksigner']['jar'])
    
    def get_manifest_editor_jar(self):
        """Get manifest editor JAR path"""
        return self._resolve_path(self.config['tools']['manifest_editor']['jar'])
    
    # Build configuration getters
    def get_ndk_directory(self):
        """Get NDK directory path"""
        return self._resolve_path(self.config['build']['ndk']['directory'])
    
    def get_ndk_build_command(self):
        """Get NDK build command"""
        ndk_dir = self.get_ndk_directory()
        if os.name == 'nt':  # Windows
            return os.path.join(ndk_dir, 'ndk-build.cmd')
        else:
            return os.path.join(ndk_dir, 'ndk-build')
    
    def get_architectures(self):
        """Get target architectures"""
        return self.config['build']['architectures']
    
    def get_optimization_level(self):
        """Get optimization level"""
        return self.config['build']['optimization_level']
    
    def is_debug_symbols_enabled(self):
        """Check if debug symbols are enabled"""
        return self.config['build']['debug_symbols']
    
    # Signing configuration getters
    def get_keystore_path(self):
        """Get keystore path"""
        return self._resolve_path(self.config['signing']['keystore'])
    
    def get_keystore_password(self):
        """Get keystore password"""
        return self.config['signing']['password']
    
    def get_keystore_alias(self):
        """Get keystore alias"""
        return self.config['signing']['alias']
    
    def get_keystore_alias_password(self):
        """Get keystore alias password"""
        return self.config['signing']['alias_password']
    
    # Path getters
    def get_temp_directory(self):
        """Get temporary directory path"""
        return self._resolve_path(self.config['paths']['temp_directory'])
    
    def get_output_directory(self):
        """Get output directory path"""
        return self._resolve_path(self.config['paths']['output_directory'])
    
    def get_project_directory(self):
        """Get project directory path"""
        return self._resolve_path(self.config['paths']['project_directory'])
    
    def get_source_archive(self):
        """Get source archive path"""
        return self._resolve_path(self.config['paths']['source_archive'])
    
    # Protection settings getters
    def get_default_filter(self):
        """Get default filter file path"""
        return self._resolve_path(self.config['protection']['default_filter'])
    
    def is_obfuscation_enabled(self):
        """Check if obfuscation is enabled"""
        return self.config['protection']['obfuscation']
    
    def is_dynamic_register_enabled(self):
        """Check if dynamic register is enabled"""
        return self.config['protection']['dynamic_register']
    
    def is_skip_synthetic_enabled(self):
        """Check if skip synthetic is enabled"""
        return self.config['protection']['skip_synthetic']
    
    def is_force_keep_libs_enabled(self):
        """Check if force keep libs is enabled"""
        return self.config['protection']['force_keep_libs']
    
    # Logging configuration getters
    def get_log_level(self):
        """Get logging level"""
        return self.config['logging']['level']
    
    def get_log_format(self):
        """Get logging format"""
        return self.config['logging']['format']
    
    def get_log_file(self):
        """Get log file path"""
        log_file = self.config['logging']['file']
        return self._resolve_path(log_file) if log_file else None
    
    # Advanced settings getters
    def get_max_dex_methods(self):
        """Get maximum DEX methods limit"""
        return self.config['advanced']['max_dex_methods']
    
    def get_compilation_threads(self):
        """Get number of compilation threads"""
        return self.config['advanced']['compilation_threads']
    
    def get_memory_limit_mb(self):
        """Get memory limit in MB"""
        return self.config['advanced']['memory_limit_mb']
    
    def get_timeout_seconds(self):
        """Get timeout in seconds"""
        return self.config['advanced']['timeout_seconds']
    
    # Utility methods
    def create_directories(self):
        """Create necessary directories if they don't exist"""
        directories = [
            self.get_temp_directory(),
            self.get_output_directory(),
            self.get_project_directory()
        ]
        
        for directory in directories:
            os.makedirs(directory, exist_ok=True)
    
    def get_tool_info(self):
        """Get information about all configured tools"""
        info = []
        tools = self.config.get('tools', {})
        
        for tool_name, tool_config in tools.items():
            description = tool_config.get('description', 'No description')
            
            if 'executable' in tool_config:
                path = self._resolve_path(tool_config['executable'])
                exists = os.path.exists(path)
            elif 'jar' in tool_config:
                path = self._resolve_path(tool_config['jar'])
                exists = os.path.exists(path)
            else:
                path = "Not configured"
                exists = False
            
            info.append({
                'name': tool_name,
                'description': description,
                'path': path,
                'exists': exists
            })
        
        return info
    
    def save_current_config(self):
        """Save current configuration to file"""
        config_path = os.path.join(self.base_dir, self.config_file)
        try:
            with open(config_path, 'w', encoding='utf-8') as f:
                json.dump(self.config, f, indent=4)
            print(f"[OK] Configuration saved to {self.config_file}")
            return True
        except Exception as e:
            print(f"[ERROR] Failed to save configuration: {e}")
            return False

    def print_configuration_summary(self):
        """Print a summary of the current configuration"""
        print("=== DEX2C Configuration Summary ===")
        print(f"Base directory: {self.base_dir}")
        print(f"Configuration file: {self.config_file}")
        if self.installation_dir:
            print(f"Installation directory: {self.installation_dir}")
        print()

        print("Tools:")
        for tool_info in self.get_tool_info():
            status = "[OK]" if tool_info['exists'] else "[MISSING]"
            print(f"  {status} {tool_info['name']}: {tool_info['path']}")
        print()

        print("Build Configuration:")
        print(f"  NDK Directory: {self.get_ndk_directory()}")
        print(f"  Architectures: {', '.join(self.get_architectures())}")
        print(f"  Optimization: {self.get_optimization_level()}")
        print()

        print("Signing:")
        print(f"  Keystore: {self.get_keystore_path()}")
        print(f"  Alias: {self.get_keystore_alias()}")
        print()


# Global configuration instance
config = ConfigManager()


if __name__ == "__main__":
    # Print configuration summary when run directly
    config.print_configuration_summary()

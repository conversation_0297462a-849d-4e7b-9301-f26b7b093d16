@echo off
echo ========================================
echo AmpedGems Template - Normal Build
echo ========================================

REM Auto-setup environment
set "SETUP_ENV=%~dp0..\setup_env.bat"
if exist "%SETUP_ENV%" (
    call "%SETUP_ENV%"
    
    REM Force the correct JAVA_HOME (override any system setting)
    set "JAVA_HOME=D:\space\AmpedGems\work_files\Java\jdk-21"
    set "GRADLE_HOME=D:\space\AmpedGems\work_files\Gradle\gradle-8.14.1"
    set "PATH=%JAVA_HOME%\bin;%GRADLE_HOME%\bin;%PATH%"
    echo Forced Java to: %JAVA_HOME%
    echo.
) else (
    echo ERROR: setup_env.bat not found. Please run _install.bat first.
    pause
    exit /b 1
)

echo Building WITHOUT O-MVLL (normal build)...

REM Change to template directory
cd /d "%~dp0"

REM Clear O-MVLL environment variables
set ENABLE_OMVLL=
set OMVLL_PLUGIN_PATH=

if exist "gradlew.bat" (
    call gradlew.bat assembleDebug
) else (
    echo ERROR: gradlew.bat not found in template directory
    pause
    exit /b 1
)

echo.
echo Build completed!
pause

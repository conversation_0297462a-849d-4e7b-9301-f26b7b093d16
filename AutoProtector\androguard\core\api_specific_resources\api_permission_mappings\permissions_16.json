{"Landroid/accounts/AbstractAccountAuthenticator$Transport;-addAccount-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-confirmCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-editProperties-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAccountRemovalAllowed-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAuthToken-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAuthTokenLabel-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-hasFeatures-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; [Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-updateCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AccountManagerService;-addAccount-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-addAcount-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Z Landroid/os/Bundle;)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-clearPassword-(Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-confirmCredentials-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Landroid/os/Bundle; Z)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-editProperties-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Z)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-getAccounts-(Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-getAccountsByFeatures-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; [Ljava/lang/String;)V": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-getAuthToken-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Ljava/lang/String; Z Z Landroid/os/Bundle;)V": ["android.permission.USE_CREDENTIALS"], "Landroid/accounts/AccountManagerService;-getPassword-(Landroid/accounts/Account;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-getUserData-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-hasFeatures-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; [Ljava/lang/String;)V": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-invalidateAuthToken-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MANAGE_ACCOUNTS", "android.permission.USE_CREDENTIALS"], "Landroid/accounts/AccountManagerService;-peekAuthToken-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-removeAccount-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-setAuthToken-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-setPassword-(Landroid/accounts/Account; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-setUserData-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManagerService;-updateCredentials-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Ljava/lang/String; Z Landroid/os/Bundle;)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/content/ContentService;-addPeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle; J)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Landroid/content/ContentService;-getCurrentSyncs-()Ljava/util/List;": ["android.permission.READ_SYNC_STATS"], "Landroid/content/ContentService;-getIsSyncable-(Landroid/accounts/Account; Ljava/lang/String;)I": ["android.permission.READ_SYNC_SETTINGS"], "Landroid/content/ContentService;-getMasterSyncAutomatically-()Z": ["android.permission.READ_SYNC_SETTINGS"], "Landroid/content/ContentService;-getPeriodicSyncs-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/util/List;": ["android.permission.READ_SYNC_SETTINGS"], "Landroid/content/ContentService;-getSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.READ_SYNC_SETTINGS"], "Landroid/content/ContentService;-getSyncStatus-(Landroid/accounts/Account; Ljava/lang/String;)Landroid/content/SyncStatusInfo;": ["android.permission.READ_SYNC_STATS"], "Landroid/content/ContentService;-isSyncActive-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.READ_SYNC_STATS"], "Landroid/content/ContentService;-isSyncPending-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.READ_SYNC_STATS"], "Landroid/content/ContentService;-removePeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Landroid/content/ContentService;-setIsSyncable-(Landroid/accounts/Account; Ljava/lang/String; I)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Landroid/content/ContentService;-setMasterSyncAutomatically-(Z)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Landroid/content/ContentService;-setSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String; Z)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Landroid/media/AudioService;-registerMediaButtonEventReceiverForCalls-(Landroid/content/ComponentName;)V": ["android.permission.MODIFY_PHONE_STATE"], "Landroid/media/AudioService;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-setMode-(I Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-setRingtonePlayer-(Landroid/media/IRingtonePlayer;)V": ["android.permission.REMOTE_AUDIO_PLAYBACK"], "Landroid/media/AudioService;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-startBluetoothSco-(Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-stopBluetoothSco-(Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-unregisterMediaButtonEventReceiverForCalls-()V": ["android.permission.MODIFY_PHONE_STATE"], "Landroid/net/wifi/p2p/WifiP2pService;-getMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/server/BluetoothA2dpService;-allowIncomingConnect-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-connectSinkInternal-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-disconnectSinkInternal-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothA2dpService;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothA2dpService;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothA2dpService;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothA2dpService;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-resumeSink-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothA2dpService;-suspendSink-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-addRfcommServiceRecord-(Ljava/lang/String; Landroid/os/ParcelUuid; I Landroid/os/IBinder;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-allowIncomingProfileConnect-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-cancelBondProcess-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-cancelPairingUserInput-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-changeApplicationBluetoothState-(Z Landroid/bluetooth/IBluetoothStateChangeCallback; Landroid/os/IBinder;)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-connectChannelToSink-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-connectHeadset-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-connectInputDevice-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-connectPanDevice-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-createBond-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-createBondOutOfBand-(Ljava/lang/String; [B [B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-disable-(Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-disconnectHeadset-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-disconnectInputDevice-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-disconnectPanDevice-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-enable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-enableNoAutoConnect-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-fetchRemoteUuids-(Ljava/lang/String; Landroid/os/ParcelUuid; Landroid/bluetooth/IBluetoothCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getBluetoothState-()I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getBondState-(Ljava/lang/String;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getConnectedHealthDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getConnectedInputDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getConnectedPanDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getDiscoverableTimeout-()I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getHealthDeviceConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getHealthDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getInputDeviceConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getInputDevicePriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getInputDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getPanDeviceConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getPanDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getRemoteAlias-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getRemoteClass-(Ljava/lang/String;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getRemoteName-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getRemoteServiceChannel-(Ljava/lang/String; Landroid/os/ParcelUuid;)I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getRemoteUuids-(Ljava/lang/String;)[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getTrustState-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-isTetheringOn-()Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-listBonds-()[Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-readOutOfBandData-()[B": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-registerAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration; Landroid/bluetooth/IBluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-removeBond-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-removeServiceRecord-(I)V": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-setBluetoothTethering-(Z)V": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-setDeviceOutOfBandData-(Ljava/lang/String; [B [B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setDiscoverableTimeout-(I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setInputDevicePriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setPairingConfirmation-(Ljava/lang/String; Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setPasskey-(Ljava/lang/String; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setPin-(Ljava/lang/String; [B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setRemoteAlias-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH"], "Landroid/server/BluetoothService;-setRemoteOutOfBandData-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-setScanMode-(I I)Z": ["android.permission.BLUETOOTH", "android.permission.WRITE_SECURE_SETTINGS"], "Landroid/server/BluetoothService;-setTrust-(Ljava/lang/String; Z)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-startDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/server/BluetoothService;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/email/provider/AttachmentProvider;-openFile-(Landroid/net/Uri; Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["com.android.email.permission.ACCESS_PROVIDER"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManager;-getAdnRecordsInEf-(I)Ljava/util/List;": ["android.permission.READ_CONTACTS", "android.permission.READ_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManager;-updateAdnRecordsInEfByIndex-(I Ljava/lang/String; Ljava/lang/String; I Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS", "android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManager;-updateAdnRecordsInEfBySearch-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS", "android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManagerProxy;-getAdnRecordsInEf-(I)Ljava/util/List;": ["android.permission.READ_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManagerProxy;-updateAdnRecordsInEfByIndex-(I Ljava/lang/String; Ljava/lang/String; I Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManagerProxy;-updateAdnRecordsInEfBySearch-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/IccSmsInterfaceManager;-sendData-(Ljava/lang/String; Ljava/lang/String; I [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/IccSmsInterfaceManager;-sendMultipartText-(Ljava/lang/String; Ljava/lang/String; Ljava/util/List; Ljava/util/List; Ljava/util/List;)V": ["android.permission.SEND_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/IccSmsInterfaceManager;-sendText-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/IccSmsInterfaceManagerProxy;-sendData-(Ljava/lang/String; Ljava/lang/String; I [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Lcom/android/internal/telephony/IccSmsInterfaceManagerProxy;-sendMultipartText-(Ljava/lang/String; Ljava/lang/String; Ljava/util/List; Ljava/util/List; Ljava/util/List;)V": ["android.permission.SEND_SMS"], "Lcom/android/internal/telephony/IccSmsInterfaceManagerProxy;-sendText-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Lcom/android/internal/telephony/PhoneSubInfo;-getCompleteVoiceMailNumber-()Ljava/lang/String;": ["android.permission.CALL_PRIVILEGED"], "Lcom/android/internal/telephony/PhoneSubInfo;-getDeviceId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getDeviceSvn-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIccSerialNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIsimDomain-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIsimImpi-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIsimImpu-()[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getLine1AlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getLine1Number-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getMsisdn-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getSubscriberId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getVoiceMailAlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getVoiceMailNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/cdma/RuimSmsInterfaceManager;-copyMessageToIccEf-(I [B [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/cdma/RuimSmsInterfaceManager;-getAllMessagesFromIccEf-()Ljava/util/List;": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/cdma/RuimSmsInterfaceManager;-updateMessageOnIccEf-(I I [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/gsm/SimSmsInterfaceManager;-copyMessageToIccEf-(I [B [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/gsm/SimSmsInterfaceManager;-disableCellBroadcast-(I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/gsm/SimSmsInterfaceManager;-disableCellBroadcastRange-(I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/gsm/SimSmsInterfaceManager;-enableCellBroadcast-(I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/gsm/SimSmsInterfaceManager;-enableCellBroadcastRange-(I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/gsm/SimSmsInterfaceManager;-getAllMessagesFromIccEf-()Ljava/util/List;": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/gsm/SimSmsInterfaceManager;-updateMessageOnIccEf-(I I [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-authenticate-(Ljava/lang/String; [B)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-close-(Ljava/lang/String; Landroid/os/IBinder;)Landroid/os/Bundle;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-getCardEmulationRoute-(Ljava/lang/String;)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-open-(Ljava/lang/String; Landroid/os/IBinder;)Landroid/os/Bundle;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-setCardEmulationRoute-(Ljava/lang/String; I)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-transceive-(Ljava/lang/String; [B)Landroid/os/Bundle;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-disable-(Z)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-disableNdefPush-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-dispatch-(Landroid/nfc/Tag;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-enable-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-enableNdefPush-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-getNfcAdapterExtrasInterface-(Ljava/lang/String;)Landroid/nfc/INfcAdapterExtras;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setForegroundDispatch-(Landroid/app/PendingIntent; [Landroid/content/IntentFilter; Landroid/nfc/TechListParcel;)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setNdefPushCallback-(Landroid/nfc/INdefPushCallback;)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setP2pModes-(I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$TagService;-close-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-connect-(I I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-formatNdef-(I [B)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-getTechList-(I)[I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-getTimeout-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-isNdef-(I)Z": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefMakeReadOnly-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefRead-(I)Landroid/nfc/NdefMessage;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefWrite-(I Landroid/nfc/NdefMessage;)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-reconnect-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-rediscover-(I)Landroid/nfc/Tag;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-resetTimeouts-()V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-setTimeout-(I I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-transceive-(I [B Z)Landroid/nfc/TransceiveResult;": ["android.permission.NFC"], "Lcom/android/phone/PhoneInterfaceManager;-answerRingingCall-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-call-(Ljava/lang/String;)V": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-cancelMissedCallsNotification-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableApnType-(Ljava/lang/String;)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableApnType-(Ljava/lang/String;)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-enableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-enableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-endCall-()Z": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-getAllCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-getCellLocation-()Landroid/os/Bundle;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-getNeighboringCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-handlePinMmi-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isSimPinEnabled-()Z": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadio-(Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-silenceRinger-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPin-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPuk-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-toggleRadioOnOff-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/providers/contacts/AbstractContactsProvider;-bulkInsert-(Landroid/net/Uri; [Landroid/content/ContentValues;)I": ["android.permission.WRITE_PROFILE"], "Lcom/android/providers/contacts/AbstractContactsProvider;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.WRITE_PROFILE"], "Lcom/android/providers/contacts/AbstractContactsProvider;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["android.permission.WRITE_PROFILE"], "Lcom/android/providers/contacts/AbstractContactsProvider;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.WRITE_PROFILE"], "Lcom/android/providers/contacts/CallLogProvider;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["com.android.voicemail.permission.ADD_VOICEMAIL"], "Lcom/android/providers/contacts/CallLogProvider;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["com.android.voicemail.permission.ADD_VOICEMAIL"], "Lcom/android/providers/contacts/CallLogProvider;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["com.android.voicemail.permission.ADD_VOICEMAIL"], "Lcom/android/providers/contacts/ContactsProvider2;-bulkInsert-(Landroid/net/Uri; [Landroid/content/ContentValues;)I": ["android.permission.READ_SOCIAL_STREAM"], "Lcom/android/providers/contacts/ContactsProvider2;-call-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Landroid/os/Bundle;": ["android.permission.READ_SOCIAL_STREAM"], "Lcom/android/providers/contacts/ContactsProvider2;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.READ_SOCIAL_STREAM", "android.permission.WRITE_SOCIAL_STREAM"], "Lcom/android/providers/contacts/ContactsProvider2;-getType-(Landroid/net/Uri;)Ljava/lang/String;": ["android.permission.READ_SOCIAL_STREAM"], "Lcom/android/providers/contacts/ContactsProvider2;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["android.permission.READ_SOCIAL_STREAM", "android.permission.WRITE_SOCIAL_STREAM"], "Lcom/android/providers/contacts/ContactsProvider2;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.READ_SOCIAL_STREAM", "android.permission.WRITE_SOCIAL_STREAM"], "Lcom/android/providers/contacts/ProfileProvider;-openAssetFile-(Landroid/net/Uri; Ljava/lang/String;)Landroid/content/res/AssetFileDescriptor;": ["android.permission.READ_PROFILE", "android.permission.WRITE_PROFILE"], "Lcom/android/providers/contacts/VoicemailContentProvider;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["com.android.voicemail.permission.ADD_VOICEMAIL"], "Lcom/android/providers/contacts/VoicemailContentProvider;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["com.android.voicemail.permission.ADD_VOICEMAIL"], "Lcom/android/providers/contacts/VoicemailContentProvider;-openFile-(Landroid/net/Uri; Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["com.android.voicemail.permission.ADD_VOICEMAIL"], "Lcom/android/providers/contacts/VoicemailContentProvider;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["com.android.voicemail.permission.ADD_VOICEMAIL"], "Lcom/android/providers/downloads/DownloadProvider;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.ACCESS_ALL_DOWNLOADS"], "Lcom/android/providers/downloads/DownloadProvider;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["android.permission.ACCESS_CACHE_FILESYSTEM", "android.permission.ACCESS_DOWNLOAD_MANAGER", "android.permission.ACCESS_DOWNLOAD_MANAGER_ADVANCED", "android.permission.DOWNLOAD_CACHE_NON_PURGEABLE", "android.permission.DOWNLOAD_WITHOUT_NOTIFICATION", "android.permission.INTERNET", "android.permission.WRITE_EXTERNAL_STORAGE"], "Lcom/android/providers/downloads/DownloadProvider;-openFile-(Landroid/net/Uri; Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.ACCESS_ALL_DOWNLOADS"], "Lcom/android/providers/downloads/DownloadProvider;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.ACCESS_ALL_DOWNLOADS"], "Lcom/android/providers/drm/DrmProvider;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.ACCESS_DRM"], "Lcom/android/providers/drm/DrmProvider;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["android.permission.INSTALL_DRM"], "Lcom/android/providers/drm/DrmProvider;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.ACCESS_DRM"], "Lcom/android/providers/media/MediaProvider;-openFile-(Landroid/net/Uri; Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.ACCESS_CACHE_FILESYSTEM", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.WRITE_EXTERNAL_STORAGE"], "Lcom/android/providers/settings/SettingsProvider;-bulkInsert-(Landroid/net/Uri; [Landroid/content/ContentValues;)I": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/providers/settings/SettingsProvider;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/providers/settings/SettingsProvider;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/providers/settings/SettingsProvider;-openAssetFile-(Landroid/net/Uri; Ljava/lang/String;)Landroid/content/res/AssetFileDescriptor;": ["android.permission.ACCESS_DRM"], "Lcom/android/providers/settings/SettingsProvider;-openFile-(Landroid/net/Uri; Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.ACCESS_DRM"], "Lcom/android/providers/settings/SettingsProvider;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/providers/telephony/TelephonyProvider;-delete-(Landroid/net/Uri; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.WRITE_APN_SETTINGS"], "Lcom/android/providers/telephony/TelephonyProvider;-insert-(Landroid/net/Uri; Landroid/content/ContentValues;)Landroid/net/Uri;": ["android.permission.WRITE_APN_SETTINGS"], "Lcom/android/providers/telephony/TelephonyProvider;-update-(Landroid/net/Uri; Landroid/content/ContentValues; Ljava/lang/String; [Ljava/lang/String;)I": ["android.permission.WRITE_APN_SETTINGS"], "Lcom/android/server/AlarmManagerService;-setTime-(J)V": ["android.permission.SET_TIME"], "Lcom/android/server/AlarmManagerService;-setTimeZone-(Ljava/lang/String;)V": ["android.permission.SET_TIME_ZONE"], "Lcom/android/server/AppWidgetService;-bindAppWidgetId-(I Landroid/content/ComponentName;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-bindAppWidgetIdIfAllowed-(Ljava/lang/String; I Landroid/content/ComponentName;)Z": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-bindRemoteViewsService-(I Landroid/content/Intent; Landroid/os/IBinder;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-deleteAppWidgetId-(I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-getAppWidgetInfo-(I)Landroid/appwidget/AppWidgetProviderInfo;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-getAppWidgetOptions-(I)Landroid/os/Bundle;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-getAppWidgetViews-(I)Landroid/widget/RemoteViews;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-hasBindAppWidgetPermission-(Ljava/lang/String;)Z": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/AppWidgetService;-notifyAppWidgetViewDataChanged-([I I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-partiallyUpdateAppWidgetIds-([I Landroid/widget/RemoteViews;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-setBindAppWidgetPermission-(Ljava/lang/String; Z)V": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/AppWidgetService;-unbindRemoteViewsService-(I Landroid/content/Intent;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-updateAppWidgetIds-([I Landroid/widget/RemoteViews;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-updateAppWidgetOptions-(I Landroid/os/Bundle;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-updateAppWidgetProvider-(Landroid/content/ComponentName; Landroid/widget/RemoteViews;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-getAvailableRestoreSets-(Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-restoreAll-(J Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-restorePackage-(Ljava/lang/String; Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-restoreSome-(J Landroid/app/backup/IRestoreObserver; [Ljava/lang/String;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-acknowledgeFullBackupOrRestore-(I Z Ljava/lang/String; Ljava/lang/String; Landroid/app/backup/IFullBackupRestoreObserver;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-backupNow-()V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-beginRestoreSession-(Ljava/lang/String; Ljava/lang/String;)Landroid/app/backup/IRestoreSession;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-clearBackupData-(Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-dataChanged-(Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-fullBackup-(Landroid/os/ParcelFileDescriptor; Z Z Z Z [Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-fullRestore-(Landroid/os/ParcelFileDescriptor;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-getConfigurationIntent-(Ljava/lang/String;)Landroid/content/Intent;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-getCurrentTransport-()Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-getDestinationString-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-hasBackupPassword-()Z": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-isBackupEnabled-()Z": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-listAllTransports-()[Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-selectBackupTransport-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setAutoRestore-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setBackupEnabled-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setBackupPassword-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setBackupProvisioned-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/ConnectivityService;-getActiveLinkProperties-()Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfoForUid-(I)Landroid/net/NetworkInfo;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getActiveNetworkQuotaInfo-()Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkState-()[Landroid/net/NetworkState;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLastTetherError-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLinkProperties-(I)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getMobileDataEnabled-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkPreference-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableBluetoothRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableUsbRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableWifiRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheredIfacePairs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheringErroredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isNetworkSupported-(I)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isTetheringSupported-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-reportInetCondition-(I I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/ConnectivityService;-requestNetworkTransitionWakelock-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-requestRouteToHost-(I I)Z": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-requestRouteToHostAddress-(I [B)Z": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setDataDependency-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setGlobalProxy-(Landroid/net/ProxyProperties;)V": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setMobileDataEnabled-(Z)V": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setNetworkPreference-(I)V": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setPolicyDataEnable-(I Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/ConnectivityService;-setRadio-(I Z)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setRadios-(Z)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setUsbTethering-(Z)I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-startUsingNetworkFeature-(I Ljava/lang/String; Landroid/os/IBinder;)I": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-stopUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-tether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-untether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/DevicePolicyManagerService;-getRemoveWarning-(Landroid/content/ComponentName; Landroid/os/RemoteCallback;)V": ["android.permission.BIND_DEVICE_ADMIN"], "Lcom/android/server/DevicePolicyManagerService;-removeActiveAdmin-(Landroid/content/ComponentName;)V": ["android.permission.BIND_DEVICE_ADMIN"], "Lcom/android/server/DevicePolicyManagerService;-reportFailedPasswordAttempt-()V": ["android.permission.BIND_DEVICE_ADMIN"], "Lcom/android/server/DevicePolicyManagerService;-reportSuccessfulPasswordAttempt-()V": ["android.permission.BIND_DEVICE_ADMIN"], "Lcom/android/server/DevicePolicyManagerService;-setActiveAdmin-(Landroid/content/ComponentName; Z)V": ["android.permission.BIND_DEVICE_ADMIN"], "Lcom/android/server/DevicePolicyManagerService;-setActivePasswordState-(I I I I I I I I)V": ["android.permission.BIND_DEVICE_ADMIN"], "Lcom/android/server/DropBoxManagerService;-getNextEntry-(Ljava/lang/String; J)Landroid/os/DropBoxManager$Entry;": ["android.permission.READ_LOGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethod-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodAndSubtype-(Landroid/os/IBinder; Ljava/lang/String; Landroid/view/inputmethod/InputMethodSubtype;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodEnabled-(Ljava/lang/String; Z)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-switchToLastInputMethod-(Landroid/os/IBinder;)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-switchToNextInputMethod-(Landroid/os/IBinder; Z)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/LocationManagerService;-addGpsStatusListener-(Landroid/location/IGpsStatusListener;)Z": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-addProximityAlert-(D D F J Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-addTestProvider-(Ljava/lang/String; Z Z Z Z Z Z Z I I)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderEnabled-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderLocation-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderStatus-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getLastKnownLocation-(Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviderInfo-(Ljava/lang/String;)Landroid/os/Bundle;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-isProviderEnabled-(Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-removeTestProvider-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-reportLocation-(Landroid/location/Location; Z)V": ["android.permission.INSTALL_LOCATION_PROVIDER"], "Lcom/android/server/LocationManagerService;-requestLocationUpdates-(Ljava/lang/String; Landroid/location/Criteria; J F Z Landroid/location/ILocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-requestLocationUpdatesPI-(Ljava/lang/String; Landroid/location/Criteria; J F Z Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Lcom/android/server/LocationManagerService;-setTestProviderEnabled-(Ljava/lang/String; Z)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-setTestProviderLocation-(Ljava/lang/String; Landroid/location/Location;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-setTestProviderStatus-(Ljava/lang/String; I Landroid/os/Bundle; J)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/MountService;-changeEncryptionPassword-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-createSecureContainer-(Ljava/lang/String; I Ljava/lang/String; Ljava/lang/String; I Z)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-decryptStorage-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-destroySecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_DESTROY"], "Lcom/android/server/MountService;-encryptStorage-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-finalizeSecureContainer-(Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-fixPermissionsSecureContainer-(Ljava/lang/String; I Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-formatVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-getEncryptionState-()I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-getSecureContainerFilesystemPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerList-()[Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getStorageUsers-(Ljava/lang/String;)[I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-isSecureContainerMounted-(Ljava/lang/String;)Z": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-mountSecureContainer-(Ljava/lang/String; Ljava/lang/String; I)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-mountVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-renameSecureContainer-(Ljava/lang/String; Ljava/lang/String;)I": ["android.permission.ASEC_RENAME"], "Lcom/android/server/MountService;-setUsbMassStorageEnabled-(Z)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-shutdown-(Landroid/os/storage/IMountShutdownObserver;)V": ["android.permission.SHUTDOWN"], "Lcom/android/server/MountService;-unmountSecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-unmountVolume-(Ljava/lang/String; Z Z)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-verifyEncryptionPassword-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/NetworkManagementService;-addRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addSecondaryRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-attachPppd-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearInterfaceAddresses-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-detachPppd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-flushDefaultDnsCache-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-flushInterfaceDnsCache-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getDnsForwarders-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getInterfaceConfig-(Ljava/lang/String;)Landroid/net/InterfaceConfiguration;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getInterfaceRxThrottle-(Ljava/lang/String;)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getInterfaceTxThrottle-(Ljava/lang/String;)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getIpForwardingEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsDetail-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryDev-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryXt-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsTethering-([Ljava/lang/String;)Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsUidDetail-(I)Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getRoutes-(Ljava/lang/String;)[Landroid/net/RouteInfo;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isBandwidthControlEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isTetheringStarted-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTetheredInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTtys-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-registerObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceAlert-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceQuota-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeSecondaryRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDefaultInterfaceForDns-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsForwarders-([Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsServersForInterface-(Ljava/lang/String; [Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setGlobalAlert-(J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceAlert-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceConfig-(Ljava/lang/String; Landroid/net/InterfaceConfiguration;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceDown-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceIpv6PrivacyExtensions-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceQuota-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceThrottle-(Ljava/lang/String; I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceUp-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setIpForwardingEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setUidNetworkRules-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-shutdown-()V": ["android.permission.SHUTDOWN"], "Lcom/android/server/NetworkManagementService;-startAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startTethering-([Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopAccessPoint-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopTethering-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-tetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-unregisterObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-untetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-wifiFirmwareReload-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NsdService;-getMessenger-()Landroid/os/Messenger;": ["android.permission.INTERNET"], "Lcom/android/server/NsdService;-setEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/PowerManagerService;-acquireWakeLock-(I Landroid/os/IBinder; Ljava/lang/String; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/PowerManagerService;-clearUserActivityTimeout-(J J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-crash-(Ljava/lang/String;)V": ["android.permission.REBOOT"], "Lcom/android/server/PowerManagerService;-goToSleep-(J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-goToSleepWithReason-(J I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-preventScreenOn-(Z)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-reboot-(Ljava/lang/String;)V": ["android.permission.REBOOT"], "Lcom/android/server/PowerManagerService;-releaseWakeLock-(Landroid/os/IBinder; I)V": ["android.permission.DEVICE_POWER", "android.permission.WAKE_LOCK"], "Lcom/android/server/PowerManagerService;-setAttentionLight-(Z I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-setAutoBrightnessAdjustment-(F)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-setBacklightBrightness-(I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-setMaximumScreenOffTimeount-(I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/PowerManagerService;-setPokeLock-(I Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-setStayOnSetting-(I)V": ["android.permission.WRITE_SETTINGS"], "Lcom/android/server/PowerManagerService;-updateWakeLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/PowerManagerService;-userActivity-(J Z)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/PowerManagerService;-userActivityWithForce-(J Z Z)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/SerialService;-getSerialPorts-()[Ljava/lang/String;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/SerialService;-openSerialPort-(Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/StatusBarManagerService;-collapse-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-disable-(I Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-expand-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-onClearAllNotifications-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onNotificationClear-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onNotificationClick-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onNotificationError-(Ljava/lang/String; Ljava/lang/String; I I I Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onPanelRevealed-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-registerStatusBar-(Lcom/android/internal/statusbar/IStatusBar; Lcom/android/internal/statusbar/StatusBarIconList; Ljava/util/List; Ljava/util/List; [I Ljava/util/List;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-removeIcon-(Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setIcon-(Ljava/lang/String; Ljava/lang/String; I I Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setIconVisibility-(Ljava/lang/String; Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setImeWindowStatus-(Landroid/os/IBinder; I I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setSystemUiVisibility-(I I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-topAppWindowChanged-(Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/TelephonyRegistry;-listen-(Ljava/lang/String; Lcom/android/internal/telephony/IPhoneStateListener; I Z)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallForwardingChanged-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallState-(I Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellInfo-(Landroid/telephony/CellInfo;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellLocation-(Landroid/os/Bundle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataActivity-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnection-(I Z Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/net/LinkProperties; Landroid/net/LinkCapabilities; I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionFailed-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyMessageWaitingChanged-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyOtaspChanged-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyServiceState-(Landroid/telephony/ServiceState;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifySignalStrength-(Landroid/telephony/SignalStrength;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellChecker-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellCheckerSubtype-(Ljava/lang/String; I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setSpellCheckerEnabled-(Z)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/ThrottleService;-getByteCount-(Ljava/lang/String; I I I)J": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ThrottleService;-getCliffLevel-(Ljava/lang/String; I)I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ThrottleService;-getCliffThreshold-(Ljava/lang/String; I)J": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ThrottleService;-getHelpUri-()Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ThrottleService;-getPeriodStartTime-(Ljava/lang/String;)J": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ThrottleService;-getResetTime-(Ljava/lang/String;)J": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ThrottleService;-getThrottle-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/UpdateLockService;-acquireUpdateLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/UpdateLockService;-releaseUpdateLock-(Landroid/os/IBinder;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/VibratorService;-cancelVibrate-(Landroid/os/IBinder;)V": ["android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibrate-(J Landroid/os/IBinder;)V": ["android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibratePattern-([J I Landroid/os/IBinder;)V": ["android.permission.VIBRATE"], "Lcom/android/server/WallpaperManagerService;-setDimensionHints-(I I)V": ["android.permission.SET_WALLPAPER_HINTS"], "Lcom/android/server/WallpaperManagerService;-setWallpaper-(Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.SET_WALLPAPER"], "Lcom/android/server/WallpaperManagerService;-setWallpaperComponent-(Landroid/content/ComponentName;)V": ["android.permission.SET_WALLPAPER_COMPONENT"], "Lcom/android/server/WifiService;-acquireMulticastLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/WifiService;-acquireWifiLock-(Landroid/os/IBinder; I Ljava/lang/String; Landroid/os/WorkSource;)Z": ["android.permission.WAKE_LOCK"], "Lcom/android/server/WifiService;-addOrUpdateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-addToBlacklist-(Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-clearBlacklist-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-disconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-getConfigFile-()Ljava/lang/String;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getFrequencyBand-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getScanResults-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getWifiApConfiguration-()Landroid/net/wifi/WifiConfiguration;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getWifiApEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getWifiEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-getWifiServiceMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-getWifiStateMachineMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-initializeMulticastFiltering-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/WifiService;-isMulticastEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/WifiService;-reassociate-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-reconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-releaseMulticastLock-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/WifiService;-releaseWifiLock-(Landroid/os/IBinder;)Z": ["android.permission.WAKE_LOCK"], "Lcom/android/server/WifiService;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-setCountryCode-(Ljava/lang/String; Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-setFrequencyBand-(I Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-setWifiApConfiguration-(Landroid/net/wifi/WifiConfiguration;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-setWifiApEnabled-(Landroid/net/wifi/WifiConfiguration; Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-setWifiEnabled-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-startScan-(Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-startWifi-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-stopWifi-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/WifiService;-updateWifiLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-activitySlept-(Landroid/os/IBinder;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-activityStopped-(Landroid/os/IBinder; Landroid/os/Bundle; Landroid/graphics/Bitmap; Ljava/lang/CharSequence;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-bindBackupAgent-(Landroid/content/pm/ApplicationInfo; I)Z": ["android.permission.BACKUP", "android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-bindService-(Landroid/app/IApplicationThread; Landroid/os/IBinder; Landroid/content/Intent; Ljava/lang/String; Landroid/app/IServiceConnection; I I)I": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-crashApplication-(I I Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-dismissKeyguardOnNextActivity-()V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-dumpHeap-(Ljava/lang/String; Z Ljava/lang/String; Landroid/os/ParcelFileDescriptor;)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-finishHeavyWeightApp-()V": ["android.permission.BROADCAST_STICKY", "android.permission.FORCE_STOP_PACKAGES", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-finishReceiver-(Landroid/os/IBinder; I Ljava/lang/String; Landroid/os/Bundle; Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-forceStopPackage-(Ljava/lang/String;)V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-getContentProviderExternal-(Ljava/lang/String; Landroid/os/IBinder;)Landroid/app/IActivityManager$ContentProviderHolder;": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY"], "Lcom/android/server/am/ActivityManagerService;-getRecentTasks-(I I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-getTaskThumbnails-(I)Landroid/app/ActivityManager$TaskThumbnails;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/am/ActivityManagerService;-getTasks-(I I Landroid/app/IThumbnailReceiver;)Ljava/util/List;": ["android.permission.GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-goingToSleep-()V": ["android.permission.BROADCAST_STICKY", "android.permission.DEVICE_POWER", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-handleApplicationCrash-(Landroid/os/IBinder; Landroid/app/ApplicationErrorReport$CrashInfo;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-handleApplicationWtf-(Landroid/os/IBinder; Ljava/lang/String; Landroid/app/ApplicationErrorReport$CrashInfo;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-killAllBackgroundProcesses-()V": ["android.permission.BROADCAST_STICKY", "android.permission.KILL_BACKGROUND_PROCESSES", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-killBackgroundProcesses-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-moveActivityTaskToBack-(Landroid/os/IBinder; Z)Z": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-moveTaskBackwards-(I)V": ["android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToBack-(I)V": ["android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-navigateUpTo-(Landroid/os/IBinder; Landroid/content/Intent; I Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-profileControl-(Ljava/lang/String; Z Ljava/lang/String; Landroid/os/ParcelFileDescriptor; I)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-registerProcessObserver-(Landroid/app/IProcessObserver;)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-removeContentProviderExternal-(Ljava/lang/String; Landroid/os/IBinder;)V": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY", "android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-removeSubTask-(I I)Z": ["android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-removeTask-(I I)Z": ["android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-resumeAppSwitches-()V": ["android.permission.STOP_APP_SWITCHES"], "Lcom/android/server/am/ActivityManagerService;-setActivityController-(Landroid/app/IActivityController;)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-setAlwaysFinish-(Z)V": ["android.permission.SET_ALWAYS_FINISH"], "Lcom/android/server/am/ActivityManagerService;-setDebugApp-(Ljava/lang/String; Z Z)V": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-setFrontActivityScreenCompatMode-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.SET_SCREEN_COMPATIBILITY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-setLockScreenShown-(Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.DEVICE_POWER", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-setPackageAskScreenCompat-(Ljava/lang/String; Z)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setPackageScreenCompatMode-(Ljava/lang/String; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.SET_SCREEN_COMPATIBILITY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-setProcessForeground-(Landroid/os/IBinder; I Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.SET_PROCESS_LIMIT", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-setProcessLimit-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.SET_PROCESS_LIMIT", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-setRequestedOrientation-(Landroid/os/IBinder; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-shutdown-(I)Z": ["android.permission.BROADCAST_STICKY", "android.permission.SHUTDOWN", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-signalPersistentProcesses-(I)V": ["android.permission.SIGNAL_PERSISTENT_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-startActivities-(Landroid/app/IApplicationThread; [Landroid/content/Intent; [Ljava/lang/String; Landroid/os/IBinder; Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivitiesInPackage-(I [Landroid/content/Intent; [Ljava/lang/String; Landroid/os/IBinder; Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivity-(Landroid/app/IApplicationThread; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Ljava/lang/String; Landroid/os/ParcelFileDescriptor; Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityAndWait-(Landroid/app/IApplicationThread; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Ljava/lang/String; Landroid/os/ParcelFileDescriptor; Landroid/os/Bundle;)Landroid/app/IActivityManager$WaitResult;": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityInPackage-(I Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityWithConfig-(Landroid/app/IApplicationThread; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/content/res/Configuration; Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startRunning-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-stopAppSwitches-()V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY", "android.permission.STOP_APP_SWITCHES"], "Lcom/android/server/am/ActivityManagerService;-switchUser-(I)Z": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-unbroadcastIntent-(Landroid/app/IApplicationThread; Landroid/content/Intent; I)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-unhandledBack-()V": ["android.permission.FORCE_BACK"], "Lcom/android/server/am/ActivityManagerService;-unregisterReceiver-(Landroid/content/IIntentReceiver;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-updateConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.CHANGE_CONFIGURATION"], "Lcom/android/server/am/ActivityManagerService;-updatePersistentConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.CHANGE_CONFIGURATION"], "Lcom/android/server/am/ActivityManagerService;-wakingUp-()V": ["android.permission.BROADCAST_STICKY", "android.permission.DEVICE_POWER", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimeBattery-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimePlugged-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getStatistics-()[B": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquired-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquiredFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleased-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleasedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteInputEvent-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteNetworkInterfaceType-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneDataConnectionState-(I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneSignalStrength-(Landroid/telephony/SignalStrength;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneState-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScanWifiLockAcquired-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScanWifiLockAcquiredFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScanWifiLockReleased-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScanWifiLockReleasedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenBrightness-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelock-(I I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelock-(I I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteUserActivity-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunning-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunningChanged-(Landroid/os/WorkSource; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiStopped-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-setBatteryState-(I I I I I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/UsageStatsService;-getAllPkgUsageStats-()[Lcom/android/internal/os/PkgUsageStats;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/UsageStatsService;-getPkgUsageStats-(Landroid/content/ComponentName;)Lcom/android/internal/os/PkgUsageStats;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/UsageStatsService;-noteLaunchTime-(Landroid/content/ComponentName; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/UsageStatsService;-notePauseComponent-(Landroid/content/ComponentName;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/UsageStatsService;-noteResumeComponent-(Landroid/content/ComponentName;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/input/InputManagerService;-addKeyboardLayoutForInputDevice-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-removeKeyboardLayoutForInputDevice-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-setCurrentKeyboardLayoutForInputDevice-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-tryPointerSpeed-(I)V": ["android.permission.SET_POINTER_SPEED"], "Lcom/android/server/net/NetworkPolicyManagerService;-getAppPolicy-(I)I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getAppsWithPolicy-(I)[I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkPolicies-()[Landroid/net/NetworkPolicy;": ["android.permission.MANAGE_NETWORK_POLICY", "android.permission.READ_PHONE_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkQuotaInfo-(Landroid/net/NetworkState;)Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getRestrictBackground-()Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-isUidForeground-(I)Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-registerListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkPolicyManagerService;-setAppPolicy-(I I)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setNetworkPolicies-([Landroid/net/NetworkPolicy;)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setRestrictBackground-(Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-snoozeLimit-(Landroid/net/NetworkTemplate;)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-unregisterListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkStatsService;-advisePersistThreshold-(J)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-forceUpdate-()V": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-getDataLayerSnapshotForUid-(I)Landroid/net/NetworkStats;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkStatsService;-getNetworkTotalBytes-(Landroid/net/NetworkTemplate; J J)J": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-incrementOperationCount-(I I I)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-openSession-()Landroid/net/INetworkStatsSession;": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-setUidForeground-(I Z)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/pm/PackageManagerService;-addPreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName;)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-clearApplicationUserData-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver; I)V": ["android.permission.CLEAR_APP_USER_DATA"], "Lcom/android/server/pm/PackageManagerService;-clearPackagePreferredActivities-(Ljava/lang/String;)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-deleteApplicationCacheFiles-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.DELETE_CACHE_FILES"], "Lcom/android/server/pm/PackageManagerService;-deletePackage-(Ljava/lang/String; Landroid/content/pm/IPackageDeleteObserver; I)V": ["android.permission.DELETE_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-freeStorage-(J Landroid/content/IntentSender;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-freeStorageAndNotify-(J Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-getPackageSizeInfo-(Ljava/lang/String; Landroid/content/pm/IPackageStatsObserver;)V": ["android.permission.GET_PACKAGE_SIZE"], "Lcom/android/server/pm/PackageManagerService;-getVerifierDeviceIdentity-()Landroid/content/pm/VerifierDeviceIdentity;": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/pm/PackageManagerService;-grantPermission-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-installPackage-(Landroid/net/Uri; Landroid/content/pm/IPackageInstallObserver; I Ljava/lang/String;)V": ["android.permission.INSTALL_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-installPackageWithVerification-(Landroid/net/Uri; Landroid/content/pm/IPackageInstallObserver; I Ljava/lang/String; Landroid/net/Uri; Landroid/content/pm/ManifestDigest; Landroid/content/pm/ContainerEncryptionParams;)V": ["android.permission.INSTALL_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-movePackage-(Ljava/lang/String; Landroid/content/pm/IPackageMoveObserver; I)V": ["android.permission.MOVE_PACKAGE"], "Lcom/android/server/pm/PackageManagerService;-replacePreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName;)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-revokePermission-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-setApplicationEnabledSetting-(Ljava/lang/String; I I I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE"], "Lcom/android/server/pm/PackageManagerService;-setComponentEnabledSetting-(Landroid/content/ComponentName; I I I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE"], "Lcom/android/server/pm/PackageManagerService;-setInstallLocation-(I)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/pm/PackageManagerService;-setPackageStoppedState-(Ljava/lang/String; Z I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE"], "Lcom/android/server/pm/PackageManagerService;-setPermissionEnforced-(Ljava/lang/String; Z)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/sip/SipService;-close-(Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-createSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/ISipSessionListener;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getListOfProfiles-()[Landroid/net/sip/SipProfile;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getPendingSession-(Ljava/lang/String;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isOpened-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isRegistered-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open-(Landroid/net/sip/SipProfile;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open3-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/ISipSessionListener;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/ISipSessionListener;)V": ["android.permission.USE_SIP"], "Lcom/android/server/usb/UsbService;-clearDefaults-(Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantAccessoryPermission-(Landroid/hardware/usb/UsbAccessory; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantDevicePermission-(Landroid/hardware/usb/UsbDevice; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-hasDefaults-(Ljava/lang/String;)Z": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setAccessoryPackage-(Landroid/hardware/usb/UsbAccessory; Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setCurrentFunction-(Ljava/lang/String; Z)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setDevicePackage-(Landroid/hardware/usb/UsbDevice; Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setMassStorageBackingFile-(Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/wm/WindowManagerService;-addAppToken-(I Landroid/view/IApplicationToken; I I Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-addWindowToken-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-disableKeyguard-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-dismissKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-executeAppTransition-()V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-exitKeyguardSecurely-(Landroid/view/IOnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-freezeRotation-(I)V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-isViewServerRunning-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-moveAppToken-(I Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-moveAppTokensToBottom-(Ljava/util/List;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-moveAppTokensToTop-(Ljava/util/List;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-pauseKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-prepareAppTransition-(I Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-reenableKeyguard-(Landroid/os/IBinder;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-removeAppToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-removeWindowToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-resumeKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-screenshotApplications-(Landroid/os/IBinder; I I)Landroid/graphics/Bitmap;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScale-(I F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScales-([F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAppGroupId-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppOrientation-(Landroid/view/IApplicationToken; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppStartingWindow-(Landroid/os/IBinder; Ljava/lang/String; I Landroid/content/res/CompatibilityInfo; Ljava/lang/CharSequence; I I I Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppVisibility-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppWillBeHidden-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setEventDispatching-(Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setFocusedApp-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setNewConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-startAppFreezingScreen-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-startViewServer-(I)Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-statusBarVisibilityChanged-(I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/wm/WindowManagerService;-stopAppFreezingScreen-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-stopViewServer-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-thawRotation-()V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-updateOrientationFromAppTokens-(Landroid/content/res/Configuration; Landroid/os/IBinder;)Landroid/content/res/Configuration;": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/ti/server/StubFmService;-resumeFm-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxChangeAudioTarget-(I I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxChangeDigitalTargetConfiguration-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxCompleteScan_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxDisable-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxDisableAudioRouting-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxDisableRds-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxDisableRds_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxEnable-()Z": ["ti.permission.FMRX", "ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxEnableAudioRouting-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxEnableRds-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxEnableRds_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetBand-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetBand_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetChannelSpacing-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetChannelSpacing_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetCompleteScanProgress-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetCompleteScanProgress_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetDeEmphasisFilter-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetDeEmphasisFilter_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetFMState-()I": ["ti.permission.FMRX"], "Lcom/ti/server/StubFmService;-rxGetFwVersion-()D": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetMonoStereoMode-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetMonoStereoMode_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetMuteMode-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetMuteMode_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRdsAfSwitchMode-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRdsAfSwitchMode_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRdsGroupMask-()J": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRdsGroupMask_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRdsSystem-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRdsSystem_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRfDependentMuteMode-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRfDependentMuteMode_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRssi-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRssiThreshold-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRssiThreshold_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetRssi_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetTunedFrequency-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetTunedFrequency_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetVolume-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxGetVolume_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxIsEnabled-()Z": ["ti.permission.FMRX"], "Lcom/ti/server/StubFmService;-rxIsFMPaused-()Z": ["ti.permission.FMRX"], "Lcom/ti/server/StubFmService;-rxIsValidChannel-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSeek_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetBand-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetBand_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetChannelSpacing-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetChannelSpacing_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetDeEmphasisFilter-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetDeEmphasisFilter_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetMonoStereoMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetMonoStereoMode_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetMuteMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetMuteMode_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRdsAfSwitchMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRdsAfSwitchMode_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRdsGroupMask-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRdsGroupMask_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRdsSystem-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRdsSystem_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRfDependentMuteMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRfDependentMuteMode_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRssiThreshold-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetRssiThreshold_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxSetVolume-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxStopCompleteScan-()I": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxStopCompleteScan_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxStopSeek-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxStopSeek_nb-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-rxTune_nb-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txChangeAudioSource-(I I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txChangeDigitalSourceConfiguration-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txDisable-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txDisableRds-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txEnable-()Z": ["ti.permission.FMRX", "ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txEnableRds-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txGetFMState-()I": ["ti.permission.FMRX"], "Lcom/ti/server/StubFmService;-txSetMonoStereoMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetMuteMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetPowerLevel-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetPreEmphasisFilter-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsAfCode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsECC-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsMusicSpeechFlag-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsPiCode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsPsDisplayMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsPsScrollSpeed-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsPtyCode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsTextPsMsg-(Ljava/lang/String;)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsTextRepertoire-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsTextRtMsg-(I Ljava/lang/String; I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsTrafficCodes-(I I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsTransmissionMode-(I)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txSetRdsTransmittedGroupsMask-(J)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txStartTransmission-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txStopTransmission-()Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txTune-(J)Z": ["ti.permission.FMRX_ADMIN"], "Lcom/ti/server/StubFmService;-txWriteRdsRawData-(Ljava/lang/String;)Z": ["ti.permission.FMRX_ADMIN"], "Landroid/accounts/AccountAuthenticatorActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountManager;-addAccountExplicitly-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-addOnAccountsUpdatedListener-(Landroid/accounts/OnAccountsUpdateListener; Landroid/os/Handler; Z)V": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-clearPassword-(Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManager;-getAccounts-()[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-getAccountsByType-(Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-getPassword-(Landroid/accounts/Account;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-getUserData-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-invalidateAuthToken-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MANAGE_ACCOUNTS", "android.permission.USE_CREDENTIALS"], "Landroid/accounts/AccountManager;-peekAuthToken-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-removeOnAccountsUpdatedListener-(Landroid/accounts/OnAccountsUpdateListener;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountManager;-setAuthToken-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setPassword-(Landroid/accounts/Account; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setUserData-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/app/Activity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-moveTaskToBack-(Z)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-navigateUpTo-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-navigateUpToFromChild-(Landroid/app/Activity; Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-onMenuItemSelected-(I Landroid/view/MenuItem;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-onNavigateUp-()Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-onNavigateUpFromChild-(Landroid/app/Activity;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-setRequestedOrientation-(I)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityManager;-getRecentTasks-(I I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-getRunningTasks-(I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-killBackgroundProcesses-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-restartPackage-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/AlarmManager;-setTimeZone-(Ljava/lang/String;)V": ["android.permission.SET_TIME_ZONE"], "Landroid/app/AliasActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/KeyguardManager$KeyguardLock;-disableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager$KeyguardLock;-reenableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager;-exitKeyguardSecurely-(Landroid/app/KeyguardManager$OnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/ListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/WallpaperManager;-clear-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setBitmap-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setResource-(I)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setStream-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-suggestDesiredDimensions-(I I)V": ["android.permission.SET_WALLPAPER_HINTS"], "Landroid/app/backup/BackupAgentHelper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/bluetooth/BluetoothA2dp;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-disable-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-enable-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBondedDevices-()Ljava/util/Set;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingInsecureRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-startDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-fetchUuidsWithSdp-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBluetoothClass-()Landroid/bluetooth/BluetoothClass;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBondState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-isAudioConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHealth;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHealth;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-registerSinkAppConfiguration-(Ljava/lang/String; I Landroid/bluetooth/BluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothSocket;-connect-()V": ["android.permission.BLUETOOTH"], "Landroid/content/BroadcastReceiver$PendingResult;-finish-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/location/LocationManager;-addGpsStatusListener-(Landroid/location/GpsStatus$Listener;)Z": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addNmeaListener-(Landroid/location/GpsStatus$NmeaListener;)Z": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addProximityAlert-(D D F J Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addTestProvider-(Ljava/lang/String; Z Z Z Z Z Z Z I I)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderEnabled-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderLocation-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderStatus-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getLastKnownLocation-(Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProvider-(Ljava/lang/String;)Landroid/location/LocationProvider;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-isProviderEnabled-(Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeTestProvider-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Landroid/location/LocationManager;-setTestProviderEnabled-(Ljava/lang/String; Z)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-setTestProviderLocation-(Ljava/lang/String; Landroid/location/Location;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-setTestProviderStatus-(Ljava/lang/String; I Landroid/os/Bundle; J)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/media/AsyncPlayer;-play-(Landroid/content/Context; Landroid/net/Uri; Z I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/AsyncPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/AudioManager;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setMode-(I)V": ["android.permission.BLUETOOTH", "android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-startBluetoothSco-()V": ["android.permission.BLUETOOTH", "android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-stopBluetoothSco-()V": ["android.permission.BLUETOOTH", "android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/MediaPlayer;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-reset-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-setWakeMode-(Landroid/content/Context; I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-play-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-setStreamType-(I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(Landroid/content/Context; Landroid/net/Uri;)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(I)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-stopPreviousRingtone-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/ConnectivityManager;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkPreference-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-requestRouteToHost-(I I)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-setNetworkPreference-(I)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-startUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-stopUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/VpnService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/sip/SipAudioCall;-close-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-endCall-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-setSpeakerMode-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/net/sip/SipAudioCall;-startAudio-()V": ["android.permission.ACCESS_WIFI_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/sip/SipManager;-close-(Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-createSipSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipSession$Listener;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-getSessionFor-(Landroid/content/Intent;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isOpened-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isRegistered-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipProfile; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Ljava/lang/String; Ljava/lang/String; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-register-(Landroid/net/sip/SipProfile; I Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-takeAudioCall-(Landroid/content/Intent; Landroid/net/sip/SipAudioCall$Listener;)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-unregister-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/wifi/WifiManager$MulticastLock;-acquire-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$MulticastLock;-release-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$WifiLock;-acquire-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager$WifiLock;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager;-addNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disconnect-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getScanResults-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getWifiState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isWifiEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reassociate-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reconnect-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-setWifiEnabled-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-startScan-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-updateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/p2p/WifiP2pManager;-initialize-(Landroid/content/Context; Landroid/os/Looper; Landroid/net/wifi/p2p/WifiP2pManager$ChannelListener;)Landroid/net/wifi/p2p/WifiP2pManager$Channel;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/nfc/NfcAdapter;-disableForegroundDispatch-(Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-disableForegroundNdefPush-(Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-enableForegroundDispatch-(Landroid/app/Activity; Landroid/app/PendingIntent; [Landroid/content/IntentFilter; [L[java/lang/String;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-enableForegroundNdefPush-(Landroid/app/Activity; Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setBeamPushUris-([Landroid/net/Uri; Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setBeamPushUrisCallback-(Landroid/nfc/NfcAdapter$CreateBeamUrisCallback; Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setNdefPushMessage-(Landroid/nfc/NdefMessage; Landroid/app/Activity; [Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setNdefPushMessageCallback-(Landroid/nfc/NfcAdapter$CreateNdefMessageCallback; Landroid/app/Activity; [Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setOnNdefPushCompleteCallback-(Landroid/nfc/NfcAdapter$OnNdefPushCompleteCallback; Landroid/app/Activity; [Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/tech/BasicTagTechnology;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/BasicTagTechnology;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-authenticateSectorWithKeyA-(I [B)Z": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-authenticateSectorWithKeyB-(I [B)Z": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-decrement-(I I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-increment-(I I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-readBlock-(I)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-restore-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-transfer-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-writeBlock-(I [B)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-readPages-(I)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-writePage-(I [B)V": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-getNdefMessage-()Landroid/nfc/NdefMessage;": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-makeReadOnly-()Z": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-writeNdefMessage-(Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-format-(Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-formatReadOnly-(Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/NfcB;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcB;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcB;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/NfcV;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcV;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcV;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/os/PowerManager$WakeLock;-acquire-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-acquire-(J)V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/SystemVibrator;-cancel-()V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-([J I)V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(J)V": ["android.permission.VIBRATE"], "Landroid/telephony/SmsManager;-sendDataMessage-(Ljava/lang/String; Ljava/lang/String; S [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendMultipartTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/util/ArrayList; Ljava/util/ArrayList; Ljava/util/ArrayList;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/TelephonyManager;-getCellLocation-()Landroid/telephony/CellLocation;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getDeviceId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getDeviceSoftwareVersion-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getLine1Number-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getNeighboringCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getSimSerialNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getSubscriberId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailAlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-listen-(Landroid/telephony/PhoneStateListener; I)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE"], "Landroid/telephony/gsm/SmsManager;-sendDataMessage-(Ljava/lang/String; Ljava/lang/String; S [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/gsm/SmsManager;-sendMultipartTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/util/ArrayList; Ljava/util/ArrayList; Ljava/util/ArrayList;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/gsm/SmsManager;-sendTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/test/IsolatedContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-unregisterReceiver-(Landroid/content/BroadcastReceiver;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/widget/VideoView;-onKeyDown-(I Landroid/view/KeyEvent;)Z": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-resume-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoPath-(Ljava/lang/String;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoURI-(Landroid/net/Uri;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-stopPlayback-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-suspend-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/ZoomButtonsController;-setVisible-(Z)V": ["android.permission.BROADCAST_STICKY"]}
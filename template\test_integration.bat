@echo off
REM Test script to verify O-MVLL integration
echo ========================================
echo AmpedGems Template - Integration Test
echo ========================================

echo.
echo Testing file structure...

REM Check if all required files exist
if not exist "omvll.yml" (
    echo ❌ ERROR: omvll.yml not found
    goto :error
) else (
    echo ✅ omvll.yml found
)

if not exist "app\src\main\jni\omvll_config.py" (
    echo ❌ ERROR: omvll_config.py not found
    goto :error
) else (
    echo ✅ omvll_config.py found
)

if not exist "app\src\main\jni\Android.mk" (
    echo ❌ ERROR: Android.mk not found
    goto :error
) else (
    echo ✅ Android.mk found
)

if not exist "build_with_omvll.bat" (
    echo ❌ ERROR: build_with_omvll.bat not found
    goto :error
) else (
    echo ✅ build_with_omvll.bat found
)

echo.
echo Testing Android.mk O-MVLL integration...

REM Check if Android.mk contains O-MVLL integration
findstr /C:"ENABLE_OMVLL" "app\src\main\jni\Android.mk" >nul
if %errorlevel% neq 0 (
    echo ❌ ERROR: Android.mk missing O-MVLL integration
    goto :error
) else (
    echo ✅ Android.mk has O-MVLL integration
)

echo.
echo Testing configuration...

REM Check if omvll_config.py has the required class
findstr /C:"class AmpedGemsConfig" "app\src\main\jni\omvll_config.py" >nul
if %errorlevel% neq 0 (
    echo ❌ ERROR: omvll_config.py missing AmpedGemsConfig class
    goto :error
) else (
    echo ✅ omvll_config.py has correct configuration class
)

echo.
echo ========================================
echo ✅ ALL TESTS PASSED!
echo ========================================
echo.
echo Your template is ready for O-MVLL integration!
echo.
echo Next steps:
echo 1. Download O-MVLL from: https://github.com/open-obfuscator/o-mvll/releases
echo 2. Set OMVLL_PATH environment variable
echo 3. Run: build_with_omvll.bat
echo.
goto :end

:error
echo.
echo ========================================
echo ❌ INTEGRATION TEST FAILED!
echo ========================================
echo Please check the missing files and try again.
echo.

:end
pause

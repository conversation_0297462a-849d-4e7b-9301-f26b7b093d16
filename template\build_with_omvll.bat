@echo off
setlocal enabledelayedexpansion
REM AmpedGems Template - O-MVLL Enhanced Build Script
REM This script builds the template with O-MVLL obfuscation

echo ========================================
echo AmpedGems Template - O-MVLL Build
echo ========================================

REM Auto-setup environment (ensures Java 21 and Gradle are in PATH)
set "SETUP_ENV=%~dp0..\setup_env.bat"
if exist "%SETUP_ENV%" (
    call "%SETUP_ENV%"

    REM Force the correct JAVA_HOME (override any system setting)
    set "JAVA_HOME=D:\space\AmpedGems\work_files\Java\jdk-21"
    set "GRADLE_HOME=D:\space\AmpedGems\work_files\Gradle\gradle-8.14.1"
    set "PATH=%JAVA_HOME%\bin;%GRADLE_HOME%\bin;%PATH%"
    echo Forced Java to: %JAVA_HOME%
    echo.
) else (
    echo ERROR: setup_env.bat not found. Please run _install.bat first.
    pause
    exit /b 1
)

REM Set O-MVLL paths relative to AmpedGems work_files
set "WORK_FILES_DIR=%~dp0..\work_files"
if "%OMVLL_PATH%"=="" set "OMVLL_PATH=%WORK_FILES_DIR%\OMVLL"
if "%OMVLL_PLUGIN_PATH%"=="" set "OMVLL_PLUGIN_PATH=%WORK_FILES_DIR%\OMVLL\omvll_ndk_r26d.so"

REM Check if O-MVLL is available
if not exist "%OMVLL_PLUGIN_PATH%" (
    echo ERROR: O-MVLL not found at %OMVLL_PLUGIN_PATH%
    echo Please ensure O-MVLL is in work_files\OMVLL\
    echo.
    echo Falling back to standard build...
    call gradlew.bat assembleDebug
    goto :end
)

echo O-MVLL found at: %OMVLL_PATH%
echo Enabling O-MVLL obfuscation...

REM Set O-MVLL environment variables (for NDK library path)
set "LD_LIBRARY_PATH=%ANDROID_HOME%\ndk\26.3.11579264\toolchains\llvm\prebuilt\windows-x86_64\lib64"

REM Build with O-MVLL
echo Building with O-MVLL obfuscation...

REM Change to template directory (where this script is located)
cd /d "%~dp0"

if exist "gradlew.bat" (
    call gradlew.bat assembleDebug
) else (
    echo ERROR: gradlew.bat not found in template directory
    pause
    exit /b 1
)

:end
echo.
echo Build completed!
pause

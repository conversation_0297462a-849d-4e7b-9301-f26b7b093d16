apply plugin: 'com.android.application'

android {
    namespace 'com.android.support'
    compileSdkVersion 35

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_17
        targetCompatibility JavaVersion.VERSION_17
    }
    defaultConfig {
        applicationId "com.android.support"
        minSdkVersion 21
        targetSdkVersion 35
        versionCode 1
        versionName "3.2"
        multiDexEnabled false
        ndk {
            abiFilters 'arm64-v8a'
        }
        ndkVersion "26.3.11579264"
        signingConfig signingConfigs.debug
    }
    buildTypes {
        release {
            shrinkResources false
            minifyEnabled false

            // O-MVLL Integration for release builds
            externalNativeBuild {
                ndkBuild {
                    cppFlags '-fpass-plugin=../work_files/OMVLL/omvll_ndk_r26d.so'
                    cFlags '-fpass-plugin=../work_files/OMVLL/omvll_ndk_r26d.so'
                }
            }
        }
        debug {
            shrinkResources false
            minifyEnabled false
            debuggable true
        }
    }
    externalNativeBuild {
        // Choose build system based on O-MVLL preference
        if (project.hasProperty('useOMVLLCMake') && project.useOMVLLCMake == 'true') {
            cmake {
                path file('src/main/jni/CMakeLists.txt')
                version "3.18.1"
            }
        } else {
            ndkBuild {
                path file('src/main/jni/Android.mk')
            }
        }
    }
}

//dependencies must be placed below 'android' brackets to get it work on AIDE
dependencies {
    // No external dependencies needed - template is self-contained
}

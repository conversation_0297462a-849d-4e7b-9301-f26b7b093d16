#!/usr/bin/env python3
"""
Test script for selective APK decompilation
Tests the new smali-only decompilation to avoid Facebook Ads DEX conflicts
"""

import os
import sys
import tempfile
import shutil
from dcc import ApkTool, Logger, show_logging
from logging import INF<PERSON>

def test_selective_decompilation(apk_path):
    """Test the selective decompilation methods"""
    
    print("🧪 Testing Selective APK Decompilation")
    print("=" * 50)
    
    if not os.path.exists(apk_path):
        print(f"❌ APK not found: {apk_path}")
        return False
    
    print(f"📱 Testing APK: {apk_path}")
    print()
    
    # Test 1: Original decompilation (may fail with Facebook Ads)
    print("🔍 Test 1: Original decompilation method")
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"   Decompiling to: {temp_dir}")
            decompiled_dir = ApkTool.decompile(apk_path)
            
            # Check what was decompiled
            contents = os.listdir(decompiled_dir)
            print(f"   ✅ Decompiled successfully")
            print(f"   📁 Contents: {contents}")
            
            # Check for problematic assets DEX
            assets_dir = os.path.join(decompiled_dir, "assets")
            if os.path.exists(assets_dir):
                for root, dirs, files in os.walk(assets_dir):
                    for file in files:
                        if file.endswith('.dex'):
                            print(f"   ⚠️  Found assets DEX: {os.path.join(root, file)}")
            
            # Clean up
            shutil.rmtree(decompiled_dir)
            
    except Exception as e:
        print(f"   ❌ Original decompilation failed: {str(e)}")
    
    print()
    
    # Test 2: Selective decompilation (should work)
    print("🔍 Test 2: Selective smali-only decompilation")
    try:
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"   Decompiling to: {temp_dir}")
            decompiled_dir = ApkTool.decompile_smali_only(apk_path)
            
            # Check what was decompiled
            contents = os.listdir(decompiled_dir)
            print(f"   ✅ Selective decompilation successful")
            print(f"   📁 Contents: {contents}")
            
            # Verify AndroidManifest.xml exists
            manifest_path = os.path.join(decompiled_dir, "AndroidManifest.xml")
            if os.path.exists(manifest_path):
                print(f"   ✅ AndroidManifest.xml found")
            else:
                print(f"   ❌ AndroidManifest.xml missing!")
            
            # Count smali folders
            smali_folders = [f for f in contents if f.startswith("smali")]
            print(f"   📂 Smali folders: {len(smali_folders)} - {smali_folders}")
            
            # Check for unwanted directories (should be removed)
            unwanted = ["assets", "res", "lib", "kotlin", "META-INF"]
            found_unwanted = [d for d in unwanted if d in contents]
            if found_unwanted:
                print(f"   ⚠️  Unwanted directories found: {found_unwanted}")
            else:
                print(f"   ✅ No unwanted directories found")
            
            # Clean up
            shutil.rmtree(decompiled_dir)
            
    except Exception as e:
        print(f"   ❌ Selective decompilation failed: {str(e)}")
        return False
    
    print()
    print("🎉 Selective decompilation test completed successfully!")
    print()
    print("💡 Benefits of selective decompilation:")
    print("   ✅ Avoids Facebook Ads DEX conflicts")
    print("   ✅ Faster decompilation (only smali + manifest)")
    print("   ✅ Smaller temporary directories")
    print("   ✅ Focuses on what protection system needs")
    print("   ✅ AndroidManifest.xml available for editing")
    
    return True

def main():
    """Main test function"""
    
    # Setup logging
    show_logging(level=INFO)
    
    # Test with the Apple Grapple APK that had Facebook Ads issues
    test_apk = "../original.apk"  # Adjust path as needed
    
    if len(sys.argv) > 1:
        test_apk = sys.argv[1]
    
    print("🔧 AmpedGems Protection System - Selective Decompilation Test")
    print("=" * 60)
    print()
    
    success = test_selective_decompilation(test_apk)
    
    if success:
        print()
        print("✅ All tests passed! Your protection system should now avoid Facebook Ads DEX conflicts.")
        print()
        print("🚀 To use selective decompilation in your protection:")
        print("   1. The config.json is already set to 'smali_only' mode")
        print("   2. Your protection will now use ApkTool.decompile_smali_only()")
        print("   3. This avoids assets DEX files that cause conflicts")
        print("   4. AndroidManifest.xml is still decompiled for editing")
    else:
        print()
        print("❌ Tests failed. Check the error messages above.")
    
    return 0 if success else 1

if __name__ == "__main__":
    sys.exit(main())

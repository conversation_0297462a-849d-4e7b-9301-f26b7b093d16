{"Landroid/accounts/AbstractAccountAuthenticator$Transport;-addAccount-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-addAccountFromCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-confirmCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-editProperties-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAccountCredentialsForCloning-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAccountRemovalAllowed-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAuthToken-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAuthTokenLabel-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-hasFeatures-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; [Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-updateCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/hardware/location/ActivityRecognitionHardware;-disableActivityEvent-(Ljava/lang/String; I)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-enableActivityEvent-(Ljava/lang/String; I J)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-flush-()Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-getSupportedActivities-()[Ljava/lang/String;": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-isActivitySupported-(Ljava/lang/String;)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-registerSink-(Landroid/hardware/location/IActivityRecognitionHardwareSink;)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-unregisterSink-(Landroid/hardware/location/IActivityRecognitionHardwareSink;)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/media/AudioService;-disableSafeMediaVolume-()V": ["android.permission.STATUS_BAR_SERVICE"], "Landroid/media/AudioService;-forceRemoteSubmixFullVolume-(Z Landroid/os/IBinder;)V": ["android.permission.CAPTURE_AUDIO_OUTPUT"], "Landroid/media/AudioService;-notifyVolumeControllerVisible-(Landroid/media/IVolumeController; Z)V": ["android.permission.STATUS_BAR_SERVICE"], "Landroid/media/AudioService;-registerAudioPolicy-(Landroid/media/audiopolicy/AudioPolicyConfig; Landroid/os/IBinder;)Z": ["android.permission.MODIFY_AUDIO_ROUTING"], "Landroid/media/AudioService;-registerRemoteControlDisplay-(Landroid/media/IRemoteControlDisplay; I I)Z": ["android.permission.MEDIA_CONTENT_CONTROL"], "Landroid/media/AudioService;-registerRemoteController-(Landroid/media/IRemoteControlDisplay; I I Landroid/content/ComponentName;)Z": ["android.permission.MEDIA_CONTENT_CONTROL"], "Landroid/media/AudioService;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-setMicrophoneMute-(Z Ljava/lang/String;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-setMode-(I Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS", "android.permission.MODIFY_PHONE_STATE"], "Landroid/media/AudioService;-setRemoteStreamVolume-(I)V": ["android.permission.STATUS_BAR_SERVICE"], "Landroid/media/AudioService;-setRingtonePlayer-(Landroid/media/IRingtonePlayer;)V": ["android.permission.REMOTE_AUDIO_PLAYBACK"], "Landroid/media/AudioService;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-setVolumeController-(Landroid/media/IVolumeController;)V": ["android.permission.STATUS_BAR_SERVICE"], "Landroid/media/AudioService;-startBluetoothSco-(Landroid/os/IBinder; I)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-startBluetoothScoVirtualCall-(Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-stopBluetoothSco-(Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpSinkService$BluetoothA2dpSinkBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpSinkService$BluetoothA2dpSinkBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpSinkService$BluetoothA2dpSinkBinder;-getAudioConfig-(Landroid/bluetooth/BluetoothDevice;)Landroid/bluetooth/BluetoothAudioConfig;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpSinkService$BluetoothA2dpSinkBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpSinkService$BluetoothA2dpSinkBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpSinkService$BluetoothA2dpSinkBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-sendPassThroughCmd-(Landroid/bluetooth/BluetoothDevice; I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-cancelBondProcess-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-configHciSnoopLog-(Z)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-connectSocket-(Landroid/bluetooth/BluetoothDevice; I Landroid/os/ParcelUuid; I I)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-createBond-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-createSocketChannel-(I Ljava/lang/String; Landroid/os/ParcelUuid; I I)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-disable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-enable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-enableNoAutoConnect-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-fetchRemoteMasInstances-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.RECEIVE_BLUETOOTH_MAP"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-fetchRemoteUuids-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getActivityEnergyInfoFromController-()V": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getAdapterConnectionState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getBondState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getBondedDevices-()[Landroid/bluetooth/BluetoothDevice;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getDiscoverableTimeout-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getMessageAccessPermission-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getPhonebookAccessPermission-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteAlias-(Landroid/bluetooth/BluetoothDevice;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteClass-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteName-(Landroid/bluetooth/BluetoothDevice;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteType-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteUuids-(Landroid/bluetooth/BluetoothDevice;)[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isActivityAndEnergyReportingSupported-()Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isMultiAdvertisementSupported-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isOffloadedFilteringSupported-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isOffloadedScanBatchingSupported-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-removeBond-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-reportActivityInfo-()Landroid/bluetooth/BluetoothActivityEnergyInfo;": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-sendConnectionStateChange-(Landroid/bluetooth/BluetoothDevice; I I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setDiscoverableTimeout-(I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setMessageAccessPermission-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPairingConfirmation-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPasskey-(Landroid/bluetooth/BluetoothDevice; Z I [B)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPhonebookAccessPermission-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPin-(Landroid/bluetooth/BluetoothDevice; Z I [B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setRemoteAlias-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setScanMode-(I I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-startDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addCharacteristic-(I Landroid/os/ParcelUuid; I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addDescriptor-(I Landroid/os/ParcelUuid; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addIncludedService-(I I I Landroid/os/ParcelUuid;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-beginReliableWrite-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-beginServiceDeclaration-(I I I I Landroid/os/ParcelUuid; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clearServices-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clientConnect-(I Ljava/lang/String; Z I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clientDisconnect-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-configureMTU-(I Ljava/lang/String; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-connectionParameterUpdate-(I Ljava/lang/String; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-discoverServices-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-endReliableWrite-(I Ljava/lang/String; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-endServiceDeclaration-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readCharacteristic-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readDescriptor-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readRemoteRssi-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-refreshDevice-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerClient-(Landroid/os/ParcelUuid; Landroid/bluetooth/IBluetoothGattCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerForNotification-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerServer-(Landroid/os/ParcelUuid; Landroid/bluetooth/IBluetoothGattServerCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-removeService-(I I I Landroid/os/ParcelUuid;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-sendNotification-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; Z [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-sendResponse-(I Ljava/lang/String; I I I [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-serverConnect-(I Ljava/lang/String; Z I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-serverDisconnect-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-startMultiAdvertising-(I Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseSettings;)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-startScan-(I Z Landroid/bluetooth/le/ScanSettings; Ljava/util/List; Ljava/util/List;)V": ["android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-stopMultiAdvertising-(I)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-stopScan-(I Z)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-unregisterClient-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-unregisterServer-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-writeCharacteristic-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I I [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-writeDescriptor-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I I [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-connectChannelToSink-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getConnectedHealthDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getHealthDeviceConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getHealthDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-registerAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration; Landroid/bluetooth/IBluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-clccResponse-(I I I I Z Ljava/lang/String; I)V": ["android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_PHONE_STATE"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-connectAudio-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disableWBS-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disconnectAudio-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-enableWBS-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-isAudioConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-isAudioOn-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-phoneStateChanged-(I I I Ljava/lang/String; I)V": ["android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_PHONE_STATE"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-sendVendorSpecificResultCode-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-acceptCall-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-connectAudio-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-dial-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-dialMemory-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-disconnectAudio-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-enterPrivateMode-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-explicitCallTransfer-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getCurrentAgEvents-(Landroid/bluetooth/BluetoothDevice;)Landroid/os/Bundle;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getCurrentAgFeatures-(Landroid/bluetooth/BluetoothDevice;)Landroid/os/Bundle;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getCurrentCalls-(Landroid/bluetooth/BluetoothDevice;)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getLastVoiceTagNumber-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-holdCall-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-redial-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-rejectCall-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-sendDTMF-(Landroid/bluetooth/BluetoothDevice; B)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-terminateCall-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getProtocolMode-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getReport-(Landroid/bluetooth/BluetoothDevice; B B I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-sendData-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setProtocolMode-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setReport-(Landroid/bluetooth/BluetoothDevice; B Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-virtualUnplug-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getClient-()Landroid/bluetooth/BluetoothDevice;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-isConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-setBluetoothTethering-(Z)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getCompleteVoiceMailNumber-()Ljava/lang/String;": ["android.permission.CALL_PRIVILEGED"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getDeviceId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getDeviceSvn-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getGroupIdLevel1-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIccSerialNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIccSimChallengeResponse-(J I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIsimChallengeResponse-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIsimDomain-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIsimImpi-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIsimImpu-()[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIsimIst-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getIsimPcscf-()[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getLine1AlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getLine1Number-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getMsisdn-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getSubscriberId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getVoiceMailAlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoProxy;-getVoiceMailNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-addSubInfoRecord-(Ljava/lang/String; I)I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-clearDefaultsForInactiveSubIds-()V": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-clearSubInfo-()I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getActiveSubInfoCount-()I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getActiveSubInfoList-()Ljava/util/List;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getAllSubInfoCount-()I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getAllSubInfoList-()Ljava/util/List;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getSubInfoForSubscriber-(J)Landroid/telephony/SubInfoRecord;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getSubInfoUsingIccId-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getSubInfoUsingSlotId-(I)Ljava/util/List;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setColor-(I J)I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDataRoaming-(I J)I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDisplayName-(Ljava/lang/String; J)I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDisplayNameUsingSrc-(Ljava/lang/String; J J)I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDisplayNumber-(Ljava/lang/String; J)I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDisplayNumberFormat-(I J)I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/UiccSmsController;-copyMessageToIccEf-(Ljava/lang/String; I [B [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-copyMessageToIccEfForSubscriber-(J Ljava/lang/String; I [B [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-disableCellBroadcast-(I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-disableCellBroadcastForSubscriber-(J I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-disableCellBroadcastRange-(I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-disableCellBroadcastRangeForSubscriber-(J I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-enableCellBroadcast-(I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-enableCellBroadcastForSubscriber-(J I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-enableCellBroadcastRange-(I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-enableCellBroadcastRangeForSubscriber-(J I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-getAllMessagesFromIccEf-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-getAllMessagesFromIccEfForSubscriber-(J Ljava/lang/String;)Ljava/util/List;": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-sendData-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; I [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendDataForSubscriber-(J Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; I [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendMultipartText-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/util/List; Ljava/util/List; Ljava/util/List;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION", "android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/internal/telephony/UiccSmsController;-sendMultipartTextForSubscriber-(J Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/util/List; Ljava/util/List; Ljava/util/List;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION", "android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/internal/telephony/UiccSmsController;-sendStoredMultipartText-(J Ljava/lang/String; Landroid/net/Uri; Ljava/lang/String; Ljava/util/List; Ljava/util/List;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION", "android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/internal/telephony/UiccSmsController;-sendStoredText-(J Ljava/lang/String; Landroid/net/Uri; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION", "android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/internal/telephony/UiccSmsController;-sendText-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION", "android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/internal/telephony/UiccSmsController;-sendTextForSubscriber-(J Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION", "android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/internal/telephony/UiccSmsController;-updateMessageOnIccEf-(Ljava/lang/String; I I [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-updateMessageOnIccEfForSubscriber-(J Ljava/lang/String; I I [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-addNfcUnlockHandler-(Landroid/nfc/INfcUnlockHandler; [I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-disable-(Z)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-disableNdefPush-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-dispatch-(Landroid/nfc/Tag;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-enable-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-enableNdefPush-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-invokeBeam-()V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-invokeBeamInternal-(Landroid/nfc/BeamShareData;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-pausePolling-(I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-resumePolling-()V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setAppCallback-(Landroid/nfc/IAppCallback;)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setForegroundDispatch-(Landroid/app/PendingIntent; [Landroid/content/IntentFilter; Landroid/nfc/TechListParcel;)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setP2pModes-(I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$TagService;-close-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-connect-(I I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-formatNdef-(I [B)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-getTechList-(I)[I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-getTimeout-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-isNdef-(I)Z": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefMakeReadOnly-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefRead-(I)Landroid/nfc/NdefMessage;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefWrite-(I Landroid/nfc/NdefMessage;)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-reconnect-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-rediscover-(I)Landroid/nfc/Tag;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-resetTimeouts-()V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-setTimeout-(I I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-transceive-(I [B Z)Landroid/nfc/TransceiveResult;": ["android.permission.NFC"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-getAidGroupForService-(I Landroid/content/ComponentName; Ljava/lang/String;)Landroid/nfc/cardemulation/AidGroup;": ["android.permission.NFC"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-getServices-(I Ljava/lang/String;)Ljava/util/List;": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-isDefaultServiceForAid-(I Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-isDefaultServiceForCategory-(I Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-registerAidGroupForService-(I Landroid/content/ComponentName; Landroid/nfc/cardemulation/AidGroup;)Z": ["android.permission.NFC"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-removeAidGroupForService-(I Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-setDefaultForNextTap-(I Landroid/content/ComponentName;)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-setDefaultServiceForCategory-(I Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-setPreferredService-(Landroid/content/ComponentName;)Z": ["android.permission.NFC"], "Lcom/android/nfc/cardemulation/CardEmulationManager$CardEmulationInterface;-unsetPreferredService-()Z": ["android.permission.NFC"], "Lcom/android/phone/PhoneInterfaceManager;-answerRingingCall-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-call-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-disableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-disableLocationUpdatesForSubscriber-(J)V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-enableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableLocationUpdatesForSubscriber-(J)V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableSimplifiedNetworkSettingsForSubscriber-(J Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-endCall-()Z": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-endCallForSubscriber-(J)Z": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-getAllCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-getCalculatedPreferredNetworkType-()I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaMdn-(J)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaMin-(J)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCellLocation-()Landroid/os/Bundle;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-getDataEnabled-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getLine1AlphaTagForDisplay-(J)Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getLine1NumberForDisplay-(J)Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getNeighboringCellInfo-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-getPcscfAddress-(Ljava/lang/String;)[Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getPreferredNetworkType-()I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getSimplifiedNetworkSettingsEnabledForSubscriber-(J)Z": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-handlePinMmi-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-handlePinMmiForSubscriber-(J Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccCloseLogicalChannel-(I)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccExchangeSimIO-(I I I I I Ljava/lang/String;)[B": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccOpenLogicalChannel-(Ljava/lang/String;)Landroid/telephony/IccOpenLogicalChannelResponse;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccTransmitApduBasicChannel-(I I I I I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccTransmitApduLogicalChannel-(I I I I I I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-invokeOemRilRequestRaw-([B [B)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isSimPinEnabled-()Z": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvReadItem-(I)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvResetConfig-(I)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvWriteCdmaPrl-([B)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvWriteItem-(I Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-sendEnvelopeWithStatus-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setDataEnabled-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setImsRegistrationState-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setLine1NumberForDisplayForSubscriber-(J Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setOperatorBrandOverride-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setPreferredNetworkType-(I)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadio-(Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadioForSubscriber-(J Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadioPower-(Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-shutdownMobileRadios-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPin-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPinForSubscriber-(J Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPinReportResult-(Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPinReportResultForSubscriber-(J Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPuk-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPukForSubscriber-(J Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPukReportResult-(Ljava/lang/String; Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPukReportResultForSubscriber-(J Ljava/lang/String; Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-toggleRadioOnOff-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-toggleRadioOnOffForSubscriber-(J)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/providers/contacts/ContactsProvider2;-getType-(Landroid/net/Uri;)Ljava/lang/String;": ["android.permission.READ_SOCIAL_STREAM"], "Lcom/android/server/AppOpsService;-checkAudioOperation-(I I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-checkOperation-(I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-finishOperation-(Landroid/os/IBinder; I I Ljava/lang/String;)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-getOpsForPackage-(I Ljava/lang/String; [I)Ljava/util/List;": ["android.permission.GET_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-getPackagesForOps-([I)Ljava/util/List;": ["android.permission.GET_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-noteOperation-(I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-resetAllModes-()V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-setAudioRestriction-(I I I I [Ljava/lang/String;)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-setMode-(I I Ljava/lang/String; I)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-startOperation-(Landroid/os/IBinder; I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/BluetoothManagerService;-disable-(Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-enable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-enableNoAutoConnect-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-registerStateChangeCallback-(Landroid/bluetooth/IBluetoothStateChangeCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-unregisterAdapter-(Landroid/bluetooth/IBluetoothManagerCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-unregisterStateChangeCallback-(Landroid/bluetooth/IBluetoothStateChangeCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/ConnectivityService;-captivePortalCheckCompleted-(Landroid/net/NetworkInfo; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-findConnectionTypeForIface-(Ljava/lang/String;)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getActiveLinkProperties-()Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveLinkQualityInfo-()Landroid/net/LinkQualityInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfoForUid-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getActiveNetworkQuotaInfo-()Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllLinkQualityInfo-()[Landroid/net/LinkQualityInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkState-()[Landroid/net/NetworkState;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworks-()[Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLastTetherError-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLinkProperties-(Landroid/net/Network;)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLinkPropertiesForType-(I)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLinkQualityInfo-(I)Landroid/net/LinkQualityInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getMobileProvisioningUrl-()Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getMobileRedirectedProvisioningUrl-()Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getNetworkCapabilities-(Landroid/net/Network;)Landroid/net/NetworkCapabilities;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkForType-(I)Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkInfoForNetwork-(Landroid/net/Network;)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getProvisioningOrActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableBluetoothRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableUsbRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableWifiRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheredDhcpRanges-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getTetheredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheringErroredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isNetworkSupported-(I)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isTetheringSupported-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-listenForNetwork-(Landroid/net/NetworkCapabilities; Landroid/os/Messenger; Landroid/os/IBinder;)Landroid/net/NetworkRequest;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-prepareVpn-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-registerNetworkAgent-(Landroid/os/Messenger; Landroid/net/NetworkInfo; Landroid/net/LinkProperties; Landroid/net/NetworkCapabilities; I Landroid/net/NetworkMisc;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-registerNetworkFactory-(Landroid/os/Messenger; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-reportBadNetwork-(Landroid/net/Network;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET"], "Lcom/android/server/ConnectivityService;-reportInetCondition-(I I)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET"], "Lcom/android/server/ConnectivityService;-requestNetwork-(Landroid/net/NetworkCapabilities; Landroid/os/Messenger; I Landroid/os/IBinder; I)Landroid/net/NetworkRequest;": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-requestRouteToHostAddress-(I [B)Z": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setAirplaneMode-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setDataDependency-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setGlobalProxy-(Landroid/net/ProxyInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setPolicyDataEnable-(I Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/ConnectivityService;-setProvisioningNotificationVisible-(Z I Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setUsbTethering-(Z)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-startLegacyVpn-(Lcom/android/internal/net/VpnProfile;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-supplyMessenger-(I Landroid/os/Messenger;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-tether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-unregisterNetworkFactory-(Landroid/os/Messenger;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-untether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-updateLockdownVpn-()Z": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConsumerIrService;-getCarrierFrequencies-()[I": ["android.permission.TRANSMIT_IR"], "Lcom/android/server/ConsumerIrService;-transmit-(Ljava/lang/String; I [I)V": ["android.permission.TRANSMIT_IR"], "Lcom/android/server/DropBoxManagerService;-getNextEntry-(Ljava/lang/String; J)Landroid/os/DropBoxManager$Entry;": ["android.permission.READ_LOGS"], "Lcom/android/server/InputMethodManagerService;-addClient-(Lcom/android/internal/view/IInputMethodClient; Lcom/android/internal/view/IInputContext; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getCurrentInputMethodSubtype-()Landroid/view/inputmethod/InputMethodSubtype;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getEnabledInputMethodList-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getEnabledInputMethodSubtypeList-(Ljava/lang/String; Z)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getInputMethodList-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getLastInputMethodSubtype-()Landroid/view/inputmethod/InputMethodSubtype;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-hideMySoftInput-(Landroid/os/IBinder; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-hideSoftInput-(Lcom/android/internal/view/IInputMethodClient; I Landroid/os/ResultReceiver;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-notifySuggestionPicked-(Landroid/text/style/SuggestionSpan; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-registerSuggestionSpansForNotification-([Landroid/text/style/SuggestionSpan;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-removeClient-(Lcom/android/internal/view/IInputMethodClient;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setAdditionalInputMethodSubtypes-(Ljava/lang/String; [Landroid/view/inputmethod/InputMethodSubtype;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setCurrentInputMethodSubtype-(Landroid/view/inputmethod/InputMethodSubtype;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setImeWindowStatus-(Landroid/os/IBinder; I I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/InputMethodManagerService;-setInputMethod-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodAndSubtype-(Landroid/os/IBinder; Ljava/lang/String; Landroid/view/inputmethod/InputMethodSubtype;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodEnabled-(Ljava/lang/String; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-shouldOfferSwitchingToNextInputMethod-(Landroid/os/IBinder;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showInputMethodAndSubtypeEnablerFromClient-(Lcom/android/internal/view/IInputMethodClient; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR"], "Lcom/android/server/InputMethodManagerService;-showInputMethodPickerFromClient-(Lcom/android/internal/view/IInputMethodClient;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showMySoftInput-(Landroid/os/IBinder; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showSoftInput-(Lcom/android/internal/view/IInputMethodClient; I Landroid/os/ResultReceiver;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-startInput-(Lcom/android/internal/view/IInputMethodClient; Lcom/android/internal/view/IInputContext; Landroid/view/inputmethod/EditorInfo; I)Lcom/android/internal/view/InputBindResult;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-switchToLastInputMethod-(Landroid/os/IBinder;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-switchToNextInputMethod-(Landroid/os/IBinder; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-updateStatusIcon-(Landroid/os/IBinder; Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR"], "Lcom/android/server/InputMethodManagerService;-windowGainedFocus-(Lcom/android/internal/view/IInputMethodClient; Landroid/os/IBinder; I I I Landroid/view/inputmethod/EditorInfo; Lcom/android/internal/view/IInputContext;)Lcom/android/internal/view/InputBindResult;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR"], "Lcom/android/server/LocationManagerService;-addGpsMeasurementsListener-(Landroid/location/IGpsMeasurementsListener; Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-addGpsNavigationMessageListener-(Landroid/location/IGpsNavigationMessageListener; Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-addGpsStatusListener-(Landroid/location/IGpsStatusListener; Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-addTestProvider-(Ljava/lang/String; Lcom/android/internal/location/ProviderProperties;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderEnabled-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderLocation-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderStatus-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getLastLocation-(Landroid/location/LocationRequest; Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviderProperties-(Ljava/lang/String;)Lcom/android/internal/location/ProviderProperties;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-removeGeofence-(Landroid/location/Geofence; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-removeTestProvider-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-removeUpdates-(Landroid/location/ILocationListener; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-reportLocation-(Landroid/location/Location; Z)V": ["android.permission.INSTALL_LOCATION_PROVIDER"], "Lcom/android/server/LocationManagerService;-requestGeofence-(Landroid/location/LocationRequest; Landroid/location/Geofence; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-requestLocationUpdates-(Landroid/location/LocationRequest; Landroid/location/ILocationListener; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.UPDATE_APP_OPS_STATS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/LocationManagerService;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Lcom/android/server/LocationManagerService;-setTestProviderEnabled-(Ljava/lang/String; Z)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-setTestProviderLocation-(Ljava/lang/String; Landroid/location/Location;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-setTestProviderStatus-(Ljava/lang/String; I Landroid/os/Bundle; J)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LockSettingsService;-checkPassword-(Ljava/lang/String; I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-checkPattern-(Ljava/lang/String; I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-checkVoldPassword-(I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-getBoolean-(Ljava/lang/String; Z I)Z": ["android.permission.READ_PROFILE"], "Lcom/android/server/LockSettingsService;-getLong-(Ljava/lang/String; J I)J": ["android.permission.READ_PROFILE"], "Lcom/android/server/LockSettingsService;-getString-(Ljava/lang/String; Ljava/lang/String; I)Ljava/lang/String;": ["android.permission.READ_PROFILE"], "Lcom/android/server/LockSettingsService;-removeUser-(I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setBoolean-(Ljava/lang/String; Z I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLockPassword-(Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLockPattern-(Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLong-(Ljava/lang/String; J I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setString-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/MmsServiceBroker$BinderService;-addMultimediaMessageDraft-(Ljava/lang/String; Landroid/net/Uri;)Landroid/net/Uri;": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-addTextMessageDraft-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)Landroid/net/Uri;": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-archiveStoredConversation-(Ljava/lang/String; J Z)Z": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-deleteStoredConversation-(Ljava/lang/String; J)Z": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-deleteStoredMessage-(Ljava/lang/String; Landroid/net/Uri;)Z": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-downloadMessage-(J Ljava/lang/String; Ljava/lang/String; Landroid/net/Uri; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.RECEIVE_MMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-importMultimediaMessage-(Ljava/lang/String; Landroid/net/Uri; Ljava/lang/String; J Z Z)Landroid/net/Uri;": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-importTextMessage-(Ljava/lang/String; Ljava/lang/String; I Ljava/lang/String; J Z Z)Landroid/net/Uri;": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-sendMessage-(J Ljava/lang/String; Landroid/net/Uri; Ljava/lang/String; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-sendStoredMessage-(J Ljava/lang/String; Landroid/net/Uri; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-setAutoPersisting-(Ljava/lang/String; Z)V": ["android.permission.WRITE_SMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-updateStoredMessageStatus-(Ljava/lang/String; Landroid/net/Uri; Landroid/content/ContentValues;)Z": ["android.permission.WRITE_SMS"], "Lcom/android/server/MountService;-changeEncryptionPassword-(I Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-createSecureContainer-(Ljava/lang/String; I Ljava/lang/String; Ljava/lang/String; I Z)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-decryptStorage-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-destroySecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_DESTROY"], "Lcom/android/server/MountService;-encryptStorage-(I Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-finalizeSecureContainer-(Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-fixPermissionsSecureContainer-(Ljava/lang/String; I Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-formatVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-getEncryptionState-()I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-getSecureContainerFilesystemPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerList-()[Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getStorageUsers-(Ljava/lang/String;)[I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-getVolumeList-()[Landroid/os/storage/StorageVolume;": ["android.permission.ACCESS_ALL_EXTERNAL_STORAGE"], "Lcom/android/server/MountService;-isSecureContainerMounted-(Ljava/lang/String;)Z": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-mountSecureContainer-(Ljava/lang/String; Ljava/lang/String; I Z)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-mountVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-renameSecureContainer-(Ljava/lang/String; Ljava/lang/String;)I": ["android.permission.ASEC_RENAME"], "Lcom/android/server/MountService;-resizeSecureContainer-(Ljava/lang/String; I Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-setUsbMassStorageEnabled-(Z)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-shutdown-(Landroid/os/storage/IMountShutdownObserver;)V": ["android.permission.SHUTDOWN"], "Lcom/android/server/MountService;-unmountSecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-unmountVolume-(Ljava/lang/String; Z Z)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-verifyEncryptionPassword-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/NetworkManagementService;-addIdleTimer-(Ljava/lang/String; I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addInterfaceToLocalNetwork-(Ljava/lang/String; Ljava/util/List;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addInterfaceToNetwork-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addLegacyRouteForNetId-(I Landroid/net/RouteInfo; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addRoute-(I Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addVpnUidRanges-(I [Landroid/net/UidRange;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-allowProtect-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-attachPppd-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearDefaultNetId-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearInterfaceAddresses-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearPermission-([I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-createPhysicalNetwork-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-createVirtualNetwork-(I Z Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-denyProtect-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-detachPppd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-flushNetworkDnsCache-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getDnsForwarders-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getInterfaceConfig-(Ljava/lang/String;)Landroid/net/InterfaceConfiguration;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getIpForwardingEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsDetail-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryDev-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryXt-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsTethering-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsUidDetail-(I)Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getRoutes-(Ljava/lang/String;)[Landroid/net/RouteInfo;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isBandwidthControlEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isClatdStarted-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isTetheringStarted-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTetheredInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTtys-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-registerObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeIdleTimer-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceAlert-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceFromLocalNetwork-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceFromNetwork-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceQuota-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeNetwork-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeRoute-(I Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeVpnUidRanges-(I [Landroid/net/UidRange;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDefaultNetId-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsForwarders-(Landroid/net/Network; [Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsServersForNetwork-(I [Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setGlobalAlert-(J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceAlert-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceConfig-(Ljava/lang/String; Landroid/net/InterfaceConfiguration;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceDown-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceIpv6PrivacyExtensions-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceQuota-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceUp-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setIpForwardingEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setMtu-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setPermission-(Ljava/lang/String; [I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setUidNetworkRules-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-shutdown-()V": ["android.permission.SHUTDOWN"], "Lcom/android/server/NetworkManagementService;-startAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startClatd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startTethering-([Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopAccessPoint-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopClatd-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopTethering-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-tetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-unregisterObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-untetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-wifiFirmwareReload-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkScoreService;-clearScores-()Z": ["android.permission.BROADCAST_SCORE_NETWORKS", "android.permission.SCORE_NETWORKS"], "Lcom/android/server/NetworkScoreService;-disableScoring-()V": ["android.permission.BROADCAST_SCORE_NETWORKS", "android.permission.SCORE_NETWORKS"], "Lcom/android/server/NetworkScoreService;-registerNetworkScoreCache-(I Landroid/net/INetworkScoreCache;)V": ["android.permission.BROADCAST_SCORE_NETWORKS"], "Lcom/android/server/NetworkScoreService;-setActiveScorer-(Ljava/lang/String;)Z": ["android.permission.BROADCAST_SCORE_NETWORKS"], "Lcom/android/server/NetworkScoreService;-updateScores-([Landroid/net/ScoredNetwork;)Z": ["android.permission.SCORE_NETWORKS"], "Lcom/android/server/NsdService;-getMessenger-()Landroid/os/Messenger;": ["android.permission.INTERNET"], "Lcom/android/server/NsdService;-setEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/SerialService;-getSerialPorts-()[Ljava/lang/String;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/SerialService;-openSerialPort-(Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/TelephonyRegistry;-listen-(Ljava/lang/String; Lcom/android/internal/telephony/IPhoneStateListener; I Z)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE", "android.permission.READ_PRECISE_PHONE_STATE", "android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-listenForSubscriber-(J Ljava/lang/String; Lcom/android/internal/telephony/IPhoneStateListener; I Z)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE", "android.permission.READ_PRECISE_PHONE_STATE", "android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallForwardingChanged-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallForwardingChangedForSubscriber-(J Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallState-(I Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallStateForSubscriber-(J I Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellInfo-(Ljava/util/List;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellInfoForSubscriber-(J Ljava/util/List;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellLocation-(Landroid/os/Bundle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellLocationForSubscriber-(J Landroid/os/Bundle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataActivity-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataActivityForSubscriber-(J I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnection-(I Z Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/net/LinkProperties; Landroid/net/NetworkCapabilities; I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionFailed-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionFailedForSubscriber-(J Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionForSubscriber-(J I Z Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/net/LinkProperties; Landroid/net/NetworkCapabilities; I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionRealTimeInfo-(Landroid/telephony/DataConnectionRealTimeInfo;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDisconnectCause-(I I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyMessageWaitingChangedForPhoneId-(I J Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyOemHookRawEventForSubscriber-(J [B)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyOtaspChanged-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyPreciseCallState-(I I I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyPreciseDataConnectionFailed-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyServiceStateForPhoneId-(I J Landroid/telephony/ServiceState;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifySignalStrength-(Landroid/telephony/SignalStrength;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifySignalStrengthForSubscriber-(J Landroid/telephony/SignalStrength;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyVoLteServiceStateChanged-(Landroid/telephony/VoLteServiceState;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellChecker-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellCheckerSubtype-(Ljava/lang/String; I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setSpellCheckerEnabled-(Z)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/UpdateLockService;-acquireUpdateLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/UpdateLockService;-releaseUpdateLock-(Landroid/os/IBinder;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/VibratorService;-cancelVibrate-(Landroid/os/IBinder;)V": ["android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibrate-(I Ljava/lang/String; J I Landroid/os/IBinder;)V": ["android.permission.UPDATE_APP_OPS_STATS", "android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibratePattern-(I Ljava/lang/String; [J I I Landroid/os/IBinder;)V": ["android.permission.UPDATE_APP_OPS_STATS", "android.permission.VIBRATE"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-computeClickPointInScreen-(I J I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfoByAccessibilityId-(I J I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; I J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfosByText-(I J Ljava/lang/String; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfosByViewId-(I J Ljava/lang/String; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findFocus-(I J I I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-focusSearch-(I J I I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getWindow-(I)Landroid/view/accessibility/AccessibilityWindowInfo;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getWindows-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-performAccessibilityAction-(I J I Landroid/os/Bundle; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-performGlobalAction-(I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-addAccessibilityInteractionConnection-(Landroid/view/IWindow; Landroid/view/accessibility/IAccessibilityInteractionConnection; I)I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-addClient-(Landroid/view/accessibility/IAccessibilityManagerClient; I)I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getEnabledAccessibilityServiceList-(I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getInstalledAccessibilityServiceList-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getWindowToken-(I)Landroid/os/IBinder;": ["getWindowToken"], "Lcom/android/server/accessibility/AccessibilityManagerService;-interrupt-(I)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-removeAccessibilityInteractionConnection-(Landroid/view/IWindow;)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-sendAccessibilityEvent-(Landroid/view/accessibility/AccessibilityEvent; I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-temporaryEnableAccessibilityStateUntilKeyguardRemoved-(Landroid/content/ComponentName; Z)V": ["temporaryEnableAccessibilityStateUntilKeyguardRemoved"], "Lcom/android/server/accounts/AccountManagerService;-addAccount-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Z Landroid/os/Bundle;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-addAccountAsUser-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Z Landroid/os/Bundle; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-addAccountExplicitly-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-clearPassword-(Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-confirmCredentialsAsUser-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Landroid/os/Bundle; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-editProperties-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Z)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-getAccounts-(Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsAsUser-(Ljava/lang/String; I)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsByFeatures-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; [Ljava/lang/String;)V": ["android.permission.GET_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsByTypeForPackage-(Ljava/lang/String; Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsForPackage-(Ljava/lang/String; I)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAuthToken-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Ljava/lang/String; Z Z Landroid/os/Bundle;)V": ["android.permission.USE_CREDENTIALS"], "Lcom/android/server/accounts/AccountManagerService;-getAuthenticatorTypes-(I)[Landroid/accounts/AuthenticatorDescription;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getPassword-(Landroid/accounts/Account;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-getUserData-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-hasFeatures-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; [Ljava/lang/String;)V": ["android.permission.GET_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-invalidateAuthToken-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MANAGE_ACCOUNTS", "android.permission.USE_CREDENTIALS"], "Lcom/android/server/accounts/AccountManagerService;-peekAuthToken-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-removeAccount-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-removeAccountAsUser-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-renameAccount-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-setAuthToken-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-setPassword-(Landroid/accounts/Account; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-setUserData-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-updateCredentials-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Ljava/lang/String; Z Landroid/os/Bundle;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/am/ActivityManagerService;-activityDestroyed-(Landroid/os/IBinder;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-activityIdle-(Landroid/os/IBinder; Landroid/content/res/Configuration; Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-activityPaused-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-activitySlept-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-activityStopped-(Landroid/os/IBinder; Landroid/os/Bundle; Landroid/os/PersistableBundle; Ljava/lang/CharSequence;)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-appNotRespondingViaProvider-(Landroid/os/IBinder;)V": ["android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-attachApplication-(Landroid/app/IApplicationThread;)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-backgroundResourcesReleased-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-bindBackupAgent-(Landroid/content/pm/ApplicationInfo; I)Z": ["android.permission.CONFIRM_FULL_BACKUP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-bindService-(Landroid/app/IApplicationThread; Landroid/os/IBinder; Landroid/content/Intent; Ljava/lang/String; Landroid/app/IServiceConnection; I I)I": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-clearApplicationUserData-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver; I)Z": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-clearPendingBackup-()V": ["android.permission.BACKUP"], "Lcom/android/server/am/ActivityManagerService;-closeSystemDialogs-(Ljava/lang/String;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-convertFromTranslucent-(Landroid/os/IBinder;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_APP_OPS_STATS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-convertToTranslucent-(Landroid/os/IBinder; Landroid/app/ActivityOptions;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-crashApplication-(I I Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-createActivityContainer-(Landroid/os/IBinder; Landroid/app/IActivityContainerCallback;)Landroid/app/IActivityContainer;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-deleteActivityContainer-(Landroid/app/IActivityContainer;)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-dumpHeap-(Ljava/lang/String; I Z Ljava/lang/String; Landroid/os/ParcelFileDescriptor;)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-finishActivity-(Landroid/os/IBinder; I Landroid/content/Intent; Z)Z": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-finishActivityAffinity-(Landroid/os/IBinder;)Z": ["android.permission.MANAGE_APP_TOKENS", "android.permission.UPDATE_APP_OPS_STATS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-finishHeavyWeightApp-()V": ["android.permission.BROADCAST_STICKY", "android.permission.FORCE_STOP_PACKAGES", "android.permission.MANAGE_APP_TOKENS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-finishInstrumentation-(Landroid/app/IApplicationThread; I Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-finishReceiver-(Landroid/os/IBinder; I Ljava/lang/String; Landroid/os/Bundle; Z)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-finishSubActivity-(Landroid/os/IBinder; Ljava/lang/String; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-finishVoiceTask-(Landroid/service/voice/IVoiceInteractionSession;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-forceStopPackage-(Ljava/lang/String; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.FORCE_STOP_PACKAGES", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-getAllStackInfos-()Ljava/util/List;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getAssistContextExtras-(I)Landroid/os/Bundle;": ["android.permission.GET_TOP_ACTIVITY_INFO"], "Lcom/android/server/am/ActivityManagerService;-getContentProvider-(Landroid/app/IApplicationThread; Ljava/lang/String; I Z)Landroid/app/IActivityManager$ContentProviderHolder;": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-getContentProviderExternal-(Ljava/lang/String; I Landroid/os/IBinder;)Landroid/app/IActivityManager$ContentProviderHolder;": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY"], "Lcom/android/server/am/ActivityManagerService;-getCurrentUser-()Landroid/content/pm/UserInfo;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-getHomeActivityToken-()Landroid/os/IBinder;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getProviderMimeType-(Landroid/net/Uri; I)Ljava/lang/String;": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-getRecentTasks-(I I I)Ljava/util/List;": ["android.permission.GET_DETAILED_TASKS", "android.permission.GET_TASKS", "android.permission.REAL_GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-getRunningUserIds-()[I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-getStackInfo-(I)Landroid/app/ActivityManager$StackInfo;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getTaskThumbnail-(I)Landroid/app/ActivityManager$TaskThumbnail;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/am/ActivityManagerService;-getTasks-(I I)Ljava/util/List;": ["android.permission.GET_TASKS", "android.permission.REAL_GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-handleApplicationCrash-(Landroid/os/IBinder; Landroid/app/ApplicationErrorReport$CrashInfo;)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-handleApplicationWtf-(Landroid/os/IBinder; Ljava/lang/String; Z Landroid/app/ApplicationErrorReport$CrashInfo;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-hang-(Landroid/os/IBinder; Z)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-inputDispatchingTimedOut-(I Z Ljava/lang/String;)J": ["android.permission.BROADCAST_STICKY", "android.permission.FILTER_EVENTS"], "Lcom/android/server/am/ActivityManagerService;-isInHomeStack-(I)Z": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-isUserRunning-(I Z)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-keyguardWaitingForActivityDrawn-()V": ["android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-killAllBackgroundProcesses-()V": ["android.permission.BROADCAST_STICKY", "android.permission.KILL_BACKGROUND_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-killBackgroundProcesses-(Ljava/lang/String; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.KILL_BACKGROUND_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-killUid-(I Ljava/lang/String;)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-launchAssistIntent-(Landroid/content/Intent; I Ljava/lang/String; I)Z": ["android.permission.GET_TOP_ACTIVITY_INFO"], "Lcom/android/server/am/ActivityManagerService;-moveActivityTaskToBack-(Landroid/os/IBinder; Z)Z": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskBackwards-(I)V": ["android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToBack-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToStack-(I I Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-navigateUpTo-(Landroid/os/IBinder; Landroid/content/Intent; I Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-noteWakeupAlarm-(Landroid/content/IIntentSender; I Ljava/lang/String;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-openContentUri-(Landroid/net/Uri;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-performIdleMaintenance-()V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-profileControl-(Ljava/lang/String; I Z Landroid/app/ProfilerInfo; I)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-publishContentProviders-(Landroid/app/IApplicationThread; Ljava/util/List;)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-publishService-(Landroid/os/IBinder; Landroid/content/Intent; Landroid/os/IBinder;)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-registerProcessObserver-(Landroid/app/IProcessObserver;)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-registerUserSwitchObserver-(Landroid/app/IUserSwitchObserver;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-releaseActivityInstance-(Landroid/os/IBinder;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-releaseSomeActivities-(Landroid/app/IApplicationThread;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-removeContentProvider-(Landroid/os/IBinder; Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-removeContentProviderExternal-(Ljava/lang/String; Landroid/os/IBinder;)V": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY", "android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-removeTask-(I I)Z": ["android.permission.REMOVE_TASKS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-reportAssistContextExtras-(Landroid/os/IBinder; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-requestBugReport-()V": ["android.permission.DUMP"], "Lcom/android/server/am/ActivityManagerService;-requestVisibleBehind-(Landroid/os/IBinder; Z)Z": ["android.permission.MANAGE_APP_TOKENS", "android.permission.UPDATE_APP_OPS_STATS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-resizeStack-(I Landroid/graphics/Rect;)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-restart-()V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-resumeAppSwitches-()V": ["android.permission.STOP_APP_SWITCHES"], "Lcom/android/server/am/ActivityManagerService;-setActivityController-(Landroid/app/IActivityController;)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-setAlwaysFinish-(Z)V": ["android.permission.SET_ALWAYS_FINISH"], "Lcom/android/server/am/ActivityManagerService;-setDebugApp-(Ljava/lang/String; Z Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-setFocusedStack-(I)V": ["android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-setFrontActivityScreenCompatMode-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_APP_TOKENS", "android.permission.SET_SCREEN_COMPATIBILITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-setLockScreenShown-(Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.DEVICE_POWER", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-setPackageAskScreenCompat-(Ljava/lang/String; Z)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setPackageScreenCompatMode-(Ljava/lang/String; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_APP_TOKENS", "android.permission.SET_SCREEN_COMPATIBILITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-setProcessForeground-(Landroid/os/IBinder; I Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.SET_PROCESS_LIMIT", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-setProcessLimit-(I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PROCESS_LIMIT", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-setRequestedOrientation-(Landroid/os/IBinder; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-setServiceForeground-(Landroid/content/ComponentName; Landroid/os/IBinder; I Landroid/app/Notification; Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-shutdown-(I)Z": ["android.permission.GET_APP_OPS_STATS", "android.permission.MANAGE_APP_TOKENS", "android.permission.SHUTDOWN", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-signalPersistentProcesses-(I)V": ["android.permission.SIGNAL_PERSISTENT_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-startActivities-(Landroid/app/IApplicationThread; Ljava/lang/String; [Landroid/content/Intent; [Ljava/lang/String; Landroid/os/IBinder; Landroid/os/Bundle; I)I": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startActivity-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle;)I": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startActivityAndWait-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle; I)Landroid/app/IActivityManager$WaitResult;": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startActivityAsCaller-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle; I)I": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startActivityAsUser-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle; I)I": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startActivityFromRecents-(I Landroid/os/Bundle;)I": ["android.permission.BROADCAST_STICKY", "android.permission.START_TASKS_FROM_RECENTS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startActivityIntentSender-(Landroid/app/IApplicationThread; Landroid/content/IntentSender; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I I Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityWithConfig-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/content/res/Configuration; Landroid/os/Bundle; I)I": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startInstrumentation-(Landroid/content/ComponentName; Ljava/lang/String; I Landroid/os/Bundle; Landroid/app/IInstrumentationWatcher; Landroid/app/IUiAutomationConnection; I Ljava/lang/String;)Z": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-startLockTaskMode-(Landroid/os/IBinder;)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startLockTaskMode-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startLockTaskModeOnCurrent-()V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_ACTIVITY_STACKS", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startNextMatchingActivity-(Landroid/os/IBinder; Landroid/content/Intent; Landroid/os/Bundle;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startService-(Landroid/app/IApplicationThread; Landroid/content/Intent; Ljava/lang/String; I)Landroid/content/ComponentName;": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-startUserInBackground-(I)Z": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-startVoiceActivity-(Ljava/lang/String; I I Landroid/content/Intent; Ljava/lang/String; Landroid/service/voice/IVoiceInteractionSession; Lcom/android/internal/app/IVoiceInteractor; I Landroid/app/ProfilerInfo; Landroid/os/Bundle; I)I": ["android.permission.BIND_VOICE_INTERACTION", "android.permission.BROADCAST_STICKY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-stopAppSwitches-()V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_TOKENS", "android.permission.START_ANY_ACTIVITY", "android.permission.STOP_APP_SWITCHES", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-stopLockTaskMode-()V": ["android.permission.BROADCAST_STICKY", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-stopLockTaskModeOnCurrent-()V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-stopService-(Landroid/app/IApplicationThread; Landroid/content/Intent; Ljava/lang/String; I)I": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-stopServiceToken-(Landroid/content/ComponentName; Landroid/os/IBinder; I)Z": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-stopUser-(I Landroid/app/IStopUserCallback;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-unbindBackupAgent-(Landroid/content/pm/ApplicationInfo;)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-unbindFinished-(Landroid/os/IBinder; Landroid/content/Intent; Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-unbindService-(Landroid/app/IServiceConnection;)Z": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-unbroadcastIntent-(Landroid/app/IApplicationThread; Landroid/content/Intent; I)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-unhandledBack-()V": ["android.permission.FORCE_BACK", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-unregisterReceiver-(Landroid/content/IIntentReceiver;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.START_ANY_ACTIVITY", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-unstableProviderDied-(Landroid/os/IBinder;)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-updateConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.BROADCAST_STICKY", "android.permission.CHANGE_CONFIGURATION", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ActivityManagerService;-updatePersistentConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.BROADCAST_STICKY", "android.permission.CHANGE_CONFIGURATION", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimeBattery-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimePlugged-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getStatistics-()[B": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getStatisticsStream-()Landroid/os/ParcelFileDescriptor;": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothState-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteChangeWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteEvent-(I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFlashlightOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFlashlightOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquired-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquiredFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleased-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleasedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteInteractive-(Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteJobFinish-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteJobStart-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteMobileRadioPowerState-(I J)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteNetworkInterfaceType-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteNetworkStatsEnabled-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneDataConnectionState-(I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneSignalStrength-(Landroid/telephony/SignalStrength;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneState-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteResetAudio-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteResetVideo-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenBrightness-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenState-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartAudio-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartVideo-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelock-(I I Ljava/lang/String; Ljava/lang/String; I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopAudio-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopVideo-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelock-(I I Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteSyncFinish-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteSyncStart-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteUserActivity-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteVibratorOff-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteVibratorOn-(I J)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiBatchedScanStartedFromSource-(Landroid/os/WorkSource; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiBatchedScanStoppedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRssiChanged-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunning-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunningChanged-(Landroid/os/WorkSource; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStarted-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStartedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStopped-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStoppedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiState-(I Ljava/lang/String;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiStopped-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiSupplicantStateChanged-(I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-setBatteryState-(I I I I I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ProcessStatsService;-getCurrentStats-(Ljava/util/List;)[B": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/ProcessStatsService;-getStatsOverTime-(J)Landroid/os/ParcelFileDescriptor;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-bindAppWidgetId-(Ljava/lang/String; I I Landroid/content/ComponentName; Landroid/os/Bundle;)Z": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-bindRemoteViewsService-(Ljava/lang/String; I Landroid/content/Intent; Landroid/os/IBinder;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-createAppWidgetConfigIntentSender-(Ljava/lang/String; I I)Landroid/content/IntentSender;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-deleteAppWidgetId-(Ljava/lang/String; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-getAppWidgetInfo-(Ljava/lang/String; I)Landroid/appwidget/AppWidgetProviderInfo;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-getAppWidgetOptions-(Ljava/lang/String; I)Landroid/os/Bundle;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-getAppWidgetViews-(Ljava/lang/String; I)Landroid/widget/RemoteViews;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-hasBindAppWidgetPermission-(Ljava/lang/String; I)Z": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-notifyAppWidgetViewDataChanged-(Ljava/lang/String; [I I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-partiallyUpdateAppWidgetIds-(Ljava/lang/String; [I Landroid/widget/RemoteViews;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-setBindAppWidgetPermission-(Ljava/lang/String; I Z)V": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-unbindRemoteViewsService-(Ljava/lang/String; I Landroid/content/Intent;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-updateAppWidgetIds-(Ljava/lang/String; [I Landroid/widget/RemoteViews;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-updateAppWidgetOptions-(Ljava/lang/String; I Landroid/os/Bundle;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-getAvailableRestoreSets-(Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-restoreAll-(J Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-restorePackage-(Ljava/lang/String; Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-restoreSome-(J Landroid/app/backup/IRestoreObserver; [Ljava/lang/String;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-acknowledgeFullBackupOrRestore-(I Z Ljava/lang/String; Ljava/lang/String; Landroid/app/backup/IFullBackupRestoreObserver;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-backupNow-()V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-beginRestoreSession-(Ljava/lang/String; Ljava/lang/String;)Landroid/app/backup/IRestoreSession;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-clearBackupData-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-dataChanged-(Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-fullBackup-(Landroid/os/ParcelFileDescriptor; Z Z Z Z Z Z Z [Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-fullRestore-(Landroid/os/ParcelFileDescriptor;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-fullTransportBackup-([Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-getConfigurationIntent-(Ljava/lang/String;)Landroid/content/Intent;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-getCurrentTransport-()Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-getDataManagementIntent-(Ljava/lang/String;)Landroid/content/Intent;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-getDataManagementLabel-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-getDestinationString-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-hasBackupPassword-()Z": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-isBackupEnabled-()Z": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-listAllTransports-()[Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-selectBackupTransport-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-setAutoRestore-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-setBackupEnabled-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-setBackupPassword-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService;-setBackupProvisioned-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/connectivity/Tethering;-interfaceAdded-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/connectivity/Tethering;-interfaceLinkStateChanged-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/connectivity/Tethering;-interfaceStatusChanged-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/content/ContentService;-addPeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle; J)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-cancelSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-cancelSyncAsUser-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getCurrentSyncs-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-getCurrentSyncsAsUser-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-getIsSyncable-(Landroid/accounts/Account; Ljava/lang/String;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getIsSyncableAsUser-(Landroid/accounts/Account; Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getMasterSyncAutomatically-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getMasterSyncAutomaticallyAsUser-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getPeriodicSyncs-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncAdapterTypes-()[Landroid/content/SyncAdapterType;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getSyncAdapterTypesAsUser-(I)[Landroid/content/SyncAdapterType;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncAutomaticallyAsUser-(Landroid/accounts/Account; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncStatus-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Landroid/content/SyncStatusInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-getSyncStatusAsUser-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName; I)Landroid/content/SyncStatusInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncActive-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Z": ["android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncPending-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncPendingAsUser-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-registerContentObserver-(Landroid/net/Uri; Z Landroid/database/IContentObserver; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-removePeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setIsSyncable-(Landroid/accounts/Account; Ljava/lang/String; I)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setMasterSyncAutomatically-(Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setMasterSyncAutomaticallyAsUser-(Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setSyncAutomaticallyAsUser-(Landroid/accounts/Account; Ljava/lang/String; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-sync-(Landroid/content/SyncRequest;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-syncAsUser-(Landroid/content/SyncRequest; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-addCrossProfileIntentFilter-(Landroid/content/ComponentName; Landroid/content/IntentFilter; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-addCrossProfileWidgetProvider-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-addPersistentPreferredActivity-(Landroid/content/ComponentName; Landroid/content/IntentFilter; Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-clearCrossProfileIntentFilters-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-clearPackagePersistentPreferredActivities-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-clearProfileOwner-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-createAndInitializeUser-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String; Landroid/content/ComponentName; Landroid/os/Bundle;)Landroid/os/UserHandle;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-createUser-(Landroid/content/ComponentName; Ljava/lang/String;)Landroid/os/UserHandle;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-enableSystemApp-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-enableSystemAppWithIntent-(Landroid/content/ComponentName; Landroid/content/Intent;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-enforceCanManageCaCerts-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getAccountTypesWithManagementDisabled-()[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getAccountTypesWithManagementDisabledAsUser-(I)[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getActiveAdmins-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getApplicationRestrictions-(Landroid/content/ComponentName; Ljava/lang/String;)Landroid/os/Bundle;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getAutoTimeRequired-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCameraDisabled-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileCallerIdDisabled-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileCallerIdDisabledForUser-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileWidgetProviders-(Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCurrentFailedPasswordAttempts-(I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getDeviceOwnerName-()Ljava/lang/String;": ["android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getGlobalProxyAdmin-(I)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getKeyguardDisabledFeatures-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getLockTaskPackages-(Landroid/content/ComponentName;)[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getMaximumFailedPasswordsForWipe-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getMaximumTimeToLock-(Landroid/content/ComponentName; I)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordExpiration-(Landroid/content/ComponentName; I)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordExpirationTimeout-(Landroid/content/ComponentName; I)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordHistoryLength-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumLength-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumLetters-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumLowerCase-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumNonLetter-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumNumeric-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumSymbols-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumUpperCase-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordQuality-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedAccessibilityServices-(Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedAccessibilityServicesForUser-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedInputMethods-(Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedInputMethodsForCurrentUser-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getProfileOwnerName-(I)Ljava/lang/String;": ["android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getRemoveWarning-(Landroid/content/ComponentName; Landroid/os/RemoteCallback; I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getRestrictionsProvider-(I)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getScreenCaptureDisabled-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getStorageEncryption-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getStorageEncryptionStatus-(I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getTrustAgentFeaturesEnabled-(Landroid/content/ComponentName; Landroid/content/ComponentName; I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-hasGrantedPolicy-(Landroid/content/ComponentName; I I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-hasUserSetupCompleted-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-installCaCert-(Landroid/content/ComponentName; [B)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-installKeyPair-(Landroid/content/ComponentName; [B [B Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isActivePasswordSufficient-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isAdminActive-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isApplicationHidden-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isLockTaskPermitted-(Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isMasterVolumeMuted-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isUninstallBlocked-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-lockNow-()V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-notifyLockTaskModeChanged-(Z Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-packageHasActiveAdmins-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-removeActiveAdmin-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-removeCrossProfileWidgetProvider-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-removeUser-(Landroid/content/ComponentName; Landroid/os/UserHandle;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportFailedPasswordAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportSuccessfulPasswordAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-resetPassword-(Ljava/lang/String; I I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setAccountManagementDisabled-(Landroid/content/ComponentName; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setActiveAdmin-(Landroid/content/ComponentName; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setActivePasswordState-(I I I I I I I I I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setApplicationHidden-(Landroid/content/ComponentName; Ljava/lang/String; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setApplicationRestrictions-(Landroid/content/ComponentName; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setAutoTimeRequired-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setCameraDisabled-(Landroid/content/ComponentName; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setCrossProfileCallerIdDisabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setDeviceOwner-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setGlobalProxy-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String; I)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setGlobalSetting-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setKeyguardDisabledFeatures-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setLockTaskPackages-(Landroid/content/ComponentName; [Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setMasterVolumeMuted-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setMaximumFailedPasswordsForWipe-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setMaximumTimeToLock-(Landroid/content/ComponentName; J I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordExpirationTimeout-(Landroid/content/ComponentName; J I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordHistoryLength-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumLength-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumLetters-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumLowerCase-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumNonLetter-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumNumeric-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumSymbols-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumUpperCase-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordQuality-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPermittedAccessibilityServices-(Landroid/content/ComponentName; Ljava/util/List;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPermittedInputMethods-(Landroid/content/ComponentName; Ljava/util/List;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setProfileEnabled-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setProfileName-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setProfileOwner-(Landroid/content/ComponentName; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setRecommendedGlobalProxy-(Landroid/content/ComponentName; Landroid/net/ProxyInfo;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setRestrictionsProvider-(Landroid/content/ComponentName; Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setScreenCaptureDisabled-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setSecureSetting-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setStorageEncryption-(Landroid/content/ComponentName; Z I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setTrustAgentFeaturesEnabled-(Landroid/content/ComponentName; Landroid/content/ComponentName; Ljava/util/List; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setUninstallBlocked-(Landroid/content/ComponentName; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setUserRestriction-(Landroid/content/ComponentName; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-switchUser-(Landroid/content/ComponentName; Landroid/os/UserHandle;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-uninstallCaCert-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-wipeData-(I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/display/DisplayManagerService$BinderService;-connectWifiDisplay-(Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-createVirtualDisplay-(Landroid/hardware/display/IVirtualDisplayCallback; Landroid/media/projection/IMediaProjection; Ljava/lang/String; Ljava/lang/String; I I I Landroid/view/Surface; I)I": ["android.permission.CAPTURE_SECURE_VIDEO_OUTPUT", "android.permission.CAPTURE_VIDEO_OUTPUT"], "Lcom/android/server/display/DisplayManagerService$BinderService;-forgetWifiDisplay-(Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-pauseWifiDisplay-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-renameWifiDisplay-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-resumeWifiDisplay-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-startWifiDisplayScan-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-stopWifiDisplayScan-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-awaken-()V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-dream-()V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-getDefaultDreamComponent-()Landroid/content/ComponentName;": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-getDreamComponents-()[Landroid/content/ComponentName;": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-isDreaming-()Z": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-setDreamComponents-([Landroid/content/ComponentName;)V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-testDream-(Landroid/content/ComponentName;)V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/ethernet/EthernetServiceImpl;-getConfiguration-()Landroid/net/IpConfiguration;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ethernet/EthernetServiceImpl;-setConfiguration-(Landroid/net/IpConfiguration;)V": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addDeviceEventListener-(Landroid/hardware/hdmi/IHdmiDeviceEventListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addHdmiMhlVendorCommandListener-(Landroid/hardware/hdmi/IHdmiMhlVendorCommandListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addHotplugEventListener-(Landroid/hardware/hdmi/IHdmiHotplugEventListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addSystemAudioModeChangeListener-(Landroid/hardware/hdmi/IHdmiSystemAudioModeChangeListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addVendorCommandListener-(Landroid/hardware/hdmi/IHdmiVendorCommandListener; I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-canChangeSystemAudioMode-()Z": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-clearTimerRecording-(I I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-deviceSelect-(I Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getActiveSource-()Landroid/hardware/hdmi/HdmiDeviceInfo;": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getInputDevices-()Ljava/util/List;": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getPortInfo-()Ljava/util/List;": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getSupportedTypes-()[I": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getSystemAudioMode-()Z": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-oneTouchPlay-(Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-portSelect-(I Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-queryDisplayStatus-(Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-removeHotplugEventListener-(Landroid/hardware/hdmi/IHdmiHotplugEventListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-removeSystemAudioModeChangeListener-(Landroid/hardware/hdmi/IHdmiSystemAudioModeChangeListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendKeyEvent-(I I Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendMhlVendorCommand-(I I I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendStandby-(I I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendVendorCommand-(I I [B Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setArcMode-(Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setHdmiRecordListener-(Landroid/hardware/hdmi/IHdmiRecordListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setInputChangeListener-(Landroid/hardware/hdmi/IHdmiInputChangeListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setProhibitMode-(Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setSystemAudioMode-(Z Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setSystemAudioMute-(Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setSystemAudioVolume-(I I I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-startOneTouchRecord-(I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-startTimerRecording-(I I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-stopOneTouchRecord-(I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/input/InputManagerService;-addKeyboardLayoutForInputDevice-(Landroid/hardware/input/InputDeviceIdentifier; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-removeKeyboardLayoutForInputDevice-(Landroid/hardware/input/InputDeviceIdentifier; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-setCurrentKeyboardLayoutForInputDevice-(Landroid/hardware/input/InputDeviceIdentifier; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-setTouchCalibrationForInputDevice-(Ljava/lang/String; I Landroid/hardware/input/TouchCalibration;)V": ["android.permission.SET_INPUT_CALIBRATION"], "Lcom/android/server/input/InputManagerService;-tryPointerSpeed-(I)V": ["android.permission.SET_POINTER_SPEED"], "Lcom/android/server/job/JobSchedulerService$JobSchedulerStub;-schedule-(Landroid/app/job/JobInfo;)I": ["android.permission.RECEIVE_BOOT_COMPLETED"], "Lcom/android/server/media/MediaRouterService;-registerClientAsUser-(Landroid/media/IMediaRouterClient; Ljava/lang/String; I)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/media/MediaSessionRecord$SessionStub;-setFlags-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-addCallback-(Landroid/media/projection/IMediaProjectionWatcherCallback;)V": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-createProjection-(I Ljava/lang/String; I Z)Landroid/media/projection/IMediaProjection;": ["android.permission.MANAGE_MEDIA_PROJECTION", "android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-getActiveProjectionInfo-()Landroid/media/projection/MediaProjectionInfo;": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-removeCallback-(Landroid/media/projection/IMediaProjectionWatcherCallback;)V": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-stopActiveProjection-()V": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/net/NetworkPolicyManagerService;-addUidPolicy-(I I)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkPolicies-()[Landroid/net/NetworkPolicy;": ["android.permission.MANAGE_NETWORK_POLICY", "android.permission.READ_PHONE_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkQuotaInfo-(Landroid/net/NetworkState;)Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getPowerSaveAppIdWhitelist-()[I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getRestrictBackground-()Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getUidPolicy-(I)I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getUidsWithPolicy-(I)[I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-isUidForeground-(I)Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-registerListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkPolicyManagerService;-removeUidPolicy-(I I)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setNetworkPolicies-([Landroid/net/NetworkPolicy;)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setRestrictBackground-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setUidPolicy-(I I)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-snoozeLimit-(Landroid/net/NetworkTemplate;)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-unregisterListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkStatsService;-advisePersistThreshold-(J)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-forceUpdate-()V": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-getDataLayerSnapshotForUid-(I)Landroid/net/NetworkStats;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkStatsService;-getNetworkTotalBytes-(Landroid/net/NetworkTemplate; J J)J": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-incrementOperationCount-(I I I)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-openSession-()Landroid/net/INetworkStatsSession;": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-setUidForeground-(I Z)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/pm/PackageInstallerService;-setPermissionsResult-(I Z)V": ["android.permission.INSTALL_PACKAGES"], "Lcom/android/server/pm/PackageInstallerService;-uninstall-(Ljava/lang/String; I Landroid/content/IntentSender; I)V": ["android.permission.DELETE_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-addCrossProfileIntentFilter-(Landroid/content/IntentFilter; Ljava/lang/String; I I I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-addPreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-canForwardTo-(Landroid/content/Intent; Ljava/lang/String; I I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-clearApplicationUserData-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver; I)V": ["android.permission.CLEAR_APP_USER_DATA", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-clearCrossProfileIntentFilters-(I Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-clearPackagePreferredActivities-(Ljava/lang/String;)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-deleteApplicationCacheFiles-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.DELETE_CACHE_FILES"], "Lcom/android/server/pm/PackageManagerService;-deletePackage-(Ljava/lang/String; Landroid/content/pm/IPackageDeleteObserver2; I I)V": ["android.permission.DELETE_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-deletePackageAsUser-(Ljava/lang/String; Landroid/content/pm/IPackageDeleteObserver; I I)V": ["android.permission.DELETE_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-extendVerificationTimeout-(I I J)V": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/pm/PackageManagerService;-freeStorage-(J Landroid/content/IntentSender;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-freeStorageAndNotify-(J Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-getActivityInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ActivityInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getApplicationEnabledSetting-(Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getApplicationHiddenSettingAsUser-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/pm/PackageManagerService;-getApplicationInfo-(Ljava/lang/String; I I)Landroid/content/pm/ApplicationInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getComponentEnabledSetting-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getHomeActivities-(Ljava/util/List;)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getInstalledPackages-(I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getLastChosenActivity-(Landroid/content/Intent; Ljava/lang/String; I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getPackageInfo-(Ljava/lang/String; I I)Landroid/content/pm/PackageInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getPackageSizeInfo-(Ljava/lang/String; I Landroid/content/pm/IPackageStatsObserver;)V": ["android.permission.GET_PACKAGE_SIZE"], "Lcom/android/server/pm/PackageManagerService;-getPackageUid-(Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getProviderInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ProviderInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getReceiverInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ActivityInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getServiceInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ServiceInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getVerifierDeviceIdentity-()Landroid/content/pm/VerifierDeviceIdentity;": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/pm/PackageManagerService;-grantPermission-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-installExistingPackageAsUser-(Ljava/lang/String; I)I": ["android.permission.INSTALL_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-installPackage-(Ljava/lang/String; Landroid/content/pm/IPackageInstallObserver2; I Ljava/lang/String; Landroid/content/pm/VerificationParams; Ljava/lang/String;)V": ["android.permission.INSTALL_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-installPackageAsUser-(Ljava/lang/String; Landroid/content/pm/IPackageInstallObserver2; I Ljava/lang/String; Landroid/content/pm/VerificationParams; Ljava/lang/String; I)V": ["android.permission.INSTALL_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-isPackageAvailable-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-movePackage-(Ljava/lang/String; Landroid/content/pm/IPackageMoveObserver; I)V": ["android.permission.MOVE_PACKAGE"], "Lcom/android/server/pm/PackageManagerService;-queryIntentActivities-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentActivityOptions-(Landroid/content/ComponentName; [Landroid/content/Intent; [Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentContentProviders-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentReceivers-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentServices-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-replacePreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-resetPreferredActivities-(I)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-resolveIntent-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-resolveService-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-revokePermission-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-setApplicationEnabledSetting-(Ljava/lang/String; I I I Ljava/lang/String;)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setApplicationHiddenSettingAsUser-(Ljava/lang/String; Z I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/pm/PackageManagerService;-setBlockUninstallForUser-(Ljava/lang/String; Z I)Z": ["android.permission.DELETE_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-setComponentEnabledSetting-(Landroid/content/ComponentName; I I I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setInstallLocation-(I)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/pm/PackageManagerService;-setLastChosenActivity-(Landroid/content/Intent; Ljava/lang/String; I Landroid/content/IntentFilter; I Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-setPackageStoppedState-(Ljava/lang/String; Z I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setPermissionEnforced-(Ljava/lang/String; Z)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-verifyPendingInstall-(I I)V": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/power/PowerManagerService$BinderService;-acquireWakeLock-(Landroid/os/IBinder; I Ljava/lang/String; Ljava/lang/String; Landroid/os/WorkSource; Ljava/lang/String;)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-acquireWakeLockWithUid-(Landroid/os/IBinder; I Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-crash-(Ljava/lang/String;)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService$BinderService;-goToSleep-(J I I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-nap-(J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-powerHint-(I I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-reboot-(Z Ljava/lang/String; Z)V": ["android.permission.REBOOT", "android.permission.RECOVERY"], "Lcom/android/server/power/PowerManagerService$BinderService;-releaseWakeLock-(Landroid/os/IBinder; I)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-setAttentionLight-(Z I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-setPowerSaveMode-(Z)Z": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-setStayOnSetting-(I)V": ["android.permission.WRITE_SETTINGS"], "Lcom/android/server/power/PowerManagerService$BinderService;-setTemporaryScreenAutoBrightnessAdjustmentSettingOverride-(F)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-setTemporaryScreenBrightnessSettingOverride-(I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-shutdown-(Z Z)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService$BinderService;-updateWakeLockUids-(Landroid/os/IBinder; [I)V": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-updateWakeLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource; Ljava/lang/String;)V": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-userActivity-(J I I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-wakeUp-(J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-addPrintJobStateChangeListener-(Landroid/print/IPrintJobStateChangeListener; I I)V": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-cancelPrintJob-(Landroid/print/PrintJobId; I I)V": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-getPrintJobInfo-(Landroid/print/PrintJobId; I I)Landroid/print/PrintJobInfo;": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-getPrintJobInfos-(I I)Ljava/util/List;": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-print-(Ljava/lang/String; Landroid/print/IPrintDocumentAdapter; Landroid/print/PrintAttributes; Ljava/lang/String; I I)Landroid/os/Bundle;": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-restartPrintJob-(Landroid/print/PrintJobId; I I)V": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/sip/SipService;-close-(Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-createSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/ISipSessionListener;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getListOfProfiles-()[Landroid/net/sip/SipProfile;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getPendingSession-(Ljava/lang/String;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isOpened-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isRegistered-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open-(Landroid/net/sip/SipProfile;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open3-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/ISipSessionListener;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/ISipSessionListener;)V": ["android.permission.USE_SIP"], "Lcom/android/server/statusbar/StatusBarManagerService;-collapsePanels-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-disable-(I Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-expandNotificationsPanel-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-expandSettingsPanel-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-onClearAllNotifications-(I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationClear-(Ljava/lang/String; Ljava/lang/String; I I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationClick-(Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationError-(Ljava/lang/String; Ljava/lang/String; I I I Ljava/lang/String; I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationExpansionChanged-(Ljava/lang/String; Z Z)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationVisibilityChanged-([Ljava/lang/String; [Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onPanelHidden-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onPanelRevealed-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-registerStatusBar-(Lcom/android/internal/statusbar/IStatusBar; Lcom/android/internal/statusbar/StatusBarIconList; [I Ljava/util/List;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-removeIcon-(Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setIcon-(Ljava/lang/String; Ljava/lang/String; I I Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setIconVisibility-(Ljava/lang/String; Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setImeWindowStatus-(Landroid/os/IBinder; I I Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setSystemUiVisibility-(I I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-topAppWindowChanged-(Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/telecom/TelecomServiceImpl;-acceptRingingCall-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-cancelMissedCallsNotification-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-clearAccounts-(Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-endCall-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-getCurrentTtyMode-()I": ["android.permission.READ_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-handlePinMmi-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-isInCall-()Z": ["android.permission.READ_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-isRinging-()Z": ["android.permission.READ_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-isTtySupported-()Z": ["android.permission.READ_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-registerPhoneAccount-(Landroid/telecom/PhoneAccount;)V": ["android.permission.MODIFY_PHONE_STATE", "com.android.server.telecom.permission.REGISTER_PROVIDER_OR_SUBSCRIPTION"], "Lcom/android/server/telecom/TelecomServiceImpl;-setSimCallManager-(Landroid/telecom/PhoneAccountHandle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-setUserSelectedOutgoingPhoneAccount-(Landroid/telecom/PhoneAccountHandle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-showInCallScreen-(Z)V": ["android.permission.READ_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-silenceRinger-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/telecom/TelecomServiceImpl;-unregisterPhoneAccount-(Landroid/telecom/PhoneAccountHandle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-acquireTvInputHardware-(I Landroid/media/tv/ITvInputHardwareCallback; Landroid/media/tv/TvInputInfo; I)Landroid/media/tv/ITvInputHardware;": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-addBlockedRating-(Ljava/lang/String; I)V": ["android.permission.MODIFY_PARENTAL_CONTROLS"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-captureFrame-(Ljava/lang/String; Landroid/view/Surface; Landroid/media/tv/TvStreamConfig; I)Z": ["android.permission.CAPTURE_TV_INPUT"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-getAvailableTvStreamConfigList-(Ljava/lang/String; I)Ljava/util/List;": ["android.permission.CAPTURE_TV_INPUT"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-getHardwareList-()Ljava/util/List;": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-releaseTvInputHardware-(I Landroid/media/tv/ITvInputHardware; I)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-removeBlockedRating-(Ljava/lang/String; I)V": ["android.permission.MODIFY_PARENTAL_CONTROLS"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-setParentalControlsEnabled-(Z I)V": ["android.permission.MODIFY_PARENTAL_CONTROLS"], "Lcom/android/server/tv/TvInputManagerService$ServiceCallback;-addHardwareTvInput-(I Landroid/media/tv/TvInputInfo;)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$ServiceCallback;-addHdmiTvInput-(I Landroid/media/tv/TvInputInfo;)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$ServiceCallback;-removeTvInput-(Ljava/lang/String;)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/usage/UsageStatsService$BinderService;-queryConfigurationStats-(I J J Ljava/lang/String;)Landroid/content/pm/ParceledListSlice;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/usage/UsageStatsService$BinderService;-queryEvents-(J J Ljava/lang/String;)Landroid/app/usage/UsageEvents;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/usage/UsageStatsService$BinderService;-queryUsageStats-(I J J Ljava/lang/String;)Landroid/content/pm/ParceledListSlice;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/usb/UsbService;-allowUsbDebugging-(Z Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-clearDefaults-(Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-clearUsbDebuggingKeys-()V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-denyUsbDebugging-()V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantAccessoryPermission-(Landroid/hardware/usb/UsbAccessory; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantDevicePermission-(Landroid/hardware/usb/UsbDevice; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-hasDefaults-(Ljava/lang/String; I)Z": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setAccessoryPackage-(Landroid/hardware/usb/UsbAccessory; Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setCurrentFunction-(Ljava/lang/String; Z)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setDevicePackage-(Landroid/hardware/usb/UsbDevice; Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setMassStorageBackingFile-(Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-deleteKeyphraseSoundModel-(I Ljava/lang/String;)I": ["android.permission.MANAGE_VOICE_KEYPHRASES"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-getKeyphraseSoundModel-(I Ljava/lang/String;)Landroid/hardware/soundtrigger/SoundTrigger$KeyphraseSoundModel;": ["android.permission.MANAGE_VOICE_KEYPHRASES"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-updateKeyphraseSoundModel-(Landroid/hardware/soundtrigger/SoundTrigger$KeyphraseSoundModel;)I": ["android.permission.MANAGE_VOICE_KEYPHRASES"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setDimensionHints-(I I)V": ["android.permission.SET_WALLPAPER_HINTS"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setDisplayPadding-(Landroid/graphics/Rect;)V": ["android.permission.SET_WALLPAPER_HINTS"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setWallpaper-(Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.SET_WALLPAPER"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setWallpaperComponent-(Landroid/content/ComponentName;)V": ["android.permission.SET_WALLPAPER_COMPONENT"], "Lcom/android/server/wifi/WifiServiceImpl;-acquireMulticastLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-acquireWifiLock-(Landroid/os/IBinder; I Ljava/lang/String; Landroid/os/WorkSource;)Z": ["android.permission.WAKE_LOCK"], "Lcom/android/server/wifi/WifiServiceImpl;-addOrUpdateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-addToBlacklist-(Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-clearBlacklist-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-disconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-enableAggressiveHandover-(I)V": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-enableVerboseLogging-(I)V": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getAggressiveHandover-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getAllowScansWithTraffic-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getBatchedScanResults-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getChannelList-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConfigFile-()Ljava/lang/String;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConnectionStatistics-()Landroid/net/wifi/WifiConnectionStatistics;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.READ_WIFI_CREDENTIAL"], "Lcom/android/server/wifi/WifiServiceImpl;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getFrequencyBand-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getPrivilegedConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.READ_WIFI_CREDENTIAL"], "Lcom/android/server/wifi/WifiServiceImpl;-getScanResults-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getSupportedFeatures-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getVerboseLoggingLevel-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiApConfiguration-()Landroid/net/wifi/WifiConfiguration;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiApEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiServiceMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWpsNfcConfigurationToken-(I)Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-initializeMulticastFiltering-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-isMulticastEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-isScanAlwaysAvailable-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-pollBatchedScan-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-reassociate-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-reconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-releaseMulticastLock-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-releaseWifiLock-(Landroid/os/IBinder;)Z": ["android.permission.WAKE_LOCK"], "Lcom/android/server/wifi/WifiServiceImpl;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-reportActivityInfo-()Landroid/net/wifi/WifiActivityEnergyInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-requestBatchedScan-(Landroid/net/wifi/BatchedScanSettings; Landroid/os/IBinder; Landroid/os/WorkSource;)Z": ["android.permission.CHANGE_WIFI_STATE", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wifi/WifiServiceImpl;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setAllowScansWithTraffic-(I)V": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setCountryCode-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-setFrequencyBand-(I Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setWifiApConfiguration-(Landroid/net/wifi/WifiConfiguration;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setWifiApEnabled-(Landroid/net/wifi/WifiConfiguration; Z)V": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CHANGE_WIFI_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-setWifiEnabled-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-startScan-(Landroid/net/wifi/ScanSettings; Landroid/os/WorkSource;)V": ["android.permission.CHANGE_WIFI_STATE", "android.permission.LOCATION_HARDWARE", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wifi/WifiServiceImpl;-startWifi-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-stopBatchedScan-(Landroid/net/wifi/BatchedScanSettings;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-stopWifi-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-updateWifiLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wifi/p2p/WifiP2pServiceImpl;-getMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/p2p/WifiP2pServiceImpl;-getP2pStateMachineMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE", "android.permission.CONNECTIVITY_INTERNAL", "android.permission.LOCATION_HARDWARE"], "Lcom/android/server/wifi/p2p/WifiP2pServiceImpl;-setMiracastMode-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wm/WindowManagerService;-addAppToken-(I Landroid/view/IApplicationToken; I I I Z Z I I Z Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-addWindowToken-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-clearForcedDisplayDensity-(I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-clearForcedDisplaySize-(I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-clearWindowContentFrameStats-(Landroid/os/IBinder;)Z": ["android.permission.FRAME_STATS"], "Lcom/android/server/wm/WindowManagerService;-disableKeyguard-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-dismissKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-executeAppTransition-()V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-exitKeyguardSecurely-(Landroid/view/IOnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-freezeRotation-(I)V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-getWindowContentFrameStats-(Landroid/os/IBinder;)Landroid/view/WindowContentFrameStats;": ["android.permission.FRAME_STATS"], "Lcom/android/server/wm/WindowManagerService;-isViewServerRunning-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-keyguardGoingAway-(Z Z)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-pauseKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-prepareAppTransition-(I Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-reenableKeyguard-(Landroid/os/IBinder;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-removeAppToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-removeWindowToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-resumeKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-screenshotApplications-(Landroid/os/IBinder; I I I Z)Landroid/graphics/Bitmap;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScale-(I F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScales-([F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAppGroupId-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppOrientation-(Landroid/view/IApplicationToken; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppStartingWindow-(Landroid/os/IBinder; Ljava/lang/String; I Landroid/content/res/CompatibilityInfo; Ljava/lang/CharSequence; I I I I Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppVisibility-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppWillBeHidden-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setEventDispatching-(Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setFocusedApp-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setForcedDisplayDensity-(I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setForcedDisplaySize-(I I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setNewConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setOverscan-(I I I I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-startAppFreezingScreen-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-startFreezingScreen-(I I)V": ["android.permission.FREEZE_SCREEN"], "Lcom/android/server/wm/WindowManagerService;-startViewServer-(I)Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-statusBarVisibilityChanged-(I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/wm/WindowManagerService;-stopAppFreezingScreen-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-stopFreezingScreen-()V": ["android.permission.FREEZE_SCREEN"], "Lcom/android/server/wm/WindowManagerService;-stopViewServer-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-thawRotation-()V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-updateOrientationFromAppTokens-(Landroid/content/res/Configuration; Landroid/os/IBinder;)Landroid/content/res/Configuration;": ["android.permission.MANAGE_APP_TOKENS"], "Landroid/accounts/AccountAuthenticatorActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountManager;-addAccountExplicitly-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-addOnAccountsUpdatedListener-(Landroid/accounts/OnAccountsUpdateListener; Landroid/os/Handler; Z)V": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-clearPassword-(Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManager;-getAccounts-()[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-getAccountsByType-(Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-getPassword-(Landroid/accounts/Account;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-getUserData-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-invalidateAuthToken-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MANAGE_ACCOUNTS", "android.permission.USE_CREDENTIALS"], "Landroid/accounts/AccountManager;-peekAuthToken-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setAuthToken-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setPassword-(Landroid/accounts/Account; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setUserData-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/app/Activity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityManager;-getRecentTasks-(I I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-getRunningTasks-(I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-killBackgroundProcesses-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I)V": ["android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-restartPackage-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/AliasActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/KeyguardManager$KeyguardLock;-disableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager$KeyguardLock;-reenableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager;-exitKeyguardSecurely-(Landroid/app/KeyguardManager$OnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/ListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-clear-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setBitmap-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setResource-(I)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setStream-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-suggestDesiredDimensions-(I I)V": ["android.permission.SET_WALLPAPER_HINTS"], "Landroid/app/backup/BackupAgentHelper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/bluetooth/BluetoothA2dp;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-closeProfileProxy-(I Landroid/bluetooth/BluetoothProfile;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-disable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-enable-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBluetoothLeAdvertiser-()Landroid/bluetooth/le/BluetoothLeAdvertiser;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBluetoothLeScanner-()Landroid/bluetooth/le/BluetoothLeScanner;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBondedDevices-()Ljava/util/Set;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getProfileProxy-(Landroid/content/Context; Landroid/bluetooth/BluetoothProfile$ServiceListener; I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isMultipleAdvertisementSupported-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isOffloadedFilteringSupported-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isOffloadedScanBatchingSupported-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingInsecureRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-startDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-startLeScan-([Ljava/util/UUID; Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-startLeScan-(Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-stopLeScan-(Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-connectGatt-(Landroid/content/Context; Z Landroid/bluetooth/BluetoothGattCallback;)Landroid/bluetooth/BluetoothGatt;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-createBond-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-createInsecureRfcommSocketToServiceRecord-(Ljava/util/UUID;)Landroid/bluetooth/BluetoothSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-createRfcommSocketToServiceRecord-(Ljava/util/UUID;)Landroid/bluetooth/BluetoothSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-fetchUuidsWithSdp-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBluetoothClass-()Landroid/bluetooth/BluetoothClass;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBondState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getType-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-setPairingConfirmation-(Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-setPin-([B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothGatt;-abortReliableWrite-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-abortReliableWrite-(Landroid/bluetooth/BluetoothDevice;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-beginReliableWrite-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-close-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-connect-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-disconnect-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-discoverServices-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-executeReliableWrite-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readCharacteristic-(Landroid/bluetooth/BluetoothGattCharacteristic;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readDescriptor-(Landroid/bluetooth/BluetoothGattDescriptor;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readRemoteRssi-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-requestConnectionPriority-(I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-requestMtu-(I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-setCharacteristicNotification-(Landroid/bluetooth/BluetoothGattCharacteristic; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-writeCharacteristic-(Landroid/bluetooth/BluetoothGattCharacteristic;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-writeDescriptor-(Landroid/bluetooth/BluetoothGattDescriptor;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-addService-(Landroid/bluetooth/BluetoothGattService;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-cancelConnection-(Landroid/bluetooth/BluetoothDevice;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-clearServices-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-close-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-connect-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-notifyCharacteristicChanged-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothGattCharacteristic; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-removeService-(Landroid/bluetooth/BluetoothGattService;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-sendResponse-(Landroid/bluetooth/BluetoothDevice; I I I [B)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-isAudioConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-sendVendorSpecificResultCode-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHeadset;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHeadset;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHealth;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-registerSinkAppConfiguration-(Ljava/lang/String; I Landroid/bluetooth/BluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getConnectedDevices-(I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getConnectionState-(Landroid/bluetooth/BluetoothDevice; I)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getDevicesMatchingConnectionStates-(I [I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-openGattServer-(Landroid/content/Context; Landroid/bluetooth/BluetoothGattServerCallback;)Landroid/bluetooth/BluetoothGattServer;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothSocket;-connect-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeAdvertiser;-startAdvertising-(Landroid/bluetooth/le/AdvertiseSettings; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeAdvertiser;-startAdvertising-(Landroid/bluetooth/le/AdvertiseSettings; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeAdvertiser;-stopAdvertising-(Landroid/bluetooth/le/AdvertiseCallback;)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/le/BluetoothLeScanner;-flushPendingScanResults-(Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeScanner;-startScan-(Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeScanner;-startScan-(Ljava/util/List; Landroid/bluetooth/le/ScanSettings; Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeScanner;-stopScan-(Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/content/ContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/hardware/ConsumerIrManager;-getCarrierFrequencies-()[Landroid/hardware/ConsumerIrManager$CarrierFrequencyRange;": ["android.permission.TRANSMIT_IR"], "Landroid/hardware/ConsumerIrManager;-transmit-(I [I)V": ["android.permission.TRANSMIT_IR"], "Landroid/inputmethodservice/InputMethodService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/location/LocationManager;-addGpsStatusListener-(Landroid/location/GpsStatus$Listener;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addNmeaListener-(Landroid/location/GpsStatus$NmeaListener;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addProximityAlert-(D D F J Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addTestProvider-(Ljava/lang/String; Z Z Z Z Z Z Z I I)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderEnabled-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderLocation-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderStatus-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getLastKnownLocation-(Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProvider-(Ljava/lang/String;)Landroid/location/LocationProvider;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeProximityAlert-(Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeTestProvider-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-removeUpdates-(Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeUpdates-(Landroid/location/LocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Landroid/location/LocationManager;-setTestProviderEnabled-(Ljava/lang/String; Z)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-setTestProviderLocation-(Ljava/lang/String; Landroid/location/Location;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-setTestProviderStatus-(Ljava/lang/String; I Landroid/os/Bundle; J)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/media/AsyncPlayer;-play-(Landroid/content/Context; Landroid/net/Uri; Z I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/AsyncPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/AudioManager;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setMicrophoneMute-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setMode-(I)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-startBluetoothSco-()V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-stopBluetoothSco-()V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/MediaPlayer;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-reset-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-setWakeMode-(Landroid/content/Context; I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-play-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-setAudioAttributes-(Landroid/media/AudioAttributes;)V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-setStreamType-(I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(Landroid/content/Context; Landroid/net/Uri;)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(I)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-stopPreviousRingtone-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/ConnectivityManager;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getAllNetworks-()[Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getLinkProperties-(Landroid/net/Network;)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkCapabilities-(Landroid/net/Network;)Landroid/net/NetworkCapabilities;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkInfo-(Landroid/net/Network;)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-registerNetworkCallback-(Landroid/net/NetworkRequest; Landroid/net/ConnectivityManager$NetworkCallback;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-reportBadNetwork-(Landroid/net/Network;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET"], "Landroid/net/ConnectivityManager;-requestNetwork-(Landroid/net/NetworkRequest; Landroid/net/ConnectivityManager$NetworkCallback;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-requestRouteToHost-(I I)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-startUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/VpnService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/sip/SipAudioCall;-close-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-endCall-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-setSpeakerMode-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/net/sip/SipAudioCall;-startAudio-()V": ["android.permission.ACCESS_WIFI_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/sip/SipManager;-close-(Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-createSipSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipSession$Listener;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-getSessionFor-(Landroid/content/Intent;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isOpened-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isRegistered-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipProfile; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Ljava/lang/String; Ljava/lang/String; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-register-(Landroid/net/sip/SipProfile; I Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-takeAudioCall-(Landroid/content/Intent; Landroid/net/sip/SipAudioCall$Listener;)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-unregister-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/wifi/WifiManager$MulticastLock;-acquire-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$MulticastLock;-release-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$WifiLock;-acquire-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager$WifiLock;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager;-addNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disconnect-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getScanResults-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getWifiState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-is5GHzBandSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isDeviceToApRttSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isEnhancedPowerReportingSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isP2pSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isPreferredNetworkOffloadSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isScanAlwaysAvailable-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isTdlsSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isWifiEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reassociate-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reconnect-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-setWifiEnabled-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-startScan-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-updateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/p2p/WifiP2pManager;-initialize-(Landroid/content/Context; Landroid/os/Looper; Landroid/net/wifi/p2p/WifiP2pManager$ChannelListener;)Landroid/net/wifi/p2p/WifiP2pManager$Channel;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/nfc/NfcAdapter;-disableForegroundDispatch-(Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-disableForegroundNdefPush-(Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-enableForegroundDispatch-(Landroid/app/Activity; Landroid/app/PendingIntent; [Landroid/content/IntentFilter; [L[java/lang/String;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-enableForegroundNdefPush-(Landroid/app/Activity; Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-invokeBeam-(Landroid/app/Activity;)Z": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setBeamPushUris-([Landroid/net/Uri; Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setBeamPushUrisCallback-(Landroid/nfc/NfcAdapter$CreateBeamUrisCallback; Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setNdefPushMessage-(Landroid/nfc/NdefMessage; Landroid/app/Activity; [Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setNdefPushMessageCallback-(Landroid/nfc/NfcAdapter$CreateNdefMessageCallback; Landroid/app/Activity; [Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/NfcAdapter;-setOnNdefPushCompleteCallback-(Landroid/nfc/NfcAdapter$OnNdefPushCompleteCallback; Landroid/app/Activity; [Landroid/app/Activity;)V": ["android.permission.NFC"], "Landroid/nfc/cardemulation/CardEmulation;-getAidsForService-(Landroid/content/ComponentName; Ljava/lang/String;)Ljava/util/List;": ["android.permission.NFC"], "Landroid/nfc/cardemulation/CardEmulation;-isDefaultServiceForAid-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Landroid/nfc/cardemulation/CardEmulation;-isDefaultServiceForCategory-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Landroid/nfc/cardemulation/CardEmulation;-registerAidsForService-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/util/List;)Z": ["android.permission.NFC"], "Landroid/nfc/cardemulation/CardEmulation;-removeAidsForService-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Landroid/nfc/cardemulation/CardEmulation;-setPreferredService-(Landroid/app/Activity; Landroid/content/ComponentName;)Z": ["android.permission.NFC"], "Landroid/nfc/cardemulation/CardEmulation;-unsetPreferredService-(Landroid/app/Activity;)Z": ["android.permission.NFC"], "Landroid/nfc/tech/BasicTagTechnology;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/BasicTagTechnology;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/IsoDep;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-authenticateSectorWithKeyA-(I [B)Z": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-authenticateSectorWithKeyB-(I [B)Z": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-decrement-(I I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-increment-(I I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-readBlock-(I)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-restore-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-transfer-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareClassic;-writeBlock-(I [B)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-readPages-(I)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/MifareUltralight;-writePage-(I [B)V": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-getNdefMessage-()Landroid/nfc/NdefMessage;": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-makeReadOnly-()Z": ["android.permission.NFC"], "Landroid/nfc/tech/Ndef;-writeNdefMessage-(Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-format-(Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/tech/NdefFormatable;-formatReadOnly-(Landroid/nfc/NdefMessage;)V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcA;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/NfcB;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcB;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcB;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/NfcBarcode;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcBarcode;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-getTimeout-()I": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-setTimeout-(I)V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcF;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/nfc/tech/NfcV;-close-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcV;-connect-()V": ["android.permission.NFC"], "Landroid/nfc/tech/NfcV;-transceive-([B)[B": ["android.permission.NFC"], "Landroid/os/PowerManager$WakeLock;-acquire-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-acquire-(J)V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-release-(I)V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-setWorkSource-(Landroid/os/WorkSource;)V": ["android.permission.WAKE_LOCK"], "Landroid/os/SystemVibrator;-cancel-()V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(I Ljava/lang/String; [J I Landroid/media/AudioAttributes;)V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(I Ljava/lang/String; J Landroid/media/AudioAttributes;)V": ["android.permission.VIBRATE"], "Landroid/service/dreams/DreamService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/service/dreams/DreamService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/dreams/DreamService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/voice/VoiceInteractionService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/service/voice/VoiceInteractionService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/voice/VoiceInteractionService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/telecom/TelecomManager;-isInCall-()Z": ["android.permission.READ_PHONE_STATE"], "Landroid/telecom/TelecomManager;-showInCallScreen-(Z)V": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/PhoneNumberUtils;-isVoiceMailNumber-(Ljava/lang/String;)Z": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/SmsManager;-divideMessage-(Ljava/lang/String;)Ljava/util/ArrayList;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/SmsManager;-downloadMultimediaMessage-(Landroid/content/Context; Ljava/lang/String; Landroid/net/Uri; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.RECEIVE_MMS"], "Landroid/telephony/SmsManager;-sendDataMessage-(Ljava/lang/String; Ljava/lang/String; S [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendMultimediaMessage-(Landroid/content/Context; Landroid/net/Uri; Ljava/lang/String; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendMultipartTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/util/ArrayList; Ljava/util/ArrayList; Ljava/util/ArrayList;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/TelephonyManager;-getAllCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getCellLocation-()Landroid/telephony/CellLocation;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getDeviceId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getDeviceSoftwareVersion-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getGroupIdLevel1-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getLine1Number-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getNeighboringCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getSimSerialNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getSubscriberId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailAlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-listen-(Landroid/telephony/PhoneStateListener; I)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE"], "Landroid/telephony/gsm/SmsManager;-divideMessage-(Ljava/lang/String;)Ljava/util/ArrayList;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/gsm/SmsManager;-sendDataMessage-(Ljava/lang/String; Ljava/lang/String; S [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/gsm/SmsManager;-sendMultipartTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/util/ArrayList; Ljava/util/ArrayList; Ljava/util/ArrayList;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/gsm/SmsManager;-sendTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/test/IsolatedContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-sendStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-sendStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-sendStickyOrderedBroadcast-(Landroid/content/Intent; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-sendStickyOrderedBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle; Landroid/content/BroadcastReceiver; Landroid/os/Handler; I Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/widget/VideoView;-getAudioSessionId-()I": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-onKeyDown-(I Landroid/view/KeyEvent;)Z": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-resume-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoPath-(Ljava/lang/String;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoURI-(Landroid/net/Uri;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoURI-(Landroid/net/Uri; Ljava/util/Map;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-stopPlayback-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-suspend-()V": ["android.permission.WAKE_LOCK"]}
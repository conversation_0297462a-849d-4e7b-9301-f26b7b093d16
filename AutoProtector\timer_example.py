#!/usr/bin/env python3
"""
Example of how the Timer class works in AutoProtector
This shows what the timing output will look like during APK protection.
"""

import time
import logging

# Setup logging to see the timer output
logging.basicConfig(level=logging.INFO, format='[%(levelname)s] %(message)s')
Logger = logging.getLogger("timer_demo")

class Timer:
    """Timer utility class for tracking operation durations"""
    
    def __init__(self, name="Operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.stages = {}
        self.current_stage = None
        self.stage_start = None
    
    def start(self):
        """Start the overall timer"""
        self.start_time = time.time()
        Logger.info(f"⏱️  Starting {self.name}")
        return self
    
    def stage(self, stage_name):
        """Start timing a specific stage"""
        current_time = time.time()
        
        # End previous stage if exists
        if self.current_stage and self.stage_start:
            duration = current_time - self.stage_start
            self.stages[self.current_stage] = duration
            Logger.info(f"✅ {self.current_stage} completed in {self._format_duration(duration)}")
        
        # Start new stage
        self.current_stage = stage_name
        self.stage_start = current_time
        Logger.info(f"🔄 Starting {stage_name}...")
        return self
    
    def end(self):
        """End the overall timer and show summary"""
        self.end_time = time.time()
        
        # End current stage if exists
        if self.current_stage and self.stage_start:
            duration = self.end_time - self.stage_start
            self.stages[self.current_stage] = duration
            Logger.info(f"✅ {self.current_stage} completed in {self._format_duration(duration)}")
        
        # Show overall summary
        total_duration = self.end_time - self.start_time
        Logger.info(f"🏁 {self.name} completed in {self._format_duration(total_duration)}")
        
        # Show stage breakdown if there are stages
        if self.stages:
            Logger.info("📊 Stage breakdown:")
            for stage_name, stage_duration in self.stages.items():
                percentage = (stage_duration / total_duration) * 100
                Logger.info(f"   • {stage_name}: {self._format_duration(stage_duration)} ({percentage:.1f}%)")
        
        return total_duration
    
    def _format_duration(self, seconds):
        """Format duration in a human-readable way"""
        if seconds < 1:
            return f"{seconds*1000:.0f}ms"
        elif seconds < 60:
            return f"{seconds:.1f}s"
        else:
            minutes = int(seconds // 60)
            remaining_seconds = seconds % 60
            return f"{minutes}m {remaining_seconds:.1f}s"

def demo_protection_timing():
    """Demo showing what the timer output looks like during APK protection"""
    
    # Simulate APK protection process
    timer = Timer("APK Protection").start()
    
    # Stage 1: DEX Analysis
    timer.stage("DEX Analysis & Method Compilation")
    time.sleep(2.5)  # Simulate 2.5 seconds of DEX analysis
    
    # Stage 2: Project Setup
    timer.stage("Project Setup & File Preparation")
    time.sleep(0.8)  # Simulate 0.8 seconds of project setup
    
    # Stage 3: Native Compilation
    timer.stage("Native Library Compilation")
    time.sleep(4.2)  # Simulate 4.2 seconds of NDK build
    
    # Stage 4: APK Processing
    timer.stage("APK Decompilation & Processing")
    time.sleep(1.5)  # Simulate 1.5 seconds of APK processing
    
    # Stage 5: APK Building
    timer.stage("APK Compilation & Packaging")
    time.sleep(2.1)  # Simulate 2.1 seconds of APK building
    
    # Stage 6: APK Signing
    timer.stage("APK Signing")
    time.sleep(0.3)  # Simulate 0.3 seconds of signing
    
    # End timer and show summary
    total_time = timer.end()
    
    print(f"\n🎉 Protection completed! Total time: {total_time:.1f} seconds")

if __name__ == "__main__":
    print("🚀 AutoProtector Timer Demo")
    print("=" * 50)
    demo_protection_timing()

#!/usr/bin/env python
# coding=utf-8
import argparse
import os
import re
import sys
import json
import io
import zipfile

from os import path
from logging import getLogger, INFO
from androguard.core import androconf
from androguard.core.analysis import analysis
from androguard.core.androconf import show_logging
from androguard.core.bytecodes import apk, dvm
from androguard.util import read
from dex2c.compiler import Dex2C
from dex2c.util import (
    JniLongName,
    get_method_triple,
    get_access_method,
    is_synthetic_method,
    is_native_method,
)
from subprocess import check_call, STDOUT, run
from random import choice
from string import ascii_letters, digits
from shutil import copy, move, make_archive, rmtree, copytree
from concurrent.futures import Thread<PERSON>oolExecutor, ProcessPoolExecutor, as_completed
import time

# Import configuration manager
try:
    from config_manager import config
except ImportError:
    print("Warning: config_manager not found, using legacy configuration")
    config = None

current_dir = os.path.dirname(os.path.abspath(__file__))

# Use configuration manager if available, otherwise fall back to legacy paths
if config:
    APKTOOL = config.get_apktool_jar()
    SIGNJAR = config.get_apksigner_jar()
    MANIFEST_EDITOR = config.get_manifest_editor_jar()
    JAVA_EXECUTABLE = config.get_java_executable()
    NDKBUILD = config.get_ndk_build_command()
else:
    # Legacy configuration
    tools_dir = os.path.join(current_dir, "tools")
    APKTOOL = os.path.join(tools_dir, "apktool.jar")
    SIGNJAR = os.path.join(tools_dir, "apksigner.jar")
    MANIFEST_EDITOR = os.path.join(tools_dir, "manifest-editor.jar")
    JAVA_EXECUTABLE = os.path.join(current_dir, "..", "..", "work_files", "Java", "jdk-21", "bin", "java.exe")
    NDKBUILD = os.path.join(current_dir, "..", "..", "work_files", "SDK", "ndk", "26.3.11579264", "ndk-build.cmd")

# Legacy tool paths for compatibility
tools_dir = os.path.join(current_dir, "tools")
APKTOOL2 = os.path.join(tools_dir, "apktool.bat")
APKTOOL3 = os.path.join(tools_dir, "apktool")

SKIP_SYNTHETIC_METHODS = False
IGNORE_APP_LIB_ABIS = False
Logger = getLogger("dcc")

# Global variable to track the current project directory for cleanup
current_project_dir = None


class Timer:
    """Timer utility class for tracking operation durations"""

    def __init__(self, name="Operation"):
        self.name = name
        self.start_time = None
        self.end_time = None
        self.stages = {}
        self.current_stage = None
        self.stage_start = None

    def start(self):
        """Start the overall timer"""
        self.start_time = time.time()
        Logger.info(f"⏱️  Starting {self.name}")
        return self

    def stage(self, stage_name):
        """Start timing a specific stage"""
        current_time = time.time()

        # End previous stage if exists
        if self.current_stage and self.stage_start:
            duration = current_time - self.stage_start
            self.stages[self.current_stage] = duration
            Logger.info(f"✅ {self.current_stage} completed in {self._format_duration(duration)}")

        # Start new stage
        self.current_stage = stage_name
        self.stage_start = current_time
        Logger.info(f"🔄 Starting {stage_name}...")
        return self

    def end(self):
        """End the overall timer and show summary"""
        self.end_time = time.time()

        # End current stage if exists
        if self.current_stage and self.stage_start:
            duration = self.end_time - self.stage_start
            self.stages[self.current_stage] = duration
            Logger.info(f"✅ {self.current_stage} completed in {self._format_duration(duration)}")

        # Show overall summary
        total_duration = self.end_time - self.start_time
        Logger.info(f"🏁 {self.name} completed in {self._format_duration(total_duration)}")

        # Show stage breakdown if there are stages
        if self.stages:
            Logger.info("📊 Stage breakdown:")
            for stage_name, stage_duration in self.stages.items():
                percentage = (stage_duration / total_duration) * 100
                Logger.info(f"   • {stage_name}: {self._format_duration(stage_duration)} ({percentage:.1f}%)")

        return total_duration

    def _format_duration(self, seconds):
        """Format duration in a human-readable way"""
        if seconds < 1:
            return f"{seconds*1000:.0f}ms"
        elif seconds < 60:
            return f"{seconds:.1f}s"
        else:
            minutes = int(seconds // 60)
            remaining_seconds = seconds % 60
            return f"{minutes}m {remaining_seconds:.1f}s"

    def get_total_duration(self):
        """Get total duration if timer has ended"""
        if self.start_time and self.end_time:
            return self.end_time - self.start_time
        return None


def is_windows():
    return os.name == "nt"


def cpu_count():
    num_processes = os.cpu_count()
    if num_processes is None:
        num_processes = 2
    return num_processes


# n
def create_tmp_directory():
    Logger.info("Creating .tmp folder")

    # Use configuration manager if available
    if config:
        tmp_dir = config.get_temp_directory()
        config.create_directories()  # This creates all necessary directories
    else:
        tmp_dir = ".tmp"
        if not path.exists(tmp_dir):
            os.mkdir(tmp_dir)


# n
def get_random_str(length=8):
    characters = ascii_letters + digits
    result = "".join(choice(characters) for i in range(length))
    return result


# n
# def make_temp_dir(prefix="dcc"):
#     # NEW LOGIC: Create unique directories based on prefix
#     import string
#
#     # For project directories, use C:\z
#     if prefix.startswith("dcc-project"):
#         tmp = "C:\\z"
#     # For other directories, use unique short paths
#     else:
#         # Try single letters first: a, b, c, ..., z (skip 'z' as it's reserved for project)
#         for letter in string.ascii_lowercase:
#             if letter == 'z':  # Skip 'z' as it's reserved for project
#                 continue
#             tmp = f"C:\\{letter}"
#             if not path.exists(tmp):
#                 # Directory doesn't exist, create and use it
#                 os.makedirs(tmp, exist_ok=True)
#                 Logger.info(f"Created temporary directory: {tmp}")
#                 return tmp
#             elif path.exists(tmp) and len(os.listdir(tmp)) == 0:
#                 # Directory exists but is empty, use it
#                 Logger.info(f"Using existing empty directory: {tmp}")
#                 return tmp
#
#         # If all letters are taken, use letter + number combinations
#         for letter in string.ascii_lowercase:
#             if letter == 'z':  # Skip 'z' as it's reserved for project
#                 continue
#             for num in range(1, 10):
#                 tmp = f"C:\\{letter}{num}"
#                 if not path.exists(tmp):
#                     os.makedirs(tmp, exist_ok=True)
#                     Logger.info(f"Created temporary directory: {tmp}")
#                     return tmp
#                 elif path.exists(tmp) and len(os.listdir(tmp)) == 0:
#                     Logger.info(f"Using existing empty directory: {tmp}")
#                     return tmp
#
#     # Force clear the directory before use (for project dir or fallback)
#     if path.exists(tmp):
#         try:
#             Logger.info(f"Force clearing existing directory: {tmp}")
#             rmtree(tmp)
#         except OSError:
#             try:
#                 if is_windows():
#                     result = run(["rd", "/s", "/q", tmp], shell=True, capture_output=True, text=True)
#                     if result.returncode != 0:
#                         Logger.warning(f"Failed to force clear directory {tmp}: {result.stderr}")
#                 else:
#                     result = run(["rm", "-rf", tmp], shell=True, capture_output=True, text=True)
#                     if result.returncode != 0:
#                         Logger.warning(f"Failed to force clear directory {tmp}: {result.stderr}")
#             except Exception as e:
#                 Logger.warning(f"Failed to force clear directory {tmp}: {str(e)}")
#
#     # Create fresh directory
#     os.makedirs(tmp, exist_ok=True)
#     Logger.info(f"Created temporary directory: {tmp}")
#     return tmp

def make_temp_dir(prefix="dcc"):
    # NEW LOGIC: Handle specific prefixes with workspace-relative paths
    current_dir = os.path.dirname(os.path.abspath(__file__))

    # For project directories, use C:\z (keep for path length issues)
    if prefix.startswith("dcc-project"):
        tmp = "C:\\z"
        # Force clear the directory before use
        if path.exists(tmp):
            try:
                Logger.info(f"Force clearing existing directory: {tmp}")
                rmtree(tmp)
            except OSError:
                try:
                    if is_windows():
                        result = run(["rd", "/s", "/q", tmp], shell=True, capture_output=True, text=True)
                        if result.returncode != 0:
                            Logger.warning(f"Failed to force clear directory {tmp}: {result.stderr}")
                    else:
                        result = run(["rm", "-rf", tmp], shell=True, capture_output=True, text=True)
                        if result.returncode != 0:
                            Logger.warning(f"Failed to force clear directory {tmp}: {result.stderr}")
                except Exception as e:
                    Logger.warning(f"Failed to force clear directory {tmp}: {str(e)}")

        # Create fresh directory
        os.makedirs(tmp, exist_ok=True)
        Logger.info(f"Created temporary directory: {tmp}")
        return tmp

    # For apktool, use workspace temp
    elif prefix.startswith("dcc-apktool"):
        workspace_temp = path.join(current_dir, "..", "workspace", "temp")
        tmp = path.join(workspace_temp, "apktool")
        os.makedirs(tmp, exist_ok=True)
        Logger.info(f"Created temporary directory: {tmp}")
        return tmp

    # For jni backup, use workspace temp
    elif prefix.startswith("jni"):
        workspace_temp = path.join(current_dir, "..", "workspace", "temp")
        tmp = path.join(workspace_temp, "jni_backup")
        os.makedirs(tmp, exist_ok=True)
        Logger.info(f"Created temporary directory: {tmp}")
        return tmp

    # For any other prefix, raise an error (no other prefixes should exist)
    else:
        raise Exception(f"Unknown temp directory prefix: {prefix}")

    # OLD LOGIC: Find shortest available directory path (COMMENTED OUT)
    # import string
    #
    # # Try single letters first: a, b, c, ..., z
    # for letter in string.ascii_lowercase:
    #     tmp = f"C:\\{letter}"
    #     if not path.exists(tmp):
    #         # Directory doesn't exist, create and use it
    #         os.makedirs(tmp, exist_ok=True)
    #         return tmp
    #     elif path.exists(tmp) and len(os.listdir(tmp)) == 0:
    #         # Directory exists but is empty, use it
    #         return tmp
    #
    # # Try letter + number combinations: a1, a2, ..., a9, b1, b2, ..., z9
    # for letter in string.ascii_lowercase:
    #     for num in range(1, 10):
    #         tmp = f"C:\\{letter}{num}"
    #         if not path.exists(tmp):
    #             # Directory doesn't exist, create and use it
    #             os.makedirs(tmp, exist_ok=True)
    #             return tmp
    #         elif path.exists(tmp) and len(os.listdir(tmp)) == 0:
    #             # Directory exists but is empty, use it
    #             return tmp
    #
    # # Fallback to original method if all short paths are occupied
    # base_tmp = ".tmp"
    # random_str = get_random_str()
    # tmp = path.join(base_tmp, prefix + random_str)
    # while path.exists(tmp) and path.isdir(tmp):
    #     random_str = get_random_str()
    #     tmp = path.join(base_tmp, prefix + random_str)
    # os.mkdir(tmp)
    # tmp = os.path.normpath(tmp)
    # return tmp


# n
def make_temp_file(suffix=""):
    # Use configuration manager if available
    if config:
        base_tmp = config.get_temp_directory()
    else:
        base_tmp = ".tmp"

    random_str = get_random_str()
    tmp = path.join(base_tmp, random_str + suffix)

    while path.exists(tmp) and path.isfile(tmp):
        random_str = get_random_str()
        tmp = path.join(base_tmp, random_str + suffix)

    with open(tmp, "w") as f:
        pass  # Just create empty file

    return tmp


def modify_application_name(manifest_path, custom_loader):
    from xml.etree import ElementTree as ET

    ET.register_namespace("android", "http://schemas.android.com/apk/res/android")

    with open(manifest_path, "r") as f:
        file_contents = f.read()

    manifest_start = file_contents.index("<manifest")
    before_manifest = file_contents[:manifest_start]

    root = ET.fromstring(file_contents[manifest_start:])

    application = root.find("application")
    if "{http://schemas.android.com/apk/res/android}name" in application.attrib:
        application.attrib["{http://schemas.android.com/apk/res/android}name"] = (
            custom_loader
        )
    else:
        application.set("android:name", custom_loader)

    if (
        "{http://schemas.android.com/apk/res/android}extractNativeLibs"
        in application.attrib
    ):
        application.attrib[
            "{http://schemas.android.com/apk/res/android}extractNativeLibs"
        ] = "true"

    xml_str = ET.tostring(root, encoding="utf-8").decode()
    output = before_manifest + xml_str

    with open(manifest_path, "w") as f:
        f.write(output)


# n
def clean_tmp_directory(specific_dir=None):
    if specific_dir:
        # Clean specific directory (for new short path system)
        try:
            Logger.info(f"Removing {specific_dir} folder")
            rmtree(specific_dir)
        except OSError:
            try:
                if is_windows():
                    result = run(["rd", "/s", "/q", specific_dir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to remove directory {specific_dir}: {result.stderr}")
                else:
                    result = run(["rm", "-rf", specific_dir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to remove directory {specific_dir}: {result.stderr}")
            except Exception as e:
                Logger.warning(f"Failed to remove directory {specific_dir}: {str(e)}")
    else:
        # Use configuration manager if available (legacy cleanup)
        if config:
            tmpdir = config.get_temp_directory()
        else:
            tmpdir = ".tmp"

        try:
            Logger.info(f"Removing {tmpdir} folder")
            rmtree(tmpdir)
        except OSError:
            try:
                if is_windows():
                    result = run(["rd", "/s", "/q", tmpdir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to remove temp directory {tmpdir}: {result.stderr}")
                else:
                    result = run(["rm", "-rf", tmpdir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to remove temp directory {tmpdir}: {result.stderr}")
            except Exception as e:
                Logger.warning(f"Failed to remove temp directory {tmpdir}: {str(e)}")


# n
def force_cleanup_cz_directory():
    """Force cleanup of C:\\z directory after protection"""
    cz_dir = "C:\\z"
    if path.exists(cz_dir):
        try:
            Logger.info(f"Force cleaning up C:\\z directory after protection")
            rmtree(cz_dir)
        except OSError:
            try:
                if is_windows():
                    result = run(["rd", "/s", "/q", cz_dir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to force cleanup C:\\z directory: {result.stderr}")
                else:
                    result = run(["rm", "-rf", cz_dir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to force cleanup C:\\z directory: {result.stderr}")
            except Exception as e:
                Logger.warning(f"Failed to force cleanup C:\\z directory: {str(e)}")
    else:
        Logger.info("C:\\z directory does not exist, no cleanup needed")


def cleanup_workspace_temp_directories():
    """Clean up workspace temp directories after protection"""
    current_dir = os.path.dirname(os.path.abspath(__file__))
    workspace_temp = path.join(current_dir, "..", "workspace", "temp")

    # Clean workspace temp directories
    temp_dirs = [
        path.join(workspace_temp, "apktool"),
        path.join(workspace_temp, "jni_backup")
    ]

    for temp_dir in temp_dirs:
        if path.exists(temp_dir):
            try:
                Logger.info(f"Cleaning workspace temp directory: {temp_dir}")
                rmtree(temp_dir)
            except OSError:
                try:
                    if is_windows():
                        result = run(["rd", "/s", "/q", temp_dir], shell=True, capture_output=True, text=True)
                        if result.returncode != 0:
                            Logger.warning(f"Failed to cleanup workspace temp directory {temp_dir}: {result.stderr}")
                    else:
                        result = run(["rm", "-rf", temp_dir], shell=True, capture_output=True, text=True)
                        if result.returncode != 0:
                            Logger.warning(f"Failed to cleanup workspace temp directory {temp_dir}: {result.stderr}")
                except Exception as e:
                    Logger.warning(f"Failed to cleanup workspace temp directory {temp_dir}: {str(e)}")
        else:
            Logger.info(f"Workspace temp directory does not exist: {temp_dir}")

    # Clean .tmp folder in AutoProtector
    tmp_dir = path.join(current_dir, ".tmp")
    if path.exists(tmp_dir):
        try:
            Logger.info(f"Cleaning AutoProtector .tmp directory: {tmp_dir}")
            rmtree(tmp_dir)
        except OSError:
            try:
                if is_windows():
                    result = run(["rd", "/s", "/q", tmp_dir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to cleanup .tmp directory {tmp_dir}: {result.stderr}")
                else:
                    result = run(["rm", "-rf", tmp_dir], shell=True, capture_output=True, text=True)
                    if result.returncode != 0:
                        Logger.warning(f"Failed to cleanup .tmp directory {tmp_dir}: {result.stderr}")
            except Exception as e:
                Logger.warning(f"Failed to cleanup .tmp directory {tmp_dir}: {str(e)}")
    else:
        Logger.info(f"AutoProtector .tmp directory does not exist: {tmp_dir}")


def ensure_clean_temp_directories():
    """Ensure temp directories are clean before starting protection"""
    Logger.info("Checking and cleaning temp directories before starting protection")
    current_dir = os.path.dirname(os.path.abspath(__file__))
    workspace_temp = path.join(current_dir, "..", "workspace", "temp")

    # Check and clean workspace temp directories
    temp_dirs = [
        path.join(workspace_temp, "apktool"),
        path.join(workspace_temp, "jni_backup")
    ]

    for temp_dir in temp_dirs:
        if path.exists(temp_dir) and os.listdir(temp_dir):
            Logger.info(f"Found non-empty temp directory, cleaning: {temp_dir}")
            try:
                rmtree(temp_dir)
                os.makedirs(temp_dir, exist_ok=True)
            except Exception as e:
                Logger.warning(f"Failed to clean temp directory {temp_dir}: {str(e)}")
        else:
            Logger.info(f"Temp directory is clean or doesn't exist: {temp_dir}")

    # Check and clean .tmp folder in AutoProtector
    tmp_dir = path.join(current_dir, ".tmp")
    if path.exists(tmp_dir) and os.listdir(tmp_dir):
        Logger.info(f"Found non-empty .tmp directory, cleaning: {tmp_dir}")
        try:
            rmtree(tmp_dir)
            os.makedirs(tmp_dir, exist_ok=True)
        except Exception as e:
            Logger.warning(f"Failed to clean .tmp directory {tmp_dir}: {str(e)}")
    else:
        Logger.info(f".tmp directory is clean or doesn't exist: {tmp_dir}")


class ApkTool(object):
    @staticmethod
    def decompile(apk):
        outdir = make_temp_dir("dcc-apktool-")

        # Use configuration manager if available
        if config:
            apktool_jar = config.get_apktool_jar()
            java_exe = config.get_java_executable()
            # Simple selective decompilation: only main DEX classes (avoids assets DEX conflicts)
            command = [java_exe, "-jar", apktool_jar, "d", "--only-main-classes", "-f", "-o", outdir, apk]
        else:
            # Legacy configuration with selective flag
            if is_windows():
                command = [APKTOOL2, "d", "--only-main-classes", "-f", "-o", outdir, apk]
            else:
                command = ["bash", APKTOOL3, "d", "--only-main-classes", "-f", "-o", outdir, apk]

        try:
            check_call(command, stderr=STDOUT)
            Logger.info(f"Selective decompilation completed - main DEX classes only (assets DEX skipped)")
            return outdir
        except Exception as e:
            Logger.error(f"APK decompilation failed: {str(e)}")
            raise

    @staticmethod
    def compile(decompiled_dir):
        unsiged_apk = make_temp_file("-unsigned.apk")

        # Use configuration manager if available
        if config:
            apktool_jar = config.get_apktool_jar()
            java_exe = config.get_java_executable()
            command = [java_exe, "-jar", apktool_jar, "b", "--advanced", "-o", unsiged_apk, decompiled_dir]
        else:
            # Legacy configuration
            if is_windows():
                command = [APKTOOL2, "b", "--advanced", "-o", unsiged_apk, decompiled_dir]
            else:
                command = ["bash", APKTOOL3, "b", "--advanced", "-o", unsiged_apk, decompiled_dir]

        try:
            check_call(command, stderr=STDOUT)
            return unsiged_apk
        except Exception as e:
            Logger.error(f"APK compilation failed: {str(e)}")
            raise


# n
def change_min_sdk(command=list(), min_sdk="21", update_existing=True):
    if "--min-sdk-version" in command:
        if update_existing:
            min_sdk_value_index = command.index("--min-sdk-version") + 1
            command[min_sdk_value_index] = min_sdk
        else:
            return
    else:
        command.append("--min-sdk-version")
        command.append(min_sdk)


# n
def change_max_sdk(command=list(), max_sdk="33", update_existing=True):
    if "--max-sdk-version" in command:
        if update_existing:
            max_sdk_value_index = command.index("--max-sdk-version") + 1
            command[max_sdk_value_index] = max_sdk
        else:
            return
    else:
        command.append("--max-sdk-version")
        command.append(max_sdk)


# n
def zipalign(input_apk, output_apk):
    Logger.info(f"Zipaligning {input_apk} -> {output_apk}")

    # Use configuration manager if available
    if config:
        zipalign_exe = config.get_zipalign_executable()
    else:
        zipalign_exe = os.path.join(os.path.dirname(__file__), "tools", "zipalign.exe")

    command = [
        zipalign_exe,
        "-p",
        "-f",
        "4",
        input_apk,
        output_apk,
    ]

    try:
        check_call(command, stderr=STDOUT)
    except Exception as ex:
        Logger.error("Zipaligning %s failed!" % input_apk, exc_info=True)
        print(f"{str(ex)}")


def sign(unsigned_apk, signed_apk):
    Logger.info(f"Signing {unsigned_apk} -> {signed_apk}")

    # Use configuration manager if available
    if config:
        keystore = config.get_keystore_path()
        keystore_pass = config.get_keystore_password()
        alias = config.get_keystore_alias()
        alias_pass = config.get_keystore_alias_password()
        # For now, use simple signing (v1 only)
        signature = {
            "v1_enabled": True,
            "v2_enabled": False,
            "v3_enabled": False,
            "keystore_pass": keystore_pass,
            "store_pass": alias_pass,
            "alias": alias
        }
    else:
        # Legacy configuration
        with open("dcc.cfg") as fp:
            dcc_cfg = json.load(fp)
            signature = dcc_cfg["signature"]
            keystore = signature["keystore_path"]

    if (
        signature["v1_enabled"] is False
        and signature["v2_enabled"] is False
        and signature["v3_enabled"] is False
    ):
        Logger.warning("At least one signing scheme should be enabled from v1, v2 & v3")
        move_unsigned(unsigned_apk, signed_apk)
        return

    if not path.exists(keystore) or not path.isfile(keystore):
        Logger.error("KeyStore not found in defined path or not recognized as a file")
        move_unsigned(unsigned_apk, signed_apk)
        return

    command = [
        JAVA_EXECUTABLE,
        "-jar",
        SIGNJAR,
        "sign",
        "--in",
        unsigned_apk,
        "--out",
        signed_apk,
        "--ks",
        keystore,
        "--ks-key-alias",
        signature["alias"],
        "--ks-pass",
        "pass:" + signature["keystore_pass"],
        "--key-pass",
        "pass:" + signature["store_pass"],
    ]

    command.append("--v1-signing-enabled")
    command.append("true" if signature["v1_enabled"] is True else "false")
    command.append("--v2-signing-enabled")
    command.append("true" if signature["v2_enabled"] is True else "false")
    command.append("--v3-signing-enabled")
    command.append("true" if signature["v3_enabled"] is True else "false")
    command.append("--v4-signing-enabled")
    command.append("false")

    if signature["v1_enabled"] is True:
        change_min_sdk(command, "21")
        change_max_sdk(command, "23")
        command.append("--v1-signer-name")
        command.append("ANDROID")

    if signature["v2_enabled"] is True:
        change_min_sdk(command, "24", False)
        change_max_sdk(command, "26")

    if signature["v3_enabled"] is True:
        change_min_sdk(command, "28", False)
        change_max_sdk(command, "29")

    try:
        check_call(command, stderr=STDOUT)
    except Exception as ex:
        Logger.error("Signing %s failed!" % unsigned_apk, exc_info=True)
        print(f"{str(ex)}")
        move_unsigned(unsigned_apk, signed_apk)


def move_unsigned(unsigned_apk, signed_apk):
    Logger.info("Moving unsigned apk -> " + signed_apk)
    copy(unsigned_apk, signed_apk)


def build_project(project_dir):
    # Use maximum parallelization for NDK build
    max_jobs = cpu_count() * 2  # Use 2x CPU cores for I/O bound tasks
    ndk_command = [NDKBUILD, f"-j{max_jobs}", "-C", project_dir]
    Logger.info(f"Running NDK build command with {max_jobs} parallel jobs: {' '.join(ndk_command)}")

    # Check if NDK build executable exists
    if not path.exists(NDKBUILD):
        raise Exception(f"NDK build executable not found: {NDKBUILD}")

    # Check if project directory exists
    if not path.exists(project_dir):
        raise Exception(f"Project directory not found: {project_dir}")

    # Check if Android.mk exists
    android_mk = path.join(project_dir, "jni", "Android.mk")
    if not path.exists(android_mk):
        raise Exception(f"Android.mk not found: {android_mk}")

    check_call(ndk_command, stderr=STDOUT)


def auto_vm(filename):
    ret = androconf.is_android(filename)
    dex_files = list()

    if ret == "APK":
        for dex in apk.APK(filename).get_all_dex():
            dex_files.append(dvm.DalvikVMFormat(dex))

    elif ret == "DEX" or ret == "DEY":
        dex_files.append(dvm.DalvikVMFormat(read(filename)))

    else:
        raise Exception("Unsupported file %s" % filename)

    return dex_files


def get_sorted_dex_names(apk_obj):
    """Get DEX names sorted in proper numerical order (classes.dex, classes2.dex, classes3.dex, ...)"""
    import re
    dex_names = list(apk_obj.get_dex_names())

    def dex_sort_key(name):
        # Extract number from classes[N].dex, treat classes.dex as 1
        match = re.match(r'classes(\d+)?\.dex', name)
        if match:
            num = match.group(1)
            return int(num) if num else 1
        return 0

    return sorted(dex_names, key=dex_sort_key)


def load_specific_dex_files(filename, target_indices):
    """Load only specific DEX files from an APK by their indices"""
    ret = androconf.is_android(filename)
    dex_files = list()

    if ret == "APK":
        apk_obj = apk.APK(filename)
        dex_names = get_sorted_dex_names(apk_obj)

        Logger.info(f"Available DEX files (sorted): {dex_names}")

        for i in target_indices:
            if i < len(dex_names):
                dex_name = dex_names[i]
                Logger.info(f"Loading DEX file: {dex_name} (index {i})")
                dex_data = apk_obj.get_file(dex_name)
                dex_files.append(dvm.DalvikVMFormat(dex_data))
            else:
                Logger.error(f"DEX index {i} not found in APK (only {len(dex_names)} DEX files available)")

    elif ret == "DEX" or ret == "DEY":
        if 0 in target_indices:
            dex_files.append(dvm.DalvikVMFormat(read(filename)))

    else:
        raise Exception("Unsupported file %s" % filename)

    return dex_files


class MethodFilter(object):
    def __init__(self, configure, vm):
        self._compile_filters = []
        self._keep_filters = []
        self._compile_full_match = set()

        self.conflict_methods = set()
        self.native_methods = set()
        self.annotated_methods = set()

        self._load_filter_configure(configure)
        self._init_conflict_methods(vm)
        self._init_native_methods(vm)
        self._init_annotation_methods(vm)

    def _load_filter_configure(self, configure):
        if not path.exists(configure):
            return

        with open(configure) as fp:
            for line in fp:
                line = line.strip()
                if not line or line[0] == "#":
                    continue

                if line[0] == "!":
                    line = line[1:].strip()
                    self._keep_filters.append(re.compile(line))
                elif line[0] == "=":
                    line = line[1:].strip()
                    self._compile_full_match.add(line)
                else:
                    self._compile_filters.append(re.compile(line))

    def _init_conflict_methods(self, vm):
        all_methods = {}
        for m in vm.get_methods():
            method_triple = get_method_triple(m, return_type=False)
            if method_triple in all_methods:
                self.conflict_methods.add(m)
                self.conflict_methods.add(all_methods[method_triple])
            else:
                all_methods[method_triple] = m

    def _init_native_methods(self, vm):
        for m in vm.get_methods():
            cls_name, name, _ = get_method_triple(m)

            access = get_access_method(m.get_access_flags())
            if "native" in access:
                self.native_methods.add((cls_name, name))

    def _add_annotation_method(self, method):
        if not is_synthetic_method(method) and not is_native_method(method):
            self.annotated_methods.add(method)

    def _load_constructor_config(self):
        """Load constructor protection settings from external config file"""
        import json
        import os

        config_path = os.path.join(os.path.dirname(__file__), "constructor_config.json")
        default_config = {
            "protect_init": False,    # Instance constructors (<init>)
            "protect_clinit": False   # Static constructors (<clinit>)
        }

        try:
            if os.path.exists(config_path):
                with open(config_path, 'r') as f:
                    config = json.load(f)
                    # Merge with defaults to ensure all keys exist
                    default_config.update(config)
            return default_config
        except Exception as e:
            Logger.warning(f"Failed to load constructor config: {e}, using defaults")
            return default_config

    def _init_annotation_methods(self, vm):
        for c in vm.get_classes():
            adi_off = c.get_annotations_off()
            if adi_off == 0:
                continue

            adi = vm.CM.get_obj_by_offset(adi_off)
            annotated_class = False
            # ref:https://github.com/androguard/androguard/issues/175
            if adi.get_class_annotations_off() != 0:
                ann_set_item = vm.CM.get_obj_by_offset(adi.get_class_annotations_off())
                for aoffitem in ann_set_item.get_annotation_off_item():
                    annotation_item = vm.CM.get_obj_by_offset(
                        aoffitem.get_annotation_off()
                    )
                    encoded_annotation = annotation_item.get_annotation()
                    type_desc = vm.CM.get_type(encoded_annotation.get_type_idx())
                    if type_desc.endswith("Dex2C;"):
                        annotated_class = True
                        for method in c.get_methods():
                            self._add_annotation_method(method)
                        break

            if not annotated_class:
                for mi in adi.get_method_annotations():
                    method = vm.get_method_by_idx(mi.get_method_idx())
                    ann_set_item = vm.CM.get_obj_by_offset(mi.get_annotations_off())

                    for aoffitem in ann_set_item.get_annotation_off_item():
                        annotation_item = vm.CM.get_obj_by_offset(
                            aoffitem.get_annotation_off()
                        )
                        encoded_annotation = annotation_item.get_annotation()
                        type_desc = vm.CM.get_type(encoded_annotation.get_type_idx())
                        if type_desc.endswith("Dex2C;"):
                            self._add_annotation_method(method)

    def should_compile(self, method):
        # don't compile functions that have same parameter but differ return type
        if method in self.conflict_methods:
            return False

        # synthetic method
        if is_synthetic_method(method) and SKIP_SYNTHETIC_METHODS:
            return False

        # native method
        if is_native_method(method):
            return False

        method_triple = get_method_triple(method)
        cls_name, name, _ = method_triple

        # Load constructor protection settings from external config
        constructor_config = self._load_constructor_config()

        # Skip static constructor based on config
        if name == "<clinit>" and not constructor_config.get("protect_clinit", False):
            return False

        # Skip instance constructor based on config
        if name == "<init>" and not constructor_config.get("protect_init", False):
            return False

        # Android VM may find the wrong method using short jni name
        # don't compile function if there is a same named native method
        if (cls_name, name) in self.native_methods:
            return False

        full_name = "".join(method_triple)
        for rule in self._keep_filters:
            if rule.search(full_name):
                return False

        if full_name in self._compile_full_match:
            return True

        if method in self.annotated_methods:
            return True

        for rule in self._compile_filters:
            if rule.search(full_name):
                return True

        return False


def copy_compiled_libs(project_dir, decompiled_dir):
    compiled_libs_dir = path.join(project_dir, "libs")
    decompiled_libs_dir = path.join(decompiled_dir, "lib")

    Logger.info(f"Copying compiled libraries from {compiled_libs_dir} to {decompiled_libs_dir}")

    if not path.exists(compiled_libs_dir):
        Logger.error(f"Compiled libs directory not found: {compiled_libs_dir}")
        Logger.error("NDK build may have failed or libs were not generated")
        # List contents of project_dir to help debug
        if path.exists(project_dir):
            Logger.info(f"Contents of project directory {project_dir}: {os.listdir(project_dir)}")
        return

    Logger.info(f"Found compiled libs directory: {compiled_libs_dir}")
    Logger.info(f"Available ABIs: {os.listdir(compiled_libs_dir)}")

    if not path.exists(decompiled_libs_dir):
        Logger.info(f"Decompiled lib directory doesn't exist, copying entire libs directory")
        copytree(compiled_libs_dir, decompiled_libs_dir)
        return

    Logger.info(f"Processing ABIs in decompiled lib directory: {os.listdir(decompiled_libs_dir)}")

    for abi in os.listdir(decompiled_libs_dir):
        Logger.info(f"Processing ABI: {abi}")
        dst = path.join(decompiled_libs_dir, abi)
        src = path.join(compiled_libs_dir, abi)
        if not path.exists(src) and abi == "armeabi":
            src = path.join(compiled_libs_dir, "armeabi-v7a")
            Logger.warning("Use armeabi-v7a for armeabi")

        if not path.exists(src):
            if IGNORE_APP_LIB_ABIS:
                continue
            else:
                raise Exception("ABI %s is not supported!" % abi)
        # n
        android_mk_filename = path.join(project_dir, "jni", "Android.mk")
        local_module_value = None
        with open(android_mk_filename, "r") as android_mk_file:
            for line in android_mk_file:
                if line.startswith("LOCAL_MODULE"):
                    _, local_module_value = line.split(":=", 1)
                    local_module_value = local_module_value.strip()
                    break

        libnc = path.join(src, "lib" + local_module_value + ".so")
        if not path.exists(libnc):
            Logger.error(f"Native library not found: {libnc}")
            Logger.error(f"Expected library name: lib{local_module_value}.so")
            Logger.error(f"Available files in {src}: {os.listdir(src) if path.exists(src) else 'Directory does not exist'}")
            raise Exception(f"Native library lib{local_module_value}.so not found in {src}")

        Logger.info(f"Copying native library: {libnc} -> {dst}")
        copy(libnc, dst)


def native_class_methods(smali_path, compiled_methods):
    def next_line():
        return fp.readline()

    def handle_annotanion():
        while True:
            line = next_line()
            if not line:
                break
            s = line.strip()
            code_lines.append(line)
            if s == ".end annotation":
                break
            else:
                continue

    def handle_method_body():
        while True:
            line = next_line()
            if not line:
                break
            s = line.strip()
            if s == ".end method":
                break
            elif s.startswith(".annotation runtime") and s.find("Dex2C") < 0:
                code_lines.append(line)
                handle_annotanion()
            else:
                continue

    code_lines = []
    class_name = ""
    with open(smali_path, "r") as fp:
        while True:
            line = next_line()
            if not line:
                break
            code_lines.append(line)
            line = line.strip()
            if line.startswith(".class"):
                class_name = line.split(" ")[-1]
            elif line.startswith(".method"):
                current_method = line.split(" ")[-1]
                param = current_method.find("(")
                name, proto = current_method[:param], current_method[param:]
                if (class_name, name, proto) in compiled_methods:
                    if line.find(" native ") < 0:
                        code_lines[-1] = code_lines[-1].replace(
                            current_method, "native " + current_method
                        )
                    handle_method_body()
                    code_lines.append(".end method\n")

    with open(smali_path, "w") as fp:
        fp.writelines(code_lines)


def native_compiled_dexes(decompiled_dir, compiled_methods):
    # smali smali_classes2 smali_classes3 ...
    classes_output = list(
        filter(lambda x: x.find("smali") >= 0, os.listdir(decompiled_dir))
    )
    todo = []
    for classes in classes_output:
        for method_triple in compiled_methods.keys():
            cls_name, name, proto = method_triple
            cls_name = cls_name[1:-1]  # strip L;
            smali_path = path.join(decompiled_dir, classes, cls_name) + ".smali"
            if path.exists(smali_path):
                todo.append(smali_path)

    for smali_path in todo:
        native_class_methods(smali_path, compiled_methods)


def write_compiled_methods(project_dir, compiled_methods):
    source_dir = path.join(project_dir, "jni", "nc")
    if not path.exists(source_dir):
        os.makedirs(source_dir)

    for method_triple, code in compiled_methods.items():
        full_name = JniLongName(*method_triple)
        filepath = path.join(source_dir, full_name) + ".cpp"
        if path.exists(filepath):
            Logger.warning("Overwrite file %s %s" % (filepath, method_triple))

        try:
            with open(filepath, "w", encoding="utf-8") as fp:
                fp.write('#include "Dex2C.h"\n' + code)
        except Exception as e:
            print(f"{str(e)}\n")

    with open(
        path.join(source_dir, "compiled_methods.txt"), "w", encoding="utf-8"
    ) as fp:
        fp.write("\n".join(list(map("".join, compiled_methods.keys()))))


def archive_compiled_code(project_dir):
    outfile = make_temp_file("-dcc")
    outfile = make_archive(outfile, "zip", project_dir)
    return outfile


def compile_dex(apkfile, filtercfg, obfus, dynamic_register, target_dex_indices=None, target_dex_mode=None):
    dex_analysis = analysis.Analysis()

    X_native_method_prototype = {}
    X_compiled_method_code = {}
    X_errors = []

    # Handle different targeting modes
    if target_dex_mode == "last":
        # First, we need to know how many DEX files exist without loading them all
        apk_obj = apk.APK(apkfile)
        dex_names = get_sorted_dex_names(apk_obj)
        if len(dex_names) > 0:
            last_index = len(dex_names) - 1
            target_dex_indices = [last_index]
            Logger.info(f"Auto-targeting the last DEX file: #{last_index+1} ({dex_names[last_index]})")
        else:
            Logger.error("No DEX files found in APK")
            return {}, {}, ["No DEX files found"]

    # Load only the specific DEX files we need
    if target_dex_indices is not None:
        Logger.info(f"Loading only targeted DEX files: {target_dex_indices}")
        dex_files = load_specific_dex_files(apkfile, target_dex_indices)
        for i in target_dex_indices:
            Logger.info(f"Targeting DEX file #{i+1} (classes{i+1 if i > 0 else ''}.dex) for protection")
    else:
        # If no targeting specified, load all DEX files (original behavior)
        Logger.info("No targeting specified, loading all DEX files")
        dex_files = auto_vm(apkfile)

    # Add the selected DEX files to analysis
    for dex in dex_files:
        dex_analysis.add(dex)

    for dex in dex_files:
        method_filter = MethodFilter(filtercfg, dex)

        compiler = Dex2C(dex, dex_analysis, obfus, dynamic_register)

        native_method_prototype = {}
        compiled_method_code = {}
        errors = []

        # Count methods for performance tracking
        methods_to_compile_count = 0
        for m in dex.get_methods():
            method_triple = get_method_triple(m)
            jni_longname = JniLongName(*method_triple)
            full_name = "".join(method_triple)

            if len(jni_longname) > 244:
                Logger.debug("Name too long %s(> 244) %s" % (jni_longname, full_name))
                continue

            if method_filter.should_compile(m):
                methods_to_compile_count += 1

        if methods_to_compile_count > 0:
            compilation_start = time.time()
            Logger.info(f"Compiling {methods_to_compile_count} methods (optimized for small datasets)")

        # Single-threaded compilation (optimal for small method counts)
        for m in dex.get_methods():
            method_triple = get_method_triple(m)

            jni_longname = JniLongName(*method_triple)
            full_name = "".join(method_triple)

            if len(jni_longname) > 244:
                Logger.debug("Name too long %s(> 244) %s" % (jni_longname, full_name))
                continue

            if method_filter.should_compile(m):
                Logger.debug("compiling %s" % (full_name))
                try:
                    code = compiler.get_source_method(m)
                except Exception as e:
                    Logger.warning(
                        "compile method failed:%s (%s)" % (full_name, str(e)),
                        exc_info=True,
                    )
                    errors.append("%s:%s" % (full_name, str(e)))
                    X_errors.extend(errors)
                    continue

                if code[0]:
                    compiled_method_code[method_triple] = code[0]
                    native_method_prototype[jni_longname] = code[1]
                    X_native_method_prototype.update(native_method_prototype)
                    X_compiled_method_code.update(compiled_method_code)

        # Log compilation performance
        if methods_to_compile_count > 0:
            compilation_time = time.time() - compilation_start
            methods_per_second = len(compiled_method_code) / compilation_time if compilation_time > 0 else 0
            Logger.info(f"Compiled {len(compiled_method_code)} methods in {compilation_time:.1f}s ({methods_per_second:.1f} methods/sec)")

    return X_compiled_method_code, X_native_method_prototype, X_errors


def is_apk(name):
    return name.endswith(".apk")


# n
def get_heap_size():
    return


# n
def get_application_name_from_manifest(apk_file):
    a = apk.APK(apk_file)
    manifest_data = a.get_android_manifest_xml()
    application_element = manifest_data.find("application")
    application_name = application_element.get(
        "{http://schemas.android.com/apk/res/android}name", ""
    )
    # If application_name is not a full qualified name but a relative name (.SketchApplication)
    if application_name.startswith("."):
        application_name = a.package + application_name
    return application_name


# n
def get_smali_folders(decompiled_dir):
    folders = os.listdir(decompiled_dir)
    folders = [
        folder
        for folder in folders
        if path.isdir(path.join(decompiled_dir, folder)) and folder.startswith("smali")
    ]
    return folders


# n
def get_application_class_file(decompiled_dir, smali_folders, application_name):
    if not application_name == "":
        fileName = application_name.replace(".", os.sep) + ".smali"

        for smali_folder in smali_folders:
            filePath = path.join(decompiled_dir, smali_folder, fileName)

            if path.exists(filePath):
                return filePath

    return ""


# n
def backup_jni_project_folder():
    Logger.info("Backing up jni folder")

    src_path = path.join("project", "jni")
    if not path.exists(src_path):
        Logger.error(f"Source JNI folder not found: {src_path}")
        return None

    dest_path = make_temp_dir("jni-")

    try:
        copytree(src_path, dest_path, dirs_exist_ok=True)
        Logger.info(f"JNI folder backed up to: {dest_path}")
        return dest_path
    except Exception as e:
        Logger.error(f"Failed to backup JNI folder: {str(e)}")
        return None


# n
def restore_jni_project_folder(src_path):
    if src_path is None:
        Logger.warning("Cannot restore JNI folder: backup path is None")
        return

    Logger.info("Restoring jni folder")

    dest_path = path.join("project", "jni")

    try:
        if path.exists(dest_path) and path.isdir(dest_path):
            rmtree(dest_path)

        copytree(src_path, dest_path)
        Logger.info(f"JNI folder restored from: {src_path}")
    except Exception as e:
        Logger.error(f"Failed to restore JNI folder: {str(e)}")


# n
def adjust_application_mk(apkfile):
    Logger.info("Adjusting Application.mk file using available abis from apk")

    supported_abis = {"armeabi-v7a", "arm64-v8a", "x86_64", "x86"}
    depreacated_abis = {"armeabi"}
    available_abis = set()

    if is_apk(apkfile):
        zip_file = zipfile.ZipFile(io.BytesIO(bytearray(read(apkfile))), mode="r")

        for file_name in zip_file.namelist():
            if file_name.startswith("lib/"):
                abi_name = file_name.split("/")[1].strip()

                if len(file_name.split("/")) <= 2:
                    continue

                if abi_name in supported_abis:
                    available_abis.add(abi_name)
                elif abi_name in depreacated_abis:
                    Logger.warning(
                        "ABI 'armeabi' is depreacated, using 'armeabi-v7a' instead"
                    )
                    available_abis.add("armeabi-v7a")
                else:
                    raise Exception(
                        f"ABI '{abi_name}' is unsupported, please remove it from apk or use flag --force-keep-libs and try again"
                    )

        if len(available_abis) == 0:
            Logger.info(
                "No lib abis found in apk, using the ones defined in Application.mk file"
            )
            return

        application_mk_path = "project/jni/Application.mk"
        temp_application_mk_path = make_temp_file("-application.mk")

        with open(application_mk_path, "r") as application_mk_file:
            with open(temp_application_mk_path, "w") as temp_application_mk_file:
                for line in application_mk_file:
                    if line.startswith("APP_ABI"):
                        line = "APP_ABI := " + " ".join(available_abis) + "\n"
                    temp_application_mk_file.write(line)

        os.remove(application_mk_path)
        copy(temp_application_mk_path, application_mk_path)
    else:
        raise Exception(f"{apkfile} is not an apk file")


def adjust_application_mk_temp(apkfile, project_dir):
    """Adjust Application.mk file in the temp project directory using available abis from apk"""
    Logger.info("Adjusting Application.mk file using available abis from apk")

    supported_abis = {"armeabi-v7a", "arm64-v8a", "x86_64", "x86"}
    depreacated_abis = {"armeabi"}
    available_abis = set()

    if is_apk(apkfile):
        zip_file = zipfile.ZipFile(io.BytesIO(bytearray(read(apkfile))), mode="r")

        for file_name in zip_file.namelist():
            if file_name.startswith("lib/"):
                abi_name = file_name.split("/")[1].strip()

                if len(file_name.split("/")) <= 2:
                    continue

                if abi_name in supported_abis:
                    available_abis.add(abi_name)
                elif abi_name in depreacated_abis:
                    Logger.warning(
                        "ABI 'armeabi' is depreacated, using 'armeabi-v7a' instead"
                    )
                    available_abis.add("armeabi-v7a")
                else:
                    raise Exception(
                        f"ABI '{abi_name}' is unsupported, please remove it from apk or use flag --force-keep-libs and try again"
                    )

        if len(available_abis) == 0:
            Logger.info(
                "No lib abis found in apk, using the ones defined in Application.mk file"
            )
            return

        # Use the temp project directory instead of the original
        application_mk_path = path.join(project_dir, "jni", "Application.mk")
        temp_application_mk_path = make_temp_file("-application.mk")

        with open(application_mk_path, "r") as application_mk_file:
            with open(temp_application_mk_path, "w") as temp_application_mk_file:
                for line in application_mk_file:
                    if line.startswith("APP_ABI"):
                        line = "APP_ABI := " + " ".join(available_abis) + "\n"
                    temp_application_mk_file.write(line)

        os.remove(application_mk_path)
        copy(temp_application_mk_path, application_mk_path)
    else:
        raise Exception(f"{apkfile} is not an apk file")


def write_dummy_dynamic_register(project_dir):
    source_dir = os.path.join(project_dir, "jni", "nc")
    if not os.path.exists(source_dir):
        os.makedirs(source_dir)
    filepath = os.path.join(source_dir, "DynamicRegister.cpp")
    with open(filepath, "w", encoding="utf-8") as fp:
        fp.write(
            '#include "DynamicRegister.h"\n\nconst char *dynamic_register_compile_methods(JNIEnv *env) { return nullptr; }'
        )


def write_dynamic_register(project_dir, compiled_methods, method_prototypes):
    source_dir = os.path.join(project_dir, "jni", "nc")
    if not os.path.exists(source_dir):
        os.makedirs(source_dir)
    export_list = {}
    # Make export list
    for method_triple in sorted(compiled_methods.keys()):
        full_name = JniLongName(*method_triple)
        if not full_name in method_prototypes:
            raise Exception("Method %s prototype info could not be found" % full_name)
        class_path = method_triple[0][1:-1].replace(".", "/")
        method_name = method_triple[1]
        method_signature = method_triple[2]
        method_native_name = full_name
        method_native_prototype = method_prototypes[full_name]
        if not class_path in export_list:
            export_list[class_path] = []  # methods

        export_list[class_path].append(
            (method_name, method_signature, method_native_name, method_native_prototype)
        )
    if len(export_list) == 0:
        Logger.info("No export methods")
        return

    # Generate extern block and export block
    extern_block = []
    export_block = ["\njclass clazz;\n"]
    export_block_template = 'clazz = env->FindClass("%s");\nif (clazz == nullptr)\n    return "Class not found: %s";\n'
    export_block_template += "const JNINativeMethod export_method_%d[] = {\n%s\n};\n"
    export_block_template += "env->RegisterNatives(clazz, export_method_%d, %d);\n"
    export_block_template += "env->DeleteLocalRef(clazz);\n"
    for index, class_path in enumerate(sorted(export_list.keys())):
        methods = export_list[class_path]
        extern_block.append("\n".join(["extern %s;" % method[3] for method in methods]))

        export_methods = ",\n".join(
            [
                '{"%s", "%s", (void *)%s}' % (method[0], method[1], method[2])
                for method in methods
            ]
        )
        export_block.append(
            export_block_template
            % (class_path, class_path, index, export_methods, index, len(methods))
        )
    export_block.append("return nullptr;\n")
    # Write DynamicRegister.cpp
    filepath = os.path.join(source_dir, "DynamicRegister.cpp")
    with open(filepath, "w", encoding="utf-8") as fp:
        fp.write('#include "DynamicRegister.h"\n\n')
        fp.write("\n".join(extern_block))
        fp.write("\n\nconst char *dynamic_register_compile_methods(JNIEnv *env) {")
        fp.write("\n".join(export_block))
        fp.write("}")


# n
def dcc_main(
    apkfile,
    obfus,
    filtercfg,
    custom_loader,
    outapk,
    do_compile=True,
    project_dir=None,
    source_archive="project-source.zip",
    dynamic_register=False,
    disable_signing=False,
    target_dex_indices=None,
    target_dex_mode=None,
):
    # Start overall timer
    timer = Timer("APK Protection").start()

    if not path.exists(apkfile):
        Logger.error("Input apk file %s does not exist", apkfile)
        return

    if not outapk:
        Logger.error("\033[31mOutput file name required\n\033[0m")
        return

    if custom_loader.rfind(".") == -1:
        Logger.error(
            "\n[ERROR] Custom Loader must have at least one package, such as \033[31mDemo.%s\033[0m\n",
            custom_loader,
        )
        return

    # Convert dex to cpp first (before modifying any files)
    timer.stage("DEX Analysis & Method Compilation")
    compiled_methods, method_prototypes, errors = compile_dex(
        apkfile, filtercfg, obfus, dynamic_register, target_dex_indices, target_dex_mode
    )

    if errors:
        Logger.warning("================================")
        Logger.warning("\n".join(errors))
        Logger.warning("================================")

    if len(compiled_methods) == 0:
        Logger.info("No methods compiled! Check your filter file.")
        timer.end()
        return

    timer.stage("Project Setup & File Preparation")
    if project_dir:
        if not path.exists(project_dir):
            copytree("project", project_dir)
        write_compiled_methods(project_dir, compiled_methods)
    else:
        global current_project_dir
        project_dir = make_temp_dir("dcc-project-")
        current_project_dir = project_dir  # Track for cleanup

        Logger.info(f"Creating temp project directory: {project_dir}")
        if path.exists(project_dir):
            Logger.info(f"Removing existing directory: {project_dir}")
            rmtree(project_dir)

        Logger.info(f"Copying project directory to: {project_dir}")
        if not path.exists("project"):
            raise Exception("Source project directory not found: project")

        try:
            copytree("project", project_dir)
            Logger.info(f"Successfully copied project to: {project_dir}")
        except Exception as e:
            Logger.error(f"Failed to copy project directory: {str(e)}")
            raise

        write_compiled_methods(project_dir, compiled_methods)

    # Now modify the dex2c file in the temp project directory
    dex2c_temp_path = path.join(project_dir, "jni", "nc", "Dex2C.cpp")
    with open(dex2c_temp_path, "r") as file:
        dex2c_file_data = file.read()

    dex2c_file_data = dex2c_file_data.replace(
        'env->FindClass("am/ge");',
        'env->FindClass("' + custom_loader.replace(".", "/") + '");',
    )
    dex2c_file_data = dex2c_file_data.replace(
        "Java_am_ge", "Java_" + custom_loader.replace(".", "_")
    )

    with open(dex2c_temp_path, "w") as file:
        file.write(dex2c_file_data)

    if not IGNORE_APP_LIB_ABIS:
        adjust_application_mk_temp(apkfile, project_dir)

    if not do_compile:
        src_zip = archive_compiled_code(project_dir)
        move(src_zip, source_archive)

    if do_compile:
        timer.stage("Native Library Compilation")
        if dynamic_register:
            write_dynamic_register(project_dir, compiled_methods, method_prototypes)
        else:
            write_dummy_dynamic_register(project_dir)

        try:
            Logger.info(f"Starting NDK build in {project_dir}")
            build_project(project_dir)
            Logger.info(f"NDK build completed successfully in {project_dir}")
        except Exception as e:
            Logger.error(f"NDK build failed: {str(e)}")
            raise

    if is_apk(apkfile) and outapk:
        timer.stage("APK Decompilation & Processing")
        decompiled_dir = ApkTool.decompile(apkfile)
        native_compiled_dexes(decompiled_dir, compiled_methods)
        copy_compiled_libs(project_dir, decompiled_dir)

        # n
        smali_folders = get_smali_folders(decompiled_dir)
        android_mk_file_path = path.join(project_dir, "jni", "Android.mk")
        loader_file_path = "loader/AmGe.smali"
        temp_loader = make_temp_file("-Loader.smali")

        local_module_value = None
        with open(android_mk_file_path, "r") as android_mk_file:
            for line in android_mk_file:
                if line.startswith("LOCAL_MODULE"):
                    _, local_module_value = line.split(":=", 1)
                    local_module_value = local_module_value.strip()
                    break

        if local_module_value:
            pattern = r'const-string v0, "[\w\W]+"'
            replacement = 'const-string v0, "' + local_module_value + '"'
        else:
            raise Exception("Invalid LOCAL_MODULE defined in project/jni/Android.mk")

        with open(loader_file_path, "r") as file:
            filedata = file.read()

        filedata = re.sub(pattern, replacement, filedata)
        filedata = filedata.replace(
            "Lam/ge;", "L" + custom_loader.replace(".", "/") + ";"
        )

        with open(temp_loader, "w") as file:
            file.write(filedata)

        apk_file_path = apkfile
        application_class_name = get_application_name_from_manifest(apk_file_path)
        file_path = get_application_class_file(
            decompiled_dir, smali_folders, application_class_name
        )

        if application_class_name == "" or file_path == "":
            for smali_folder in smali_folders:
                loader = path.join(
                    decompiled_dir,
                    smali_folder,
                    custom_loader.replace(".", os.sep) + ".smali",
                )
                if path.isfile(loader):
                    Logger.error(
                        f" Please, edit the Custom Loader: \033[31m{custom_loader}\033[0m already exists.\n"
                    )
                    return
            try:
                Logger.info(
                    "\nApplication class not found in the AndroidManifest.xml or doesn't exist in dex, adding \033[32m"
                    + custom_loader
                    + "\033[0m\n"
                )

                if is_windows():
                    modify_application_name(
                        path.join(decompiled_dir, "AndroidManifest.xml"), custom_loader
                    )
                else:
                    check_call(
                        [
                            JAVA_EXECUTABLE,
                            "-jar",
                            MANIFEST_EDITOR,
                            path.join(decompiled_dir, "AndroidManifest.xml"),
                            custom_loader,
                        ],
                        stderr=STDOUT,
                    )
            except Exception as e:
                Logger.error(f"Error: {e.returncode} - {e.output}", exec_info=True)
        else:
            Logger.info(
                "\nApplication class from AndroidManifest.xml, \033[32m"
                + application_class_name
                + "\033[0m\n"
            )

            if is_windows():
                modify_application_name(
                    path.join(decompiled_dir, "AndroidManifest.xml"),
                    application_class_name,
                )
            else:
                check_call(
                    [
                        JAVA_EXECUTABLE,
                        "-jar",
                        MANIFEST_EDITOR,
                        path.join(decompiled_dir, "AndroidManifest.xml"),
                        application_class_name,
                    ],
                    stderr=STDOUT,
                )

            line_to_insert = (
                "    invoke-static {}, L"
                + custom_loader.replace(".", "/")
                + ";->initDcc()V\n"
            )

            code_block_to_append = f"""
                .method static final constructor <clinit>()V
                    .registers 0

                {line_to_insert}

                    return-void
                .end method
                """

            with open(file_path, "r") as file:
                content = file.readlines()

            index = next(
                (i for i, line in enumerate(content) if "<clinit>" in line), None
            )

            if index is not None:
                locals_index = next(
                    (
                        i
                        for i, line in enumerate(content[index:])
                        if ".locals" in line or ".registers" in line
                    ),
                    None,
                )
                if locals_index is not None:
                    loc = re.compile(
                        "(    (?:\\.locals|\\.registers) )(\\d+)\n"
                    ).search(content[index + locals_index])
                    if loc.group(2) == "0":
                        content[index + locals_index] = loc.group(1) + "1" + "\n"
                    content.insert(index + locals_index + 1, line_to_insert)
                else:
                    Logger.error("Couldn't read <clinit> method in Application class")
            else:
                content.append(code_block_to_append)

            with open(file_path, "w") as file:
                file.writelines(content)

        if custom_loader.rfind(".") > -1:
            loaderDir = path.join(
                decompiled_dir,
                smali_folders[-1],
                custom_loader[0 : custom_loader.rfind(".")].replace(".", os.sep),
            )
            if not path.isdir(loaderDir):
                os.makedirs(loaderDir)
        copy(
            temp_loader,
            path.join(
                decompiled_dir,
                smali_folders[-1],
                custom_loader.replace(".", os.sep) + ".smali",
            ),
        )

        timer.stage("APK Compilation & Packaging")
        unsigned_apk = ApkTool.compile(decompiled_dir)
        zipalign(unsigned_apk, outapk)

        if not disable_signing:
            timer.stage("APK Signing")
            sign(outapk, outapk)

    # End overall timer
    timer.end()


sys.setrecursionlimit(5000)

if __name__ == "__main__":
    parser = argparse.ArgumentParser()

    parser.add_argument("-a", "--input", nargs="?", help="Input apk file path")
    parser.add_argument("-o", "--out", nargs="?", help="Output apk file path")
    parser.add_argument(
        "-p",
        "--obfuscate",
        action="store_true",
        default=False,
        help="Obfuscate string constants.",
    )
    parser.add_argument(
        "-d",
        "--dynamic-register",
        action="store_true",
        default=False,
        help="Export native methods using RegisterNatives.",
    )
    parser.add_argument(
        "--filter", default="filter.txt", help="Method filters configuration file."
    )
    parser.add_argument(
        "--custom-loader",
        default="am.ge",
        help="Loader class, default: am.ge",
    )
    parser.add_argument(
        "--skip-synthetic",
        action="store_true",
        default=False,
        help="Skip synthetic methods in all classes.",
    )
    parser.add_argument(
        "--no-build",
        action="store_true",
        default=False,
        help="Do not build the compiled code",
    )
    parser.add_argument(
        "--force-keep-libs",
        action="store_true",
        default=False,
        help="Forcefully keep the lib abis defined in Application.mk, regardless of the abis already available in the apk",
    )
    parser.add_argument("--source-dir", help="The compiled cpp code output directory.")
    parser.add_argument(
        "--project-archive",
        default="project-source.zip",
        help="Converted cpp code, compressed as zip output file.",
    )
    parser.add_argument(
        "--disable-signing",
        action="store_true",
        default=False,
        help="Disable APK signing.",
    )
    parser.add_argument(
        "--target-dex",
        help="Target specific DEX files. Options: 'last' (highest numbered DEX), 'first' (classes.dex), or comma-separated indices (e.g., '10' for classes11.dex). DEX indices start from 0.",
    )

    args = vars(parser.parse_args())
    input_apk = args["input"]
    out_apk = args["out"]
    obfus = args["obfuscate"]
    filtercfg = args["filter"]
    custom_loader = args["custom_loader"]
    SKIP_SYNTHETIC_METHODS = args["skip_synthetic"]
    IGNORE_APP_LIB_ABIS = args["force_keep_libs"]
    do_compile = not args["no_build"]
    source_archive = args["project_archive"]
    dynamic_register = args["dynamic_register"]
    disable_signing = args["disable_signing"]

    # Parse target DEX indices
    target_dex_indices = None
    target_dex_mode = None
    if args["target_dex"]:
        target_dex_arg = args["target_dex"].strip().lower()
        if target_dex_arg == "last":
            target_dex_mode = "last"
            Logger.info("Targeting the last (highest numbered) DEX file")
        elif target_dex_arg == "first":
            target_dex_indices = [0]
            Logger.info("Targeting the first DEX file (classes.dex)")
        else:
            try:
                target_dex_indices = [int(x.strip()) for x in args["target_dex"].split(",")]
                Logger.info(f"Targeting DEX files at indices: {target_dex_indices}")
            except ValueError:
                Logger.error("Invalid --target-dex format. Use 'last', 'first', or comma-separated integers (e.g., '10' or '0,10')")
                sys.exit(1)

    if args["source_dir"]:
        project_dir = args["source_dir"]
    else:
        project_dir = None

    dcc_cfg = {}
    with open("dcc.cfg") as fp:
        dcc_cfg = json.load(fp)

    if "ndk_dir" in dcc_cfg and path.exists(dcc_cfg["ndk_dir"]):
        ndk_dir = dcc_cfg["ndk_dir"]
        if is_windows():
            NDKBUILD = path.join(ndk_dir, "ndk-build.cmd")
        else:
            NDKBUILD = path.join(ndk_dir, "ndk-build")
        NDKBUILD = os.path.normpath(NDKBUILD)

        if not path.exists(NDKBUILD):
            raise Exception("Invalid ndk_dir path, file not found at " + NDKBUILD)

    if "apktool" in dcc_cfg and path.exists(dcc_cfg["apktool"]):
        APKTOOL = dcc_cfg["apktool"]

    show_logging(level=INFO)

    # n
    # Must be invoked first before invoking any other method
    create_tmp_directory()

    # NEW: Ensure temp directories are clean before starting protection
    ensure_clean_temp_directories()

    # Backing up jni folder because modifications will be made in runtime
    backup_jni_folder_path = backup_jni_project_folder()

    try:
        dcc_main(
            input_apk,
            obfus,
            filtercfg,
            custom_loader,
            out_apk,
            do_compile,
            project_dir,
            source_archive,
            dynamic_register,
            disable_signing,
            target_dex_indices,
            target_dex_mode,
        )
    except Exception as e:
        Logger.error("Compile %s failed!" % input_apk, exc_info=True)
        print(f"{str(e)}")
    finally:
        # n
        restore_jni_project_folder(backup_jni_folder_path)
        # Clean the specific project directory if it was created
        if current_project_dir and path.exists(current_project_dir):
            clean_tmp_directory(current_project_dir)
        else:
            clean_tmp_directory()

        # NEW: Force cleanup C:\z directory after protection
        force_cleanup_cz_directory()

        # NEW: Clean workspace temp directories after protection
        cleanup_workspace_temp_directories()

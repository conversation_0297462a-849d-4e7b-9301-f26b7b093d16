#include "Includes/my_secure_obfuscate.h"

#if defined(__aarch64__)

    const char* aarch = "arm64-v8a - 64Bit";
    struct patches {
        // Boolean
        const char* BoolTrue = SECURE_OBFUSCATE("20 00 80 D2 C0 03 5F D6");  // True
        const char* BoolFalse = SECURE_OBFUSCATE("00 00 80 D2 C0 03 5F D6"); // False

        // Int
        const char* Int0 = BoolFalse;  // 0
        //const char* Int1 = BoolTrue;  // 1
        const char* Int10 = SECURE_OBFUSCATE("40 01 80 52 C0 03 5F D6");  // 10
        const char* Int100 = SECURE_OBFUSCATE("80 0C 80 52 C0 03 5F D6"); // 100
        const char* Int1000 = SECURE_OBFUSCATE("00 7D 80 52 C0 03 5F D6"); // 1000
        const char* Int10000 = SECURE_OBFUSCATE("00 E2 84 52 C0 03 5F D6"); // 10,000
        const char* IntInfinite = SECURE_OBFUSCATE("00 E0 AF D2 C0 03 5F D6"); // Infinite

        // Float
        const char* Float0 = SECURE_OBFUSCATE("E0 03 27 1E C0 03 5F D6");  // 0
        const char* Float1 = SECURE_OBFUSCATE("00 10 2E 1E C0 03 5F D6");  // 1
        const char* Float10 = SECURE_OBFUSCATE("00 90 24 1E C0 03 5F D6"); // 10
        const char* Float100 = SECURE_OBFUSCATE("00 59 A8 52 00 00 27 1E C0 03 5F D6"); // 100
        const char* Float1000 = SECURE_OBFUSCATE("40 8F A8 52 00 00 27 1E C0 03 5F D6"); // 1000
        const char* Float2000 = SECURE_OBFUSCATE("FA 04 04 E3 1E FF 2F E1"); // 2000

        //Double ( Taken From Billonz Offset Tester So Credit To Him )
        const char* Double0 = SECURE_OBFUSCATE("E003679EC0035FD6");
        const char* Double1 = SECURE_OBFUSCATE("00106E1EC0035FD6");
        const char* Double10 = SECURE_OBFUSCATE("0090641EC0035FD6");
        const char* Double100 = SECURE_OBFUSCATE("800C8052C0035FD6");
        const char* Double1000 = SECURE_OBFUSCATE("000080F20000A0F20000C8F2E011E8F20000679EC0035FD6");
        const char* Double10000 = SECURE_OBFUSCATE("000080F20000A0F20000D1F20019E8F20000679EC0035FD6");
        const char* DoubleInfinite =  SECURE_OBFUSCATE("000080F20000A0F20040CDF2001FE8F20000679EC0035FD6");

        const char* NOP = SECURE_OBFUSCATE("1F2003D5");
    } HEXPATCH;

#else // 32-bit architecture
    
    const char* aarch = "armeabi-v7a - 32Bit";
    
    struct patches {
        // Boolean
        const char* BoolTrue = SECURE_OBFUSCATE("01 00 A0 E3 1E FF 2F E1");  // True
        const char* BoolFalse = SECURE_OBFUSCATE("00 00 A0 E3 1E FF 2F E1"); // False

        // Int
        const char* Int0 = BoolFalse;  // 0
        const char* Int1 = BoolTrue;   // 1
        const char* Int10 = SECURE_OBFUSCATE("0A 00 A0 E3 1E FF 2F E1");  // 10
        const char* Int100 = SECURE_OBFUSCATE("64 00 00 E3 1E FF 2F E1"); // 100
        const char* Int1000 = SECURE_OBFUSCATE("01 0A A0 E3 1E FF 2F E1"); // 1000
        const char* Int10000 = SECURE_OBFUSCATE("10 07 02 E3 1E FF 2F E1"); // 10,000
        const char* IntInfinite = SECURE_OBFUSCATE("02 01 E0 E3 1E FF 2F E1"); // Infinite

        // Float
        const char* Float0 = SECURE_OBFUSCATE("00 00 40 E3 1E FF 2F E1");  // 0
        const char* Float1 = SECURE_OBFUSCATE("80 0F 43 E3 1E FF 2F E1");  // 1
        const char* Float10 = SECURE_OBFUSCATE("20 01 04 E3 1E FF 2F E1");  // 10
        const char* Float100 = SECURE_OBFUSCATE("C8 02 44 E3 1E FF 2F E1"); // 100
        const char* Float1000 = SECURE_OBFUSCATE("7A 04 44 E3 1E FF 2F E1"); // 1000
        const char* Float2000 = SECURE_OBFUSCATE("FA 04 04 E3 1E FF 2F E1"); // 2000

        //Double ( Taken From Billonz Offset Tester So Credit To Him )
        const char* Double0 = SECURE_OBFUSCATE("000000E3001040E3100B41EC1EFF2FE1");
        const char* Double1 = SECURE_OBFUSCATE("0000A0E3001000E3F01F43E3100B41EC1EFF2FE1");
        const char* Double10 = SECURE_OBFUSCATE("0000A0E3001000E3241044E3100B41EC1EFF2FE1");
        const char* Double100 = SECURE_OBFUSCATE("0000A0E3001000E3591044E3100B41EC1EFF2FE1");
        const char* Double1000 = SECURE_OBFUSCATE("0000A0E3001004E38F1044E3100B41EC1EFF2FE1");
        const char* Double10000 = SECURE_OBFUSCATE("0000A0E3001808E3C31044E3100B41EC1EFF2FE1");
        const char* DoubleInfinite = SECURE_OBFUSCATE("000000E3C00F4FE3FF1F0FE3DF1144E3100B41EC1EFF2FE1");

        const char* NOP = SECURE_OBFUSCATE("00F020E3");
     } HEXPATCH;

#endif

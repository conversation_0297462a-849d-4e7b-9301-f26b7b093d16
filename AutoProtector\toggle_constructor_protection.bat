@echo off
echo ========================================
echo Constructor Protection Toggle Tool
echo ========================================
echo.
echo Current settings:
type constructor_config.json | findstr "protect_"
echo.
echo Options:
echo 1. Enable Menu Constructor Protection (protect_init: true)
echo 2. Disable Menu Constructor Protection (protect_init: false)
echo 3. Enable Static Constructor Protection (protect_clinit: true)
echo 4. Disable Static Constructor Protection (protect_clinit: false)
echo 5. View full config file
echo 6. Exit
echo.
set /p choice="Enter your choice (1-6): "

if "%choice%"=="1" (
    powershell -Command "(Get-Content constructor_config.json) -replace '\"protect_init\": false', '\"protect_init\": true' | Set-Content constructor_config.json"
    echo Menu Constructor Protection ENABLED
    goto :end
)
if "%choice%"=="2" (
    powershell -Command "(Get-Content constructor_config.json) -replace '\"protect_init\": true', '\"protect_init\": false' | Set-Content constructor_config.json"
    echo Menu Constructor Protection DISABLED
    goto :end
)
if "%choice%"=="3" (
    powershell -Command "(Get-Content constructor_config.json) -replace '\"protect_clinit\": false', '\"protect_clinit\": true' | Set-Content constructor_config.json"
    echo Static Constructor Protection ENABLED
    goto :end
)
if "%choice%"=="4" (
    powershell -Command "(Get-Content constructor_config.json) -replace '\"protect_clinit\": true', '\"protect_clinit\": false' | Set-Content constructor_config.json"
    echo Static Constructor Protection DISABLED
    goto :end
)
if "%choice%"=="5" (
    echo.
    echo Full configuration:
    type constructor_config.json
    goto :end
)
if "%choice%"=="6" (
    goto :end
)

echo Invalid choice. Please try again.
pause
goto :start

:end
echo.
echo Updated settings:
type constructor_config.json | findstr "protect_"
echo.
echo Done! You can now run the protection process.
pause

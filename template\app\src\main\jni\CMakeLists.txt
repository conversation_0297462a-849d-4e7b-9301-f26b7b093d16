# CMakeLists.txt - Alternative build system for O-MVLL
# This is an OPTIONAL alternative to Android.mk for users who want full O-MVLL integration

cmake_minimum_required(VERSION 3.18)
project(AmpedGems)

# O-MVLL Configuration
option(ENABLE_OMVLL "Enable O-MVLL obfuscation" OFF)

if(ENABLE_OMVLL)
    message(STATUS "O-MVLL obfuscation enabled")
    
    # Find O-MVLL plugin
    if(DEFINED ENV{OMVLL_PLUGIN_PATH})
        set(OMVLL_PLUGIN_PATH $ENV{OMVLL_PLUGIN_PATH})
    else()
        find_library(OMVLL_PLUGIN_PATH NAMES libOMVLL.so OMVLL)
    endif()
    
    if(OMVLL_PLUGIN_PATH)
        message(STATUS "Found O-MVLL at: ${OMVLL_PLUGIN_PATH}")
        set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -fpass-plugin=${OMVLL_PLUGIN_PATH}")
    else()
        message(WARNING "O-MVLL plugin not found, building without obfuscation")
    endif()
endif()

# Compiler settings (matching Android.mk)
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -w -s -Wno-error=format-security -fvisibility=hidden -fpermissive -fexceptions")
set(CMAKE_CXX_FLAGS "${CMAKE_CXX_FLAGS} -Wno-error=c++11-narrowing -Wall")

# Include directories
include_directories(${CMAKE_CURRENT_SOURCE_DIR})

# Source files (matching Android.mk)
set(SOURCES
    Main.cpp
    decoy.cpp
    Substrate/hde64.c
    Substrate/SubstrateDebug.cpp
    Substrate/SubstrateHook.cpp
    Substrate/SubstratePosixMemory.cpp
    Substrate/SymbolFinder.cpp
    KittyMemory/KittyMemory.cpp
    KittyMemory/MemoryPatch.cpp
    KittyMemory/MemoryBackup.cpp
    KittyMemory/KittyUtils.cpp
    And64InlineHook/And64InlineHook.cpp
)

# Create shared library
add_library(AmpedGems SHARED ${SOURCES})

# Link libraries
target_link_libraries(AmpedGems
    log
    android
    EGL
    GLESv2
)

# Link flags (matching Android.mk)
set_target_properties(AmpedGems PROPERTIES
    LINK_FLAGS "-Wl,--gc-sections,--strip-all"
)

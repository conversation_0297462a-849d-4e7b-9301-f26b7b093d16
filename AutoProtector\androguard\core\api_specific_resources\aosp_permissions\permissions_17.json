{"groups": {"android.permission-group.ACCOUNTS": {"description": "Access the available accounts.", "description_ptr": "permgroupdesc_accounts", "icon": "", "icon_ptr": "perm_group_accounts", "label": "Your accounts", "label_ptr": "permgrouplab_accounts", "name": "android.permission-group.ACCOUNTS"}, "android.permission-group.AFFECTS_BATTERY": {"description": "Use features that can quickly drain battery.", "description_ptr": "permgroupdesc_affectsBattery", "icon": "", "icon_ptr": "perm_group_affects_battery", "label": "Affects Battery", "label_ptr": "permgrouplab_affectsBattery", "name": "android.permission-group.AFFECTS_BATTERY"}, "android.permission-group.APP_INFO": {"description": "Ability to affect behavior of other applications on your device.", "description_ptr": "permgroupdesc_appInfo", "icon": "", "icon_ptr": "perm_group_app_info", "label": "Your applications information", "label_ptr": "permgrouplab_appInfo", "name": "android.permission-group.APP_INFO"}, "android.permission-group.AUDIO_SETTINGS": {"description": "Change audio settings.", "description_ptr": "permgroupdesc_audioSettings", "icon": "", "icon_ptr": "perm_group_audio_settings", "label": "Audio Settings", "label_ptr": "permgrouplab_audioSettings", "name": "android.permission-group.AUDIO_SETTINGS"}, "android.permission-group.BLUETOOTH_NETWORK": {"description": "Access devices and networks through Bluetooth.", "description_ptr": "permgroupdesc_bluetoothNetwork", "icon": "", "icon_ptr": "perm_group_bluetooth", "label": "Bluetooth", "label_ptr": "permgrouplab_bluetoothNetwork", "name": "android.permission-group.BLUETOOTH_NETWORK"}, "android.permission-group.BOOKMARKS": {"description": "Direct access to bookmarks and browser history.", "description_ptr": "permgroupdesc_bookmarks", "icon": "", "icon_ptr": "perm_group_bookmarks", "label": "Bookmarks and History", "label_ptr": "permgrouplab_bookmarks", "name": "android.permission-group.BOOKMARKS"}, "android.permission-group.CALENDAR": {"description": "Direct access to calendar and events.", "description_ptr": "permgroupdesc_calendar", "icon": "", "icon_ptr": "perm_group_calendar", "label": "Calendar", "label_ptr": "permgrouplab_calendar", "name": "android.permission-group.CALENDAR"}, "android.permission-group.CAMERA": {"description": "Direct access to camera for image or video capture.", "description_ptr": "permgroupdesc_camera", "icon": "", "icon_ptr": "perm_group_camera", "label": "Camera", "label_ptr": "permgrouplab_camera", "name": "android.permission-group.CAMERA"}, "android.permission-group.COST_MONEY": {"description": "Do things that can cost you money.", "description_ptr": "permgroupdesc_costMoney", "icon": "", "icon_ptr": "", "label": "Services that cost you money", "label_ptr": "permgrouplab_costMoney", "name": "android.permission-group.COST_MONEY"}, "android.permission-group.DEVELOPMENT_TOOLS": {"description": "Features only needed for app developers.", "description_ptr": "permgroupdesc_developmentTools", "icon": "", "icon_ptr": "", "label": "Development tools", "label_ptr": "permgrouplab_developmentTools", "name": "android.permission-group.DEVELOPMENT_TOOLS"}, "android.permission-group.DEVICE_ALARMS": {"description": "Set the alarm clock.", "description_ptr": "permgroupdesc_deviceAlarms", "icon": "", "icon_ptr": "perm_group_device_alarms", "label": "Alarm", "label_ptr": "permgrouplab_deviceAlarms", "name": "android.permission-group.DEVICE_ALARMS"}, "android.permission-group.DISPLAY": {"description": "Effect the UI of other applications.", "description_ptr": "permgroupdesc_display", "icon": "", "icon_ptr": "perm_group_display", "label": "Other Application UI", "label_ptr": "permgrouplab_display", "name": "android.permission-group.DISPLAY"}, "android.permission-group.HARDWARE_CONTROLS": {"description": "Direct access to hardware on the handset.", "description_ptr": "permgroupdesc_hardwareControls", "icon": "", "icon_ptr": "", "label": "Hardware controls", "label_ptr": "permgrouplab_hardwareControls", "name": "android.permission-group.HARDWARE_CONTROLS"}, "android.permission-group.LOCATION": {"description": "Monitor your physical location.", "description_ptr": "permgroupdesc_location", "icon": "", "icon_ptr": "perm_group_location", "label": "Your location", "label_ptr": "permgrouplab_location", "name": "android.permission-group.LOCATION"}, "android.permission-group.MESSAGES": {"description": "Read and write your SMS, email, and other messages.", "description_ptr": "permgroupdesc_messages", "icon": "", "icon_ptr": "perm_group_messages", "label": "Your messages", "label_ptr": "permgrouplab_messages", "name": "android.permission-group.MESSAGES"}, "android.permission-group.MICROPHONE": {"description": "Direct access to the microphone to record audio.", "description_ptr": "permgroupdesc_microphone", "icon": "", "icon_ptr": "perm_group_microphone", "label": "Microphone", "label_ptr": "permgrouplab_microphone", "name": "android.permission-group.MICROPHONE"}, "android.permission-group.NETWORK": {"description": "Access various network features.", "description_ptr": "permgroupdesc_network", "icon": "", "icon_ptr": "perm_group_network", "label": "Network communication", "label_ptr": "permgrouplab_network", "name": "android.permission-group.NETWORK"}, "android.permission-group.PERSONAL_INFO": {"description": "Direct access to information about you, stored in on your contact card.", "description_ptr": "permgroupdesc_personalInfo", "icon": "", "icon_ptr": "perm_group_personal_info", "label": "Your personal information", "label_ptr": "permgrouplab_personalInfo", "name": "android.permission-group.PERSONAL_INFO"}, "android.permission-group.PHONE_CALLS": {"description": "Monitor, record, and process phone calls.", "description_ptr": "permgroupdesc_phoneCalls", "icon": "", "icon_ptr": "perm_group_phone_calls", "label": "Phone calls", "label_ptr": "permgrouplab_phoneCalls", "name": "android.permission-group.PHONE_CALLS"}, "android.permission-group.SCREENLOCK": {"description": "Access the SD card.", "description_ptr": "permgroupdesc_storage", "icon": "", "icon_ptr": "perm_group_screenlock", "label": "Storage", "label_ptr": "permgrouplab_storage", "name": "android.permission-group.SCREENLOCK"}, "android.permission-group.SOCIAL_INFO": {"description": "Direct access to information about your contacts and social connections.", "description_ptr": "permgroupdesc_socialInfo", "icon": "", "icon_ptr": "perm_group_social_info", "label": "Your social information", "label_ptr": "permgrouplab_socialInfo", "name": "android.permission-group.SOCIAL_INFO"}, "android.permission-group.STATUS_BAR": {"description": "Change the device status bar settings.", "description_ptr": "permgroupdesc_statusBar", "icon": "", "icon_ptr": "perm_group_status_bar", "label": "Status Bar", "label_ptr": "permgrouplab_statusBar", "name": "android.permission-group.STATUS_BAR"}, "android.permission-group.STORAGE": {"description": "Access the SD card.", "description_ptr": "permgroupdesc_storage", "icon": "", "icon_ptr": "perm_group_storage", "label": "Storage", "label_ptr": "permgrouplab_storage", "name": "android.permission-group.STORAGE"}, "android.permission-group.SYNC_SETTINGS": {"description": "Access to the sync settings.", "description_ptr": "permgroupdesc_syncSettings", "icon": "", "icon_ptr": "perm_group_sync_settings", "label": "Sync Settings", "label_ptr": "permgrouplab_syncSettings", "name": "android.permission-group.SYNC_SETTINGS"}, "android.permission-group.SYSTEM_CLOCK": {"description": "Change the device time or timezone.", "description_ptr": "permgroupdesc_systemClock", "icon": "", "icon_ptr": "perm_group_system_clock", "label": "Clock", "label_ptr": "permgrouplab_systemClock", "name": "android.permission-group.SYSTEM_CLOCK"}, "android.permission-group.SYSTEM_TOOLS": {"description": "Lower-level access and control of the system.", "description_ptr": "permgroupdesc_systemTools", "icon": "", "icon_ptr": "perm_group_system_tools", "label": "System tools", "label_ptr": "permgrouplab_systemTools", "name": "android.permission-group.SYSTEM_TOOLS"}, "android.permission-group.USER_DICTIONARY": {"description": "Read words in user dictionary.", "description_ptr": "permgroupdesc_dictionary", "icon": "", "icon_ptr": "perm_group_user_dictionary", "label": "Read User Dictionary", "label_ptr": "permgrouplab_dictionary", "name": "android.permission-group.USER_DICTIONARY"}, "android.permission-group.VOICEMAIL": {"description": "Direct access to voicemail.", "description_ptr": "permgroupdesc_voicemail", "icon": "", "icon_ptr": "perm_group_voicemail", "label": "Voicemail", "label_ptr": "permgrouplab_voicemail", "name": "android.permission-group.VOICEMAIL"}, "android.permission-group.WALLPAPER": {"description": "Change the device wallpaper settings.", "description_ptr": "permgroupdesc_wallpaper", "icon": "", "icon_ptr": "perm_group_wallpaper", "label": "Wallpaper", "label_ptr": "permgrouplab_wallpaper", "name": "android.permission-group.WALLPAPER"}, "android.permission-group.WRITE_USER_DICTIONARY": {"description": "Add words to the user dictionary.", "description_ptr": "permgroupdesc_writeDictionary", "icon": "", "icon_ptr": "perm_group_user_dictionary_write", "label": "Write User Dictionary", "label_ptr": "permgrouplab_writeDictionary", "name": "android.permission-group.WRITE_USER_DICTIONARY"}}, "permissions": {"android.intent.category.MASTER_CLEAR.permission.C2D_MESSAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.intent.category.MASTER_CLEAR.permission.C2D_MESSAGE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_ALL_EXTERNAL_STORAGE": {"description": "Allows the app to access external storage for all users.", "description_ptr": "permdesc_sdcardAccessAll", "label": "access external storage of all users", "label_ptr": "permlab_sdcardAccessAll", "name": "android.permission.ACCESS_ALL_EXTERNAL_STORAGE", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature"}, "android.permission.ACCESS_CACHE_FILESYSTEM": {"description": "Allows the app to read and write the cache filesystem.", "description_ptr": "permdesc_cache_filesystem", "label": "access the cache filesystem", "label_ptr": "permlab_cache_filesystem", "name": "android.permission.ACCESS_CACHE_FILESYSTEM", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.ACCESS_CHECKIN_PROPERTIES": {"description": "Allows the app read/write access to\n        properties uploaded by the checkin service.  Not for use by normal\n        apps.", "description_ptr": "permdesc_checkinProperties", "label": "access checkin properties", "label_ptr": "permlab_checkinProperties", "name": "android.permission.ACCESS_CHECKIN_PROPERTIES", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.ACCESS_COARSE_LOCATION": {"description": "Allows the app to get your\n      approximate location. This location is derived by location services using\n      network location sources such as cell towers and Wi-Fi. These location\n      services must be turned on and available to your device for the app to\n      use them. Apps may use this to determine approximately where you\n      are.", "description_ptr": "permdesc_accessCoarseLocation", "label": "approximate location\n      (network-based)", "label_ptr": "permlab_accessCoarseLocation", "name": "android.permission.ACCESS_COARSE_LOCATION", "permissionGroup": "android.permission-group.LOCATION", "protectionLevel": "dangerous"}, "android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY": {"description": "Allows the holder to access content\n     providers from the shell. Should never be needed for normal apps.", "description_ptr": "permdesc_accessContentProvidersExternally", "label": "access content providers externally", "label_ptr": "permlab_accessContentProvidersExternally", "name": "android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_FINE_LOCATION": {"description": "Allows the app to get your\n      precise location using the Global Positioning System (GPS) or network\n      location sources such as cell towers and Wi-Fi. These location services\n      must be turned on and available to your device for the app to use them.\n      Apps may use this to determine where you are, and may consume additional\n      battery power.", "description_ptr": "permdesc_accessFineLocation", "label": "precise location (GPS and\n      network-based)", "label_ptr": "permlab_accessFineLocation", "name": "android.permission.ACCESS_FINE_LOCATION", "permissionGroup": "android.permission-group.LOCATION", "protectionLevel": "dangerous"}, "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS": {"description": "Allows the app to access\n      extra location provider commands.  This may allow the app to to interfere\n      with the operation of the GPS or other location sources.", "description_ptr": "permdesc_accessLocationExtraCommands", "label": "access extra location provider commands", "label_ptr": "permlab_accessLocationExtraCommands", "name": "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "normal"}, "android.permission.ACCESS_MOCK_LOCATION": {"description": "Create mock location sources for\n      testing or install a new location provider.  This allows the app to\n      override the location and/or status returned by other location sources\n      such as GPS or location providers.", "description_ptr": "permdesc_accessMockLocation", "label": "mock location sources for testing", "label_ptr": "permlab_accessMockLocation", "name": "android.permission.ACCESS_MOCK_LOCATION", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "dangerous"}, "android.permission.ACCESS_MTP": {"description": "Allows access to the kernel MTP driver to implement the MTP USB protocol.", "description_ptr": "permdesc_accessMtp", "label": "implement MTP protocol", "label_ptr": "permlab_accessMtp", "name": "android.permission.ACCESS_MTP", "permissionGroup": "android.permission-group.HARDWARE_CONTROLS", "protectionLevel": "signature|system"}, "android.permission.ACCESS_NETWORK_STATE": {"description": "Allows the app to view\n      information about network connections such as which networks exist and are\n      connected.", "description_ptr": "permdesc_accessNetworkState", "label": "view network connections", "label_ptr": "permlab_accessNetworkState", "name": "android.permission.ACCESS_NETWORK_STATE", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "normal"}, "android.permission.ACCESS_SURFACE_FLINGER": {"description": "Allows the app to use SurfaceFlinger low-level features.", "description_ptr": "permdesc_accessSurfaceFlinger", "label": "access SurfaceFlinger", "label_ptr": "permlab_accessSurfaceFlinger", "name": "android.permission.ACCESS_SURFACE_FLINGER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_WIFI_STATE": {"description": "Allows the app to view information\n      about Wi-Fi networking, such as whether Wi-Fi is enabled and name of\n      connected Wi-Fi devices.", "description_ptr": "permdesc_accessWifiState", "label": "view Wi-Fi connections", "label_ptr": "permlab_accessWifiState", "name": "android.permission.ACCESS_WIFI_STATE", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "normal"}, "android.permission.ACCESS_WIMAX_STATE": {"description": "Allows the app to determine whether\n     WiMAX is enabled and information about any WiMAX networks that are\n     connected. ", "description_ptr": "permdesc_accessWimaxState", "label": "connect and disconnect from WiMAX", "label_ptr": "permlab_accessWimaxState", "name": "android.permission.ACCESS_WIMAX_STATE", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "normal"}, "android.permission.ACCOUNT_MANAGER": {"description": "Allows the app to make calls to AccountAuthenticators.", "description_ptr": "permdesc_accountManagerService", "label": "act as the AccountManagerService", "label_ptr": "permlab_accountManagerService", "name": "android.permission.ACCOUNT_MANAGER", "permissionGroup": "android.permission-group.ACCOUNTS", "protectionLevel": "signature"}, "android.permission.ALLOW_ANY_CODEC_FOR_PLAYBACK": {"description": "Allows the app to use any installed\n        media decoder to decode for playback.", "description_ptr": "permdesc_anyCodecForPlayback", "label": "use any media decoder for playback", "label_ptr": "permlab_anyCodecForPlayback", "name": "android.permission.ALLOW_ANY_CODEC_FOR_PLAYBACK", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.ASEC_ACCESS": {"description": "Allows the app to get information on internal storage.", "description_ptr": "permdesc_asec_access", "label": "get information on internal storage", "label_ptr": "permlab_asec_access", "name": "android.permission.ASEC_ACCESS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.ASEC_CREATE": {"description": "Allows the app to create internal storage.", "description_ptr": "permdesc_asec_create", "label": "create internal storage", "label_ptr": "permlab_asec_create", "name": "android.permission.ASEC_CREATE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.ASEC_DESTROY": {"description": "Allows the app to destroy internal storage.", "description_ptr": "permdesc_asec_destroy", "label": "destroy internal storage", "label_ptr": "permlab_asec_destroy", "name": "android.permission.ASEC_DESTROY", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.ASEC_MOUNT_UNMOUNT": {"description": "Allows the app to mount/unmount internal storage.", "description_ptr": "permdesc_asec_mount_unmount", "label": "mount/unmount internal storage", "label_ptr": "permlab_asec_mount_unmount", "name": "android.permission.ASEC_MOUNT_UNMOUNT", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.ASEC_RENAME": {"description": "Allows the app to rename internal storage.", "description_ptr": "permdesc_asec_rename", "label": "rename internal storage", "label_ptr": "permlab_asec_rename", "name": "android.permission.ASEC_RENAME", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.AUTHENTICATE_ACCOUNTS": {"description": "Allows the app\n    to use the account authenticator capabilities of the\n    AccountManager, including creating accounts and getting and\n    setting their passwords.", "description_ptr": "permdesc_authenticateAccounts", "label": "create accounts and set passwords", "label_ptr": "permlab_authenticateAccounts", "name": "android.permission.AUTHENTICATE_ACCOUNTS", "permissionGroup": "android.permission-group.ACCOUNTS", "protectionLevel": "dangerous"}, "android.permission.BACKUP": {"description": "Allows the app to control the system's backup and restore mechanism.  Not for use by normal apps.", "description_ptr": "permdesc_backup", "label": "control system backup and restore", "label_ptr": "permlab_backup", "name": "android.permission.BACKUP", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.BATTERY_STATS": {"description": "Allows an application to read the current low-level\n        battery use data.  May allow the application to find out detailed information about\n        which apps you use.", "description_ptr": "permdesc_batteryStats", "label": "read battery statistics", "label_ptr": "permlab_batteryStats", "name": "android.permission.BATTERY_STATS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "dangerous"}, "android.permission.BIND_ACCESSIBILITY_SERVICE": {"description": "Allows the holder to bind to the top-level\n        interface of an accessibility service. Should never be needed for normal apps.", "description_ptr": "permdesc_bindAccessibilityService", "label": "bind to an accessibility service", "label_ptr": "permlab_bindAccessibilityService", "name": "android.permission.BIND_ACCESSIBILITY_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_APPWIDGET": {"description": "Allows the app to tell the system\n        which widgets can be used by which app. An app with this permission\n        can give access to personal data to other apps.\n        Not for use by normal apps.", "description_ptr": "permdesc_bindGadget", "label": "choose widgets", "label_ptr": "permlab_bindGadget", "name": "android.permission.BIND_APPWIDGET", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "signature|system"}, "android.permission.BIND_DEVICE_ADMIN": {"description": "Allows the holder to send intents to\n        a device administrator. Should never be needed for normal apps.", "description_ptr": "permdesc_bindDeviceAdmin", "label": "interact with a device admin", "label_ptr": "permlab_bindDeviceAdmin", "name": "android.permission.BIND_DEVICE_ADMIN", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_DIRECTORY_SEARCH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_DIRECTORY_SEARCH", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "signature|system"}, "android.permission.BIND_INPUT_METHOD": {"description": "Allows the holder to bind to the top-level\n        interface of an input method. Should never be needed for normal apps.", "description_ptr": "permdesc_bindInputMethod", "label": "bind to an input method", "label_ptr": "permlab_bindInputMethod", "name": "android.permission.BIND_INPUT_METHOD", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_KEYGUARD_APPWIDGET": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_KEYGUARD_APPWIDGET", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "signature|system"}, "android.permission.BIND_PACKAGE_VERIFIER": {"description": "Allows the holder to make requests of\n        package verifiers. Should never be needed for normal apps.", "description_ptr": "permdesc_bindPackageVerifier", "label": "bind to a package verifier", "label_ptr": "permlab_bindPackageVerifier", "name": "android.permission.BIND_PACKAGE_VERIFIER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_REMOTEVIEWS": {"description": "Allows the holder to bind to the top-level\n        interface of a widget service. Should never be needed for normal apps.", "description_ptr": "permdesc_bindRemoteViews", "label": "bind to a widget service", "label_ptr": "permlab_bindRemoteViews", "name": "android.permission.BIND_REMOTEVIEWS", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.BIND_TEXT_SERVICE": {"description": "Allows the holder to bind to the top-level\n        interface of a text service(e.g. SpellCheckerService). Should never be needed for normal apps.", "description_ptr": "permdesc_bindTextService", "label": "bind to a text service", "label_ptr": "permlab_bindTextService", "name": "android.permission.BIND_TEXT_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_VPN_SERVICE": {"description": "Allows the holder to bind to the top-level\n        interface of a Vpn service. Should never be needed for normal apps.", "description_ptr": "permdesc_bindVpnService", "label": "bind to a VPN service", "label_ptr": "permlab_bindVpnService", "name": "android.permission.BIND_VPN_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_WALLPAPER": {"description": "Allows the holder to bind to the top-level\n        interface of a wallpaper. Should never be needed for normal apps.", "description_ptr": "permdesc_bindWallpaper", "label": "bind to a wallpaper", "label_ptr": "permlab_bindWallpaper", "name": "android.permission.BIND_WALLPAPER", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.BLUETOOTH": {"description": "Allows the app to view the\n      configuration of the Bluetooth on the phone, and to make and accept\n      connections with paired devices.", "description_ptr": "permdesc_bluetooth", "label": "pair with Bluetooth devices", "label_ptr": "permlab_bluetooth", "name": "android.permission.BLUETOOTH", "permissionGroup": "android.permission-group.BLUETOOTH_NETWORK", "protectionLevel": "dangerous"}, "android.permission.BLUETOOTH_ADMIN": {"description": "Allows the app to configure\n      the local Bluetooth phone, and to discover and pair with remote devices.", "description_ptr": "permdesc_bluetoothAdmin", "label": "access Bluetooth settings", "label_ptr": "permlab_bluetoothAdmin", "name": "android.permission.BLUETOOTH_ADMIN", "permissionGroup": "android.permission-group.BLUETOOTH_NETWORK", "protectionLevel": "dangerous"}, "android.permission.BLUETOOTH_STACK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BLUETOOTH_STACK", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.BRICK": {"description": "Allows the app to\n        disable the entire phone permanently. This is very dangerous.", "description_ptr": "permdesc_brick", "label": "permanently disable phone", "label_ptr": "permlab_brick", "name": "android.permission.BRICK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BROADCAST_PACKAGE_REMOVED": {"description": "Allows the app to\n        broadcast a notification that an app package has been removed.\n        Malicious apps may use this to kill any other running\n        app.", "description_ptr": "permdesc_broadcastPackageRemoved", "label": "send package removed broadcast", "label_ptr": "permlab_broadcastPackageRemoved", "name": "android.permission.BROADCAST_PACKAGE_REMOVED", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.BROADCAST_SMS": {"description": "Allows the app to\n        broadcast a notification that an SMS message has been received.\n        Malicious apps may use this to forge incoming SMS messages.", "description_ptr": "permdesc_broadcastSmsReceived", "label": "send SMS-received broadcast", "label_ptr": "permlab_broadcastSmsReceived", "name": "android.permission.BROADCAST_SMS", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "signature"}, "android.permission.BROADCAST_STICKY": {"description": "Allows the app to\n    send sticky broadcasts, which remain after the broadcast ends. Excessive\n    use may make the phone slow or unstable by causing it to use too\n    much memory.", "description_ptr": "permdesc_broadcastSticky", "label": "send sticky broadcast", "label_ptr": "permlab_broadcastSticky", "name": "android.permission.BROADCAST_STICKY", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "normal"}, "android.permission.BROADCAST_WAP_PUSH": {"description": "Allows the app to\n        broadcast a notification that a WAP PUSH message has been received.\n        Malicious apps may use this to forge MMS message receipt or to\n        silently replace the content of any webpage with malicious variants.", "description_ptr": "permdesc_broadcastWapPush", "label": "send WAP-PUSH-received broadcast", "label_ptr": "permlab_broadcastWapPush", "name": "android.permission.BROADCAST_WAP_PUSH", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "signature"}, "android.permission.CALL_PHONE": {"description": "Allows the app to call phone numbers\n      without your intervention. This may result in unexpected charges or calls.\n      Note that this doesn't allow the app to call emergency numbers.\n      Malicious apps may cost you money by making calls without your\n      confirmation.", "description_ptr": "permdesc_callPhone", "label": "directly call phone numbers", "label_ptr": "permlab_callPhone", "name": "android.permission.CALL_PHONE", "permissionGroup": "android.permission-group.PHONE_CALLS", "protectionLevel": "dangerous"}, "android.permission.CALL_PRIVILEGED": {"description": "Allows the app to call\n        any phone number, including emergency numbers, without your intervention.\n        Malicious apps may place unnecessary and illegal calls to emergency\n        services.", "description_ptr": "permdesc_callPrivileged", "label": "directly call any phone numbers", "label_ptr": "permlab_callPrivileged", "name": "android.permission.CALL_PRIVILEGED", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.CAMERA": {"description": "Allows the app to take pictures and videos\n      with the camera.  This permission allows the app to use the camera at any\n      time without your confirmation.", "description_ptr": "permdesc_camera", "label": "take pictures and videos", "label_ptr": "permlab_camera", "name": "android.permission.CAMERA", "permissionGroup": "android.permission-group.CAMERA", "protectionLevel": "dangerous"}, "android.permission.CHANGE_BACKGROUND_DATA_SETTING": {"description": "Allows the app to change the background data usage setting.", "description_ptr": "permdesc_changeBackgroundDataSetting", "label": "change background data usage setting", "label_ptr": "permlab_changeBackgroundDataSetting", "name": "android.permission.CHANGE_BACKGROUND_DATA_SETTING", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.CHANGE_COMPONENT_ENABLED_STATE": {"description": "Allows the app to change whether a\n        component of another app is enabled or not. Malicious apps may use this\n        to disable important phone capabilities. Care must be used with this permission, as it is\n        possible to get app components into an unusable, inconsistent, or unstable state.\n    ", "description_ptr": "permdesc_changeComponentState", "label": "enable or disable app components", "label_ptr": "permlab_changeComponentState", "name": "android.permission.CHANGE_COMPONENT_ENABLED_STATE", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.CHANGE_CONFIGURATION": {"description": "Allows the app to\n        change the current configuration, such as the locale or overall font\n        size.", "description_ptr": "permdesc_changeConfiguration", "label": "change system display settings", "label_ptr": "permlab_changeConfiguration", "name": "android.permission.CHANGE_CONFIGURATION", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "system|signature"}, "android.permission.CHANGE_NETWORK_STATE": {"description": "Allows the app to change the state of network connectivity.", "description_ptr": "permdesc_changeNetworkState", "label": "change network connectivity", "label_ptr": "permlab_changeNetworkState", "name": "android.permission.CHANGE_NETWORK_STATE", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "normal"}, "android.permission.CHANGE_WIFI_MULTICAST_STATE": {"description": "Allows the app to receive\n      packets sent to all devices on a Wi-Fi network using multicast addresses,\n      not just your phone.  It uses more power than the non-multicast mode.", "description_ptr": "permdesc_changeWifiMulticastState", "label": "allow Wi-Fi Multicast reception", "label_ptr": "permlab_changeWifiMulticastState", "name": "android.permission.CHANGE_WIFI_MULTICAST_STATE", "permissionGroup": "android.permission-group.AFFECTS_BATTERY", "protectionLevel": "dangerous"}, "android.permission.CHANGE_WIFI_STATE": {"description": "Allows the app to connect to and\n      disconnect from Wi-Fi access points and to make changes to device\n      configuration for Wi-Fi networks.", "description_ptr": "permdesc_changeWifiState", "label": "connect and disconnect from Wi-Fi", "label_ptr": "permlab_changeWifiState", "name": "android.permission.CHANGE_WIFI_STATE", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "dangerous"}, "android.permission.CHANGE_WIMAX_STATE": {"description": "Allows the app to\n      connect the phone to and disconnect the phone from WiMAX networks.", "description_ptr": "permdesc_changeWimaxState", "label": "Change WiMAX state", "label_ptr": "permlab_changeWimaxState", "name": "android.permission.CHANGE_WIMAX_STATE", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "dangerous"}, "android.permission.CLEAR_APP_CACHE": {"description": "Allows the app to free phone storage\n        by deleting files in the cache directories of other applications.  This may cause other\n        applications to start up more slowly as they need to re-retrieve their data.", "description_ptr": "permdesc_clearAppCache", "label": "delete all app cache data", "label_ptr": "permlab_clearAppCache", "name": "android.permission.CLEAR_APP_CACHE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "dangerous"}, "android.permission.CLEAR_APP_USER_DATA": {"description": "Allows the app to clear user data.", "description_ptr": "permdesc_clearAppUserData", "label": "delete other apps' data", "label_ptr": "permlab_clearAppUserData", "name": "android.permission.CLEAR_APP_USER_DATA", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONFIGURE_WIFI_DISPLAY": {"description": "Allows the app to configure and connect to Wifi displays.", "description_ptr": "permdesc_configureWifiDisplay", "label": "configure Wifi displays", "label_ptr": "permlab_configureWifiDisplay", "name": "android.permission.CONFIGURE_WIFI_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONFIRM_FULL_BACKUP": {"description": "Allows the app to launch the full backup confirmation UI.  Not to be used by any app.", "description_ptr": "permdesc_confirm_full_backup", "label": "confirm a full backup or restore operation", "label_ptr": "permlab_confirm_full_backup", "name": "android.permission.CONFIRM_FULL_BACKUP", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONNECTIVITY_INTERNAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONNECTIVITY_INTERNAL", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "signature|system"}, "android.permission.CONTROL_LOCATION_UPDATES": {"description": "Allows the app to enable/disable location\n        update notifications from the radio.  Not for use by normal apps.", "description_ptr": "permdesc_locationUpdates", "label": "control location update notifications", "label_ptr": "permlab_locationUpdates", "name": "android.permission.CONTROL_LOCATION_UPDATES", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.CONTROL_WIFI_DISPLAY": {"description": "Allows the app to control low-level features of Wifi displays.", "description_ptr": "permdesc_controlWifiDisplay", "label": "control Wifi displays", "label_ptr": "permlab_controlWifiDisplay", "name": "android.permission.CONTROL_WIFI_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.COPY_PROTECTED_DATA": {"description": "copy content", "description_ptr": "permlab_copyProtectedData", "label": "copy content", "label_ptr": "permlab_copyProtectedData", "name": "android.permission.COPY_PROTECTED_DATA", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CRYPT_KEEPER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CRYPT_KEEPER", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.DELETE_CACHE_FILES": {"description": "Allows the app to delete\n        cache files.", "description_ptr": "permdesc_deleteCacheFiles", "label": "delete other apps' caches", "label_ptr": "permlab_deleteCacheFiles", "name": "android.permission.DELETE_CACHE_FILES", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.DELETE_PACKAGES": {"description": "Allows the app to delete\n        Android packages. Malicious apps may use this to delete important apps.", "description_ptr": "permdesc_deletePackages", "label": "delete apps", "label_ptr": "permlab_deletePackages", "name": "android.permission.DELETE_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.DEVICE_POWER": {"description": "Allows the app to turn the phone on or off.", "description_ptr": "permdesc_devicePower", "label": "power phone on or off", "label_ptr": "permlab_devicePower", "name": "android.permission.DEVICE_POWER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.DIAGNOSTIC": {"description": "Allows the app to read and write to\n    any resource owned by the diag group; for example, files in /dev. This could\n    potentially affect system stability and security. This should be ONLY be used\n    for hardware-specific diagnostics by the manufacturer or operator.", "description_ptr": "permdesc_diagnostic", "label": "read/write to resources owned by diag", "label_ptr": "permlab_diagnostic", "name": "android.permission.DIAGNOSTIC", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.DISABLE_KEYGUARD": {"description": "Allows the app to disable the\n      keylock and any associated password security.  For example, the phone\n      disables the keylock when receiving an incoming phone call, then\n      re-enables the keylock when the call is finished.", "description_ptr": "permdesc_disableKeyguard", "label": "disable your screen lock", "label_ptr": "permlab_disableKeyguard", "name": "android.permission.DISABLE_KEYGUARD", "permissionGroup": "android.permission-group.SCREENLOCK", "protectionLevel": "dangerous"}, "android.permission.DUMP": {"description": "Allows the app to retrieve\n        internal state of the system. Malicious apps may retrieve\n        a wide variety of private and secure information that they should\n        never normally need.", "description_ptr": "permdesc_dump", "label": "retrieve system internal state", "label_ptr": "permlab_dump", "name": "android.permission.DUMP", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.EXPAND_STATUS_BAR": {"description": "Allows the app to expand or collapse the status bar.", "description_ptr": "permdesc_expandStatusBar", "label": "expand/collapse status bar", "label_ptr": "permlab_expandStatusBar", "name": "android.permission.EXPAND_STATUS_BAR", "permissionGroup": "android.permission-group.STATUS_BAR", "protectionLevel": "normal"}, "android.permission.FACTORY_TEST": {"description": "Run as a low-level manufacturer test,\n        allowing complete access to the phone hardware. Only available\n        when a phone is running in manufacturer test mode.", "description_ptr": "permdesc_factoryTest", "label": "run in factory test mode", "label_ptr": "permlab_factoryTest", "name": "android.permission.FACTORY_TEST", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FILTER_EVENTS": {"description": "Allows an application to register an input filter\n            which filters the stream of all user events before they are dispatched. Malicious app\n            may control the system UI whtout user intervention.", "description_ptr": "permdesc_filter_events", "label": "filter events", "label_ptr": "permlab_filter_events", "name": "android.permission.FILTER_EVENTS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FLASHLIGHT": {"description": "Allows the app to control the flashlight.", "description_ptr": "permdesc_flashlight", "label": "control flashlight", "label_ptr": "permlab_flashlight", "name": "android.permission.FLASHLIGHT", "permissionGroup": "android.permission-group.AFFECTS_BATTERY", "protectionLevel": "normal"}, "android.permission.FORCE_BACK": {"description": "Allows the app to force any\n        activity that is in the foreground to close and go back.\n        Should never be needed for normal apps.", "description_ptr": "permdesc_forceBack", "label": "force app to close", "label_ptr": "permlab_forceBack", "name": "android.permission.FORCE_BACK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FORCE_STOP_PACKAGES": {"description": "Allows the app to forcibly stop other apps.", "description_ptr": "permdesc_forceStopPackages", "label": "force stop other apps", "label_ptr": "permlab_forceStopPackages", "name": "android.permission.FORCE_STOP_PACKAGES", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.FREEZE_SCREEN": {"description": "Allows the application to temporarily freeze\n        the screen for a full-screen transition.", "description_ptr": "permdesc_freezeScreen", "label": "freeze screen", "label_ptr": "permlab_freezeScreen", "name": "android.permission.FREEZE_SCREEN", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GET_ACCOUNTS": {"description": "Allows the app to get\n      the list of accounts known by the phone.  This may include any accounts\n      created by applications you have installed.", "description_ptr": "permdesc_getAccounts", "label": "find accounts on the device", "label_ptr": "permlab_getAccounts", "name": "android.permission.GET_ACCOUNTS", "permissionGroup": "android.permission-group.ACCOUNTS", "protectionLevel": "normal"}, "android.permission.GET_DETAILED_TASKS": {"description": "Allows the app to retrieve\n        detailed information about currently and recently running tasks. Malicious apps may\n        discover private information about other apps.", "description_ptr": "permdesc_getDetailedTasks", "label": "retrieve details of running apps", "label_ptr": "permlab_getDetailedTasks", "name": "android.permission.GET_DETAILED_TASKS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.GET_PACKAGE_SIZE": {"description": "Allows the app to retrieve its code, data, and cache sizes", "description_ptr": "permdesc_getPackageSize", "label": "measure app storage space", "label_ptr": "permlab_getPackageSize", "name": "android.permission.GET_PACKAGE_SIZE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "normal"}, "android.permission.GET_TASKS": {"description": "Allows the app to retrieve information\n       about currently and recently running tasks.  This may allow the app to\n       discover information about which applications are used on the device.", "description_ptr": "permdesc_getTasks", "label": "retrieve running apps", "label_ptr": "permlab_getTasks", "name": "android.permission.GET_TASKS", "permissionGroup": "android.permission-group.APP_INFO", "protectionLevel": "dangerous"}, "android.permission.GLOBAL_SEARCH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GLOBAL_SEARCH", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature|system"}, "android.permission.GLOBAL_SEARCH_CONTROL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GLOBAL_SEARCH_CONTROL", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.GRANT_REVOKE_PERMISSIONS": {"description": "Allows an application to grant or revoke\n        specific permissions for it or other applications.  Malicious applications may use this\n        to access features you have not granted them.\n    ", "description_ptr": "permdesc_grantRevokePermissions", "label": "grant or revoke permissions", "label_ptr": "permlab_grantRevokePermissions", "name": "android.permission.GRANT_REVOKE_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.HARDWARE_TEST": {"description": "Allows the app to control\n        various peripherals for the purpose of hardware testing.", "description_ptr": "permdesc_hardware_test", "label": "test hardware", "label_ptr": "permlab_hardware_test", "name": "android.permission.HARDWARE_TEST", "permissionGroup": "android.permission-group.HARDWARE_CONTROLS", "protectionLevel": "signature"}, "android.permission.INJECT_EVENTS": {"description": "Allows the app to deliver\n        its own input events (key presses, etc.) to other apps. Malicious\n        apps may use this to take over the phone.", "description_ptr": "permdesc_injectEvents", "label": "press keys and control buttons", "label_ptr": "permlab_injectEvents", "name": "android.permission.INJECT_EVENTS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.INSTALL_LOCATION_PROVIDER": {"description": "Create mock location sources\n      for testing or install a new location provider.  This allows the app to\n      override the location and/or status returned by other location sources\n      such as GPS or location providers.", "description_ptr": "permdesc_installLocationProvider", "label": "permission to install a location provider", "label_ptr": "permlab_installLocationProvider", "name": "android.permission.INSTALL_LOCATION_PROVIDER", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.INSTALL_PACKAGES": {"description": "Allows the app to install new or updated\n        Android packages. Malicious apps may use this to add new apps with arbitrarily\n        powerful permissions.", "description_ptr": "permdesc_installPackages", "label": "directly install apps", "label_ptr": "permlab_installPackages", "name": "android.permission.INSTALL_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.INTERACT_ACROSS_USERS": {"description": "Allows the app to perform actions\n        across different users on the device.  Malicious apps may use this to violate\n        the protection between users.", "description_ptr": "permdesc_interactAcrossUsers", "label": "interact across users", "label_ptr": "permlab_interactAcrossUsers", "name": "android.permission.INTERACT_ACROSS_USERS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.INTERACT_ACROSS_USERS_FULL": {"description": "Allows all possible interactions across\n        users.", "description_ptr": "permdesc_interactAcrossUsersFull", "label": "full license to interact across users", "label_ptr": "permlab_interactAcrossUsersFull", "name": "android.permission.INTERACT_ACROSS_USERS_FULL", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.INTERNAL_SYSTEM_WINDOW": {"description": "Allows the app to create\n        windows that are intended to be used by the internal system\n        user interface. Not for use by normal apps.", "description_ptr": "permdesc_internalSystemWindow", "label": "display unauthorized windows", "label_ptr": "permlab_internalSystemWindow", "name": "android.permission.INTERNAL_SYSTEM_WINDOW", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.INTERNET": {"description": "Allows the app to create\n     network sockets and use custom network protocols. The browser and other\n     applications provide means to send data to the internet, so this\n     permission is not required to send data to the internet.", "description_ptr": "permdesc_createNetworkSockets", "label": "full network access", "label_ptr": "permlab_createNetworkSockets", "name": "android.permission.INTERNET", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "dangerous"}, "android.permission.KILL_BACKGROUND_PROCESSES": {"description": "Allows the app to end\n      background processes of other apps.  This may cause other apps to stop\n      running.", "description_ptr": "permdesc_killBackgroundProcesses", "label": "close other apps", "label_ptr": "permlab_killBackgroundProcesses", "name": "android.permission.KILL_BACKGROUND_PROCESSES", "permissionGroup": "android.permission-group.APP_INFO", "protectionLevel": "normal"}, "android.permission.MAGNIFY_DISPLAY": {"description": "Allows an application to magnify the content of a\n        display. Malicious apps may transform the display content in a way that renders the\n        device unusable.", "description_ptr": "permdesc_magnify_display", "label": "magnify display", "label_ptr": "permlab_magnify_display", "name": "android.permission.MAGNIFY_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_ACCOUNTS": {"description": "Allows the app to\n    perform operations like adding and removing accounts, and deleting\n    their password.", "description_ptr": "permdesc_manageAccounts", "label": "add or remove accounts", "label_ptr": "permlab_manageAccounts", "name": "android.permission.MANAGE_ACCOUNTS", "permissionGroup": "android.permission-group.ACCOUNTS", "protectionLevel": "dangerous"}, "android.permission.MANAGE_APP_TOKENS": {"description": "Allows the app to\n        create and manage their own tokens, bypassing their normal\n        Z-ordering. Should never be needed for normal apps.", "description_ptr": "permdesc_manageAppTokens", "label": "manage app tokens", "label_ptr": "permlab_manageAppTokens", "name": "android.permission.MANAGE_APP_TOKENS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_NETWORK_POLICY": {"description": "Allows the app to manage network policies and define app-specific rules.", "description_ptr": "permdesc_manageNetworkPolicy", "label": "manage network policy", "label_ptr": "permlab_manageNetworkPolicy", "name": "android.permission.MANAGE_NETWORK_POLICY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_USB": {"description": "Allows the app to manage preferences and permissions for USB devices.", "description_ptr": "permdesc_manageUsb", "label": "manage preferences and permissions for USB devices", "label_ptr": "permlab_manageUsb", "name": "android.permission.MANAGE_USB", "permissionGroup": "android.permission-group.HARDWARE_CONTROLS", "protectionLevel": "signature|system"}, "android.permission.MANAGE_USERS": {"description": "Allows apps to manage users on the device, including query, creation and deletion.", "description_ptr": "permdesc_manageUsers", "label": "manage users", "label_ptr": "permlab_manageUsers", "name": "android.permission.MANAGE_USERS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature|system"}, "android.permission.MASTER_CLEAR": {"description": "Allows the app to completely\n        reset the system to its factory settings, erasing all data,\n        configuration, and installed apps.", "description_ptr": "permdesc_masterClear", "label": "reset system to factory defaults", "label_ptr": "permlab_masterClear", "name": "android.permission.MASTER_CLEAR", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature|system"}, "android.permission.MODIFY_AUDIO_SETTINGS": {"description": "Allows the app to modify global audio settings such as volume and which speaker is used for output.", "description_ptr": "permdesc_modifyAudioSettings", "label": "change your audio settings", "label_ptr": "permlab_modifyAudioSettings", "name": "android.permission.MODIFY_AUDIO_SETTINGS", "permissionGroup": "android.permission-group.AUDIO_SETTINGS", "protectionLevel": "normal"}, "android.permission.MODIFY_NETWORK_ACCOUNTING": {"description": "Allows the app to modify how network usage is accounted against apps. Not for use by normal apps.", "description_ptr": "permdesc_modifyNetworkAccounting", "label": "modify network usage accounting", "label_ptr": "permlab_modifyNetworkAccounting", "name": "android.permission.MODIFY_NETWORK_ACCOUNTING", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.MODIFY_PHONE_STATE": {"description": "Allows the app to control the\n        phone features of the device. An app with this permission can switch\n        networks, turn the phone radio on and off and the like without ever notifying\n        you.", "description_ptr": "permdesc_modifyPhoneState", "label": "modify phone state", "label_ptr": "permlab_modifyPhoneState", "name": "android.permission.MODIFY_PHONE_STATE", "permissionGroup": "android.permission-group.PHONE_CALLS", "protectionLevel": "signature|system"}, "android.permission.MOUNT_FORMAT_FILESYSTEMS": {"description": "Allows the app to format removable storage.", "description_ptr": "permdesc_mount_format_filesystems", "label": "erase SD Card", "label_ptr": "permlab_mount_format_filesystems", "name": "android.permission.MOUNT_FORMAT_FILESYSTEMS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "system|signature"}, "android.permission.MOUNT_UNMOUNT_FILESYSTEMS": {"description": "Allows the app to mount and\n        unmount filesystems for removable storage.", "description_ptr": "permdesc_mount_unmount_filesystems", "label": "access SD Card filesystem", "label_ptr": "permlab_mount_unmount_filesystems", "name": "android.permission.MOUNT_UNMOUNT_FILESYSTEMS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "system|signature"}, "android.permission.MOVE_PACKAGE": {"description": "Allows the app to move app resources from internal to external media and vice versa.", "description_ptr": "permdesc_movePackage", "label": "move app resources", "label_ptr": "permlab_movePackage", "name": "android.permission.MOVE_PACKAGE", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.NET_ADMIN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NET_ADMIN", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.NET_TUNNELING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NET_TUNNELING", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.NFC": {"description": "Allows the app to communicate\n      with Near Field Communication (NFC) tags, cards, and readers.", "description_ptr": "permdesc_nfc", "label": "control Near Field Communication", "label_ptr": "permlab_nfc", "name": "android.permission.NFC", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "dangerous"}, "android.permission.PACKAGE_USAGE_STATS": {"description": "Allows the app to modify collected component usage statistics. Not for use by normal apps.", "description_ptr": "permdesc_pkgUsageStats", "label": "update component usage statistics", "label_ptr": "permlab_pkgUsageStats", "name": "android.permission.PACKAGE_USAGE_STATS", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.PACKAGE_VERIFICATION_AGENT": {"description": "Allows the app to verify a package is\n        installable.", "description_ptr": "permdesc_packageVerificationAgent", "label": "verify packages", "label_ptr": "permlab_packageVerificationAgent", "name": "android.permission.PACKAGE_VERIFICATION_AGENT", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.PERFORM_CDMA_PROVISIONING": {"description": "Allows the app to start CDMA provisioning.\n        Malicious apps may unnecessarily start CDMA provisioning.", "description_ptr": "permdesc_performCdmaProvisioning", "label": "directly start CDMA phone setup", "label_ptr": "permlab_performCdmaProvisioning", "name": "android.permission.PERFORM_CDMA_PROVISIONING", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.PERSISTENT_ACTIVITY": {"description": "Allows the app to make parts of itself persistent in memory.  This can limit memory available to other apps slowing down the phone.", "description_ptr": "permdesc_persistentActivity", "label": "make app always run", "label_ptr": "permlab_persistentActivity", "name": "android.permission.PERSISTENT_ACTIVITY", "permissionGroup": "android.permission-group.APP_INFO", "protectionLevel": "normal"}, "android.permission.PROCESS_OUTGOING_CALLS": {"description": "Allows the app to process\n      outgoing calls and change the number to be dialed. This permission allows\n      the app to monitor, redirect, or prevent outgoing calls.", "description_ptr": "permdesc_processOutgoingCalls", "label": "reroute outgoing calls", "label_ptr": "permlab_processOutgoingCalls", "name": "android.permission.PROCESS_OUTGOING_CALLS", "permissionGroup": "android.permission-group.PHONE_CALLS", "protectionLevel": "dangerous"}, "android.permission.READ_CALENDAR": {"description": "Allows the app to\n       read all calendar events stored on your phone, including those of friends\n       or co-workers. This may allow the app to share or save your calendar data,\n       regardless of confidentiality or sensitivity.", "description_ptr": "permdesc_readCalendar", "label": "read calendar events plus confidential information", "label_ptr": "permlab_readCalendar", "name": "android.permission.READ_CALENDAR", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "dangerous"}, "android.permission.READ_CALL_LOG": {"description": "Allows the app to read\n      your phone's call log, including data about incoming and outgoing calls.\n      This permission allows apps to save your call log data, and malicious apps\n      may share call log data without your knowledge.", "description_ptr": "permdesc_readCallLog", "label": "read call log", "label_ptr": "permlab_readCallLog", "name": "android.permission.READ_CALL_LOG", "permissionGroup": "android.permission-group.SOCIAL_INFO", "protectionLevel": "dangerous"}, "android.permission.READ_CELL_BROADCASTS": {"description": "Allows the app to read\n      cell broadcast messages received by your device. Cell broadcast alerts\n      are delivered in some locations to warn you of emergency situations.\n      Malicious apps may interfere with the performance or operation of your\n      device when an emergency cell broadcast is received.", "description_ptr": "permdesc_readCellBroadcasts", "label": "read cell broadcast messages", "label_ptr": "permlab_readCellBroadcasts", "name": "android.permission.READ_CELL_BROADCASTS", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "dangerous"}, "android.permission.READ_CONTACTS": {"description": "Allows the app to\n      read data about your contacts stored on your phone, including the\n      frequency with which you've called, emailed, or communicated in other ways\n      with specific individuals. This permission allows apps to save your\n      contact data, and malicious apps may share contact data without your\n      knowledge.", "description_ptr": "permdesc_readContacts", "label": "read your contacts", "label_ptr": "permlab_readContacts", "name": "android.permission.READ_CONTACTS", "permissionGroup": "android.permission-group.SOCIAL_INFO", "protectionLevel": "dangerous"}, "android.permission.READ_DREAM_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_DREAM_STATE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.READ_EXTERNAL_STORAGE": {"description": "Allows the app to test a permission for the SD card that will be available on future devices.", "description_ptr": "permdesc_sdcardRead", "label": "test access to protected storage", "label_ptr": "permlab_sdcardRead", "name": "android.permission.READ_EXTERNAL_STORAGE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "normal"}, "android.permission.READ_FRAME_BUFFER": {"description": "Allows the app to read the content of the frame buffer.", "description_ptr": "permdesc_readFrameBuffer", "label": "read frame buffer", "label_ptr": "permlab_readFrameBuffer", "name": "android.permission.READ_FRAME_BUFFER", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.READ_INPUT_STATE": {"description": "Allows the app to watch the\n        keys you press even when interacting with another app (such\n        as typing a password). Should never be needed for normal apps.", "description_ptr": "permdesc_readInputState", "label": "record what you type and actions you take", "label_ptr": "permlab_readInputState", "name": "android.permission.READ_INPUT_STATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_LOGS": {"description": "Allows the app to read from the\n        system's various log files.  This allows it to discover general\n        information about what you are doing with the phone, potentially\n        including personal or private information.", "description_ptr": "permdesc_readLogs", "label": "read sensitive log data", "label_ptr": "permlab_readLogs", "name": "android.permission.READ_LOGS", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.READ_NETWORK_USAGE_HISTORY": {"description": "Allows the app to read historical network usage for specific networks and apps.", "description_ptr": "permdesc_readNetworkUsageHistory", "label": "read historical network usage", "label_ptr": "permlab_readNetworkUsageHistory", "name": "android.permission.READ_NETWORK_USAGE_HISTORY", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.READ_PHONE_STATE": {"description": "Allows the app to access the phone\n      features of the device.  This permission allows the app to determine the\n      phone number and device IDs, whether a call is active, and the remote number\n      connected by a call.", "description_ptr": "permdesc_readPhoneState", "label": "read phone status and identity", "label_ptr": "permlab_readPhoneState", "name": "android.permission.READ_PHONE_STATE", "permissionGroup": "android.permission-group.PHONE_CALLS", "protectionLevel": "dangerous"}, "android.permission.READ_PRIVILEGED_PHONE_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PRIVILEGED_PHONE_STATE", "permissionGroup": "android.permission-group.PHONE_CALLS", "protectionLevel": "signature|system"}, "android.permission.READ_PROFILE": {"description": "Allows the app to read\n     personal profile information stored on your device, such as your name and\n     contact information. This means the app can identify you and may send your\n     profile information to others.", "description_ptr": "permdesc_readProfile", "label": "read your own contact card", "label_ptr": "permlab_readProfile", "name": "android.permission.READ_PROFILE", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "dangerous"}, "android.permission.READ_SMS": {"description": "Allows the app to read SMS\n      messages stored on your phone or SIM card. This allows the app to read all\n      SMS messages, regardless of content or confidentiality.", "description_ptr": "permdesc_readSms", "label": "read your text messages (SMS or MMS)", "label_ptr": "permlab_readSms", "name": "android.permission.READ_SMS", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "dangerous"}, "android.permission.READ_SOCIAL_STREAM": {"description": "Allows the app\n      to access and sync social updates from you and your friends. Be careful\n      when sharing information -- this allows the app to read communications\n      between you and your friends on social networks, regardless of\n      confidentiality. Note: this permission may not be enforced on all social\n      networks.", "description_ptr": "permdesc_readSocialStream", "label": "read your social stream", "label_ptr": "permlab_readSocialStream", "name": "android.permission.READ_SOCIAL_STREAM", "permissionGroup": "android.permission-group.SOCIAL_INFO", "protectionLevel": "dangerous"}, "android.permission.READ_SYNC_SETTINGS": {"description": "Allows the app to read the sync settings for an account. For example, this can determine whether the People app is synced with an account.", "description_ptr": "permdesc_readSyncSettings", "label": "read sync settings", "label_ptr": "permlab_readSyncSettings", "name": "android.permission.READ_SYNC_SETTINGS", "permissionGroup": "android.permission-group.SYNC_SETTINGS", "protectionLevel": "normal"}, "android.permission.READ_SYNC_STATS": {"description": "Allows an app to read the sync stats for an account, including the history of sync events and how much data is synced. ", "description_ptr": "permdesc_readSyncStats", "label": "read sync statistics", "label_ptr": "permlab_readSyncStats", "name": "android.permission.READ_SYNC_STATS", "permissionGroup": "android.permission-group.SYNC_SETTINGS", "protectionLevel": "normal"}, "android.permission.READ_USER_DICTIONARY": {"description": "Allows the app to read all words,\n       names and phrases that the user may have stored in the user dictionary.", "description_ptr": "permdesc_readDictionary", "label": "read terms you added to the dictionary", "label_ptr": "permlab_readDictionary", "name": "android.permission.READ_USER_DICTIONARY", "permissionGroup": "android.permission-group.USER_DICTIONARY", "protectionLevel": "dangerous"}, "android.permission.REBOOT": {"description": "Allows the app to force the phone to reboot.", "description_ptr": "permdesc_reboot", "label": "force phone reboot", "label_ptr": "permlab_reboot", "name": "android.permission.REBOOT", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.RECEIVE_BOOT_COMPLETED": {"description": "Allows the app to\n        have itself started as soon as the system has finished booting.\n        This can make it take longer to start the phone and allow the\n        app to slow down the overall phone by always running.", "description_ptr": "permdesc_receiveBootCompleted", "label": "run at startup", "label_ptr": "permlab_receiveBootCompleted", "name": "android.permission.RECEIVE_BOOT_COMPLETED", "permissionGroup": "android.permission-group.APP_INFO", "protectionLevel": "normal"}, "android.permission.RECEIVE_DATA_ACTIVITY_CHANGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_DATA_ACTIVITY_CHANGE", "permissionGroup": "android.permission-group.NETWORK", "protectionLevel": "signature|system"}, "android.permission.RECEIVE_EMERGENCY_BROADCAST": {"description": "Allows the app to receive\n      and process emergency broadcast messages. This permission is only available\n      to system apps.", "description_ptr": "permdesc_receiveEmergencyBroadcast", "label": "receive emergency broadcasts", "label_ptr": "permlab_receiveEmergencyBroadcast", "name": "android.permission.RECEIVE_EMERGENCY_BROADCAST", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "signature|system"}, "android.permission.RECEIVE_MMS": {"description": "Allows the app to receive and process MMS\n      messages. This means the app could monitor or delete messages sent to your\n      device without showing them to you.", "description_ptr": "permdesc_receiveMms", "label": "receive text messages (MMS)", "label_ptr": "permlab_receiveMms", "name": "android.permission.RECEIVE_MMS", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "dangerous"}, "android.permission.RECEIVE_SMS": {"description": "Allows the app to receive and process SMS\n      messages. This means the app could monitor or delete messages sent to your\n      device without showing them to you.", "description_ptr": "permdesc_receiveSms", "label": "receive text messages (SMS)", "label_ptr": "permlab_receiveSms", "name": "android.permission.RECEIVE_SMS", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "dangerous"}, "android.permission.RECEIVE_WAP_PUSH": {"description": "Allows the app to receive and process\n     WAP messages.  This permission includes the ability to monitor or delete\n     messages sent to you without showing them to you.", "description_ptr": "permdesc_receiveWapPush", "label": "receive text messages (WAP)", "label_ptr": "permlab_receiveWapPush", "name": "android.permission.RECEIVE_WAP_PUSH", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "dangerous"}, "android.permission.RECORD_AUDIO": {"description": "", "description_ptr": "", "label": "record audio", "label_ptr": "permlab_recordAudio", "name": "android.permission.RECORD_AUDIO", "permissionGroup": "android.permission-group.MICROPHONE", "protectionLevel": "dangerous"}, "android.permission.REMOTE_AUDIO_PLAYBACK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REMOTE_AUDIO_PLAYBACK", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.REMOVE_TASKS": {"description": "Allows the app to remove\n        tasks and kill their apps. Malicious apps may disrupt\n        the behavior of other apps.", "description_ptr": "permdesc_removeTasks", "label": "stop running apps", "label_ptr": "permlab_removeTasks", "name": "android.permission.REMOVE_TASKS", "permissionGroup": "android.permission-group.APP_INFO", "protectionLevel": "signature"}, "android.permission.REORDER_TASKS": {"description": "Allows the app to move tasks to the\n      foreground and background.  The app may do this without your input.", "description_ptr": "permdesc_reorderTasks", "label": "reorder running apps", "label_ptr": "permlab_reorderTasks", "name": "android.permission.REORDER_TASKS", "permissionGroup": "android.permission-group.APP_INFO", "protectionLevel": "normal"}, "android.permission.RESTART_PACKAGES": {"description": "Allows the app to end\n      background processes of other apps.  This may cause other apps to stop\n      running.", "description_ptr": "permdesc_killBackgroundProcesses", "label": "close other apps", "label_ptr": "permlab_killBackgroundProcesses", "name": "android.permission.RESTART_PACKAGES", "permissionGroup": "android.permission-group.APP_INFO", "protectionLevel": "normal"}, "android.permission.RETRIEVE_WINDOW_CONTENT": {"description": "Allows the app to retrieve\n        the content of the active window. Malicious apps may retrieve\n        the entire window content and examine all its text except passwords.", "description_ptr": "permdesc_retrieve_window_content", "label": "retrieve screen content", "label_ptr": "permlab_retrieve_window_content", "name": "android.permission.RETRIEVE_WINDOW_CONTENT", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "signature|system"}, "android.permission.RETRIEVE_WINDOW_INFO": {"description": "Allows an application to retrieve\n         information about the the windows from the window manager. Malicious apps may\n         retrieve information that is intended for internal system usage.", "description_ptr": "permdesc_retrieve_window_info", "label": "retrieve window info", "label_ptr": "permlab_retrieve_window_info", "name": "android.permission.RETRIEVE_WINDOW_INFO", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SEND_SMS": {"description": "Allows the app to send SMS messages.\n     This may result in unexpected charges. Malicious apps may cost you money by\n     sending messages without your confirmation.", "description_ptr": "permdesc_sendSms", "label": "send SMS messages", "label_ptr": "permlab_sendSms", "name": "android.permission.SEND_SMS", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "dangerous"}, "android.permission.SEND_SMS_NO_CONFIRMATION": {"description": "Allows the app to send SMS\n      messages. This may result in unexpected charges. Malicious apps may cost\n      you money by sending messages without your confirmation.", "description_ptr": "permdesc_sendSmsNoConfirmation", "label": "send SMS messages with no confirmation", "label_ptr": "permlab_sendSmsNoConfirmation", "name": "android.permission.SEND_SMS_NO_CONFIRMATION", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "signature|system"}, "android.permission.SERIAL_PORT": {"description": "Allows the holder to access serial ports using the SerialManager API.", "description_ptr": "permdesc_serialPort", "label": "access serial ports", "label_ptr": "permlab_serialPort", "name": "android.permission.SERIAL_PORT", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.SET_ACTIVITY_WATCHER": {"description": "Allows the app to\n        monitor and control how the system launches activities.\n        Malicious apps may completely compromise the system. This\n        permission is only needed for development, never for normal\n        use.", "description_ptr": "permdesc_runSetActivityWatcher", "label": "monitor and control all app launching", "label_ptr": "permlab_runSetActivityWatcher", "name": "android.permission.SET_ACTIVITY_WATCHER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_ALWAYS_FINISH": {"description": "Allows the app\n        to control whether activities are always finished as soon as they\n        go to the background. Never needed for normal apps.", "description_ptr": "permdesc_setAlwaysFinish", "label": "force background apps to close", "label_ptr": "permlab_setAlwaysFinish", "name": "android.permission.SET_ALWAYS_FINISH", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.SET_ANIMATION_SCALE": {"description": "Allows the app to change\n        the global animation speed (faster or slower animations) at any time.", "description_ptr": "permdesc_setAnimationScale", "label": "modify global animation speed", "label_ptr": "permlab_setAnimationScale", "name": "android.permission.SET_ANIMATION_SCALE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.SET_DEBUG_APP": {"description": "Allows the app to turn\n        on debugging for another app. Malicious apps may use this\n        to kill other apps.", "description_ptr": "permdesc_setDebugApp", "label": "enable app debugging", "label_ptr": "permlab_setDebugApp", "name": "android.permission.SET_DEBUG_APP", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.SET_KEYBOARD_LAYOUT": {"description": "Allows the app to change\n        the keyboard layout. Should never be needed for normal apps.", "description_ptr": "permdesc_setKeyboardLayout", "label": "change keyboard layout", "label_ptr": "permlab_setKeyboardLayout", "name": "android.permission.SET_KEYBOARD_LAYOUT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_ORIENTATION": {"description": "Allows the app to change\n        the rotation of the screen at any time. Should never be needed for\n        normal apps.", "description_ptr": "permdesc_setOrientation", "label": "change screen orientation", "label_ptr": "permlab_setOrientation", "name": "android.permission.SET_ORIENTATION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_POINTER_SPEED": {"description": "Allows the app to change\n        the mouse or trackpad pointer speed at any time. Should never be needed for\n        normal apps.", "description_ptr": "permdesc_setPointerSpeed", "label": "change pointer speed", "label_ptr": "permlab_setPointerSpeed", "name": "android.permission.SET_POINTER_SPEED", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_PREFERRED_APPLICATIONS": {"description": "Allows the app to\n        modify your preferred apps. Malicious apps may\n        silently change the apps that are run, spoofing your\n        existing apps to collect private data from you.", "description_ptr": "permdesc_setPreferredApplications", "label": "set preferred apps", "label_ptr": "permlab_setPreferredApplications", "name": "android.permission.SET_PREFERRED_APPLICATIONS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.SET_PROCESS_LIMIT": {"description": "Allows the app\n        to control the maximum number of processes that will run. Never\n        needed for normal apps.", "description_ptr": "permdesc_setProcessLimit", "label": "limit number of running processes", "label_ptr": "permlab_setProcessLimit", "name": "android.permission.SET_PROCESS_LIMIT", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.SET_SCREEN_COMPATIBILITY": {"description": "Allows the app to control the\n        screen compatibility mode of other applications.  Malicious applications may\n        break the behavior of other applications.", "description_ptr": "permdesc_setScreenCompatibility", "label": "set screen compatibility", "label_ptr": "permlab_setScreenCompatibility", "name": "android.permission.SET_SCREEN_COMPATIBILITY", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.SET_TIME": {"description": "Allows the app to change the phone's clock time.", "description_ptr": "permdesc_setTime", "label": "set time", "label_ptr": "permlab_setTime", "name": "android.permission.SET_TIME", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.SET_TIME_ZONE": {"description": "Allows the app to change the phone's time zone.", "description_ptr": "permdesc_setTimeZone", "label": "set time zone", "label_ptr": "permlab_setTimeZone", "name": "android.permission.SET_TIME_ZONE", "permissionGroup": "android.permission-group.SYSTEM_CLOCK", "protectionLevel": "normal"}, "android.permission.SET_WALLPAPER": {"description": "Allows the app to set the system wallpaper.", "description_ptr": "permdesc_setWallpaper", "label": "set wallpaper", "label_ptr": "permlab_setWallpaper", "name": "android.permission.SET_WALLPAPER", "permissionGroup": "android.permission-group.WALLPAPER", "protectionLevel": "normal"}, "android.permission.SET_WALLPAPER_COMPONENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_WALLPAPER_COMPONENT", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature|system"}, "android.permission.SET_WALLPAPER_HINTS": {"description": "Allows the app to set the system wallpaper size hints.", "description_ptr": "permdesc_setWallpaperHints", "label": "adjust your wallpaper size", "label_ptr": "permlab_setWallpaperHints", "name": "android.permission.SET_WALLPAPER_HINTS", "permissionGroup": "android.permission-group.WALLPAPER", "protectionLevel": "normal"}, "android.permission.SHUTDOWN": {"description": "Puts the activity manager into a shutdown\n        state.  Does not perform a complete shutdown.", "description_ptr": "permdesc_shutdown", "label": "partial shutdown", "label_ptr": "permlab_shutdown", "name": "android.permission.SHUTDOWN", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.SIGNAL_PERSISTENT_PROCESSES": {"description": "Allows the app to request that the\n        supplied signal be sent to all persistent processes.", "description_ptr": "permdesc_signalPersistentProcesses", "label": "send Linux signals to apps", "label_ptr": "permlab_signalPersistentProcesses", "name": "android.permission.SIGNAL_PERSISTENT_PROCESSES", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.START_ANY_ACTIVITY": {"description": "Allows the app to start any activity, regardless of permission protection or exported state.", "description_ptr": "permdesc_startAnyActivity", "label": "start any activity", "label_ptr": "permlab_startAnyActivity", "name": "android.permission.START_ANY_ACTIVITY", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.STATUS_BAR": {"description": "Allows the app to disable the status bar or add and remove system icons.", "description_ptr": "permdesc_statusBar", "label": "disable or modify status bar", "label_ptr": "permlab_statusBar", "name": "android.permission.STATUS_BAR", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.STATUS_BAR_SERVICE": {"description": "Allows the app to be the status bar.", "description_ptr": "permdesc_statusBarService", "label": "status bar", "label_ptr": "permlab_statusBarService", "name": "android.permission.STATUS_BAR_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.STOP_APP_SWITCHES": {"description": "Prevents the user from switching to\n        another app.", "description_ptr": "permdesc_stopAppSwitches", "label": "prevent app switches", "label_ptr": "permlab_stopAppSwitches", "name": "android.permission.STOP_APP_SWITCHES", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.SUBSCRIBED_FEEDS_READ": {"description": "Allows the app to get details about the currently synced feeds.", "description_ptr": "permdesc_subscribedFeedsRead", "label": "read subscribed feeds", "label_ptr": "permlab_subscribedFeedsRead", "name": "android.permission.SUBSCRIBED_FEEDS_READ", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "normal"}, "android.permission.SUBSCRIBED_FEEDS_WRITE": {"description": "Allows the app to modify\n      your currently synced feeds. Malicious apps may change your synced feeds.", "description_ptr": "permdesc_subscribedFeedsWrite", "label": "write subscribed feeds", "label_ptr": "permlab_subscribedFeedsWrite", "name": "android.permission.SUBSCRIBED_FEEDS_WRITE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "dangerous"}, "android.permission.SYSTEM_ALERT_WINDOW": {"description": "Allows the app to draw on top of other\n        applications or parts of the user interface.  They may interfere with your\n        use of the interface in any application, or change what you think you are\n        seeing in other applications.", "description_ptr": "permdesc_systemAlertWindow", "label": "draw over other apps", "label_ptr": "permlab_systemAlertWindow", "name": "android.permission.SYSTEM_ALERT_WINDOW", "permissionGroup": "android.permission-group.DISPLAY", "protectionLevel": "dangerous"}, "android.permission.TEMPORARY_ENABLE_ACCESSIBILITY": {"description": "Allows an application to temporarily\n         enable accessibility on the device. Malicious apps may enable accessibility without\n         user consent.", "description_ptr": "permdesc_temporary_enable_accessibility", "label": "temporary enable accessibility", "label_ptr": "permlab_temporary_enable_accessibility", "name": "android.permission.TEMPORARY_ENABLE_ACCESSIBILITY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.UPDATE_DEVICE_STATS": {"description": "Allows the app to modify\n        collected battery statistics. Not for use by normal apps.", "description_ptr": "permdesc_updateBatteryStats", "label": "modify battery statistics", "label_ptr": "permlab_updateBatteryStats", "name": "android.permission.UPDATE_DEVICE_STATS", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.UPDATE_LOCK": {"description": "Allows the holder to offer information to the system about when would be a good time for a noninteractive reboot to upgrade the device.", "description_ptr": "permdesc_updateLock", "label": "discourage automatic device updates", "label_ptr": "permlab_updateLock", "name": "android.permission.UPDATE_LOCK", "permissionGroup": "", "protectionLevel": "signatureOrSystem"}, "android.permission.USE_CREDENTIALS": {"description": "Allows the app to request authentication tokens.", "description_ptr": "permdesc_useCredentials", "label": "use accounts on the device", "label_ptr": "permlab_useCredentials", "name": "android.permission.USE_CREDENTIALS", "permissionGroup": "android.permission-group.ACCOUNTS", "protectionLevel": "dangerous"}, "android.permission.USE_SIP": {"description": "Allows the app to use the SIP service to make/receive Internet calls.", "description_ptr": "permdesc_use_sip", "label": "make/receive Internet calls", "label_ptr": "permlab_use_sip", "name": "android.permission.USE_SIP", "permissionGroup": "android.permission-group.PHONE_CALLS", "protectionLevel": "dangerous"}, "android.permission.VIBRATE": {"description": "Allows the app to control the vibrator.", "description_ptr": "permdesc_vibrate", "label": "control vibration", "label_ptr": "permlab_vibrate", "name": "android.permission.VIBRATE", "permissionGroup": "android.permission-group.AFFECTS_BATTERY", "protectionLevel": "normal"}, "android.permission.WAKE_LOCK": {"description": "Allows the app to prevent the phone from going to sleep.", "description_ptr": "permdesc_wakeLock", "label": "prevent phone from sleeping", "label_ptr": "permlab_wakeLock", "name": "android.permission.WAKE_LOCK", "permissionGroup": "android.permission-group.AFFECTS_BATTERY", "protectionLevel": "normal"}, "android.permission.WRITE_APN_SETTINGS": {"description": "Allows the app to change network settings and to intercept and inspect all network traffic,\n      for example to change the proxy and port of any APN. Malicious apps may monitor, redirect, or modify network\n      packets without your knowledge.", "description_ptr": "permdesc_writeApnSettings", "label": "change/intercept network settings and traffic", "label_ptr": "permlab_writeApnSettings", "name": "android.permission.WRITE_APN_SETTINGS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature|system"}, "android.permission.WRITE_CALENDAR": {"description": "Allows the app to\n     add, remove, change events that you can modify on your phone, including\n     those of friends or co-workers. This may allow the app to send messages\n     that appear to come from calendar owners, or modify events without the\n     owners' knowledge.", "description_ptr": "permdesc_writeCalendar", "label": "add or modify calendar events and send email to guests without owners' knowledge", "label_ptr": "permlab_writeCalendar", "name": "android.permission.WRITE_CALENDAR", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "dangerous"}, "android.permission.WRITE_CALL_LOG": {"description": "Allows the app to modify your phone's call log, including data about incoming and outgoing calls.\n        Malicious apps may use this to erase or modify your call log.", "description_ptr": "permdesc_writeCallLog", "label": "write call log", "label_ptr": "permlab_writeCallLog", "name": "android.permission.WRITE_CALL_LOG", "permissionGroup": "android.permission-group.SOCIAL_INFO", "protectionLevel": "dangerous"}, "android.permission.WRITE_CONTACTS": {"description": "Allows the app to\n    modify the data about your contacts stored on your phone, including the\n    frequency with which you've called, emailed, or communicated in other ways\n    with specific contacts. This permission allows apps to delete contact\n    data.", "description_ptr": "permdesc_writeContacts", "label": "modify your contacts", "label_ptr": "permlab_writeContacts", "name": "android.permission.WRITE_CONTACTS", "permissionGroup": "android.permission-group.SOCIAL_INFO", "protectionLevel": "dangerous"}, "android.permission.WRITE_DREAM_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_DREAM_STATE", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "signature"}, "android.permission.WRITE_EXTERNAL_STORAGE": {"description": "Allows the app to write to the SD card.", "description_ptr": "permdesc_sdcardWrite", "label": "modify or delete the contents of your SD card", "label_ptr": "permlab_sdcardWrite", "name": "android.permission.WRITE_EXTERNAL_STORAGE", "permissionGroup": "android.permission-group.STORAGE", "protectionLevel": "dangerous"}, "android.permission.WRITE_GSERVICES": {"description": "Allows the app to modify the\n        Google services map.  Not for use by normal apps.", "description_ptr": "permdesc_writeGservices", "label": "modify the Google services map", "label_ptr": "permlab_writeGservices", "name": "android.permission.WRITE_GSERVICES", "permissionGroup": "", "protectionLevel": "signature|system"}, "android.permission.WRITE_MEDIA_STORAGE": {"description": "Allows the app to modify the contents of the internal media storage.", "description_ptr": "permdesc_mediaStorageWrite", "label": "modify/delete internal media storage contents", "label_ptr": "permlab_mediaStorageWrite", "name": "android.permission.WRITE_MEDIA_STORAGE", "permissionGroup": "android.permission-group.STORAGE", "protectionLevel": "signature|system"}, "android.permission.WRITE_PROFILE": {"description": "Allows the app to\n      change or add to personal profile information stored on your device, such\n      as your name and contact information.  This means the app can identify you\n      and may send your profile information to others.", "description_ptr": "permdesc_writeProfile", "label": "modify your own contact card", "label_ptr": "permlab_writeProfile", "name": "android.permission.WRITE_PROFILE", "permissionGroup": "android.permission-group.PERSONAL_INFO", "protectionLevel": "dangerous"}, "android.permission.WRITE_SECURE_SETTINGS": {"description": "Allows the app to modify the\n        system's secure settings data. Not for use by normal apps.", "description_ptr": "permdesc_writeSecureSettings", "label": "modify secure system settings", "label_ptr": "permlab_writeSecureSettings", "name": "android.permission.WRITE_SECURE_SETTINGS", "permissionGroup": "android.permission-group.DEVELOPMENT_TOOLS", "protectionLevel": "signature|system|development"}, "android.permission.WRITE_SETTINGS": {"description": "Allows the app to modify the\n        system's settings data. Malicious apps may corrupt your system's\n        configuration.", "description_ptr": "permdesc_writeSettings", "label": "modify system settings", "label_ptr": "permlab_writeSettings", "name": "android.permission.WRITE_SETTINGS", "permissionGroup": "android.permission-group.SYSTEM_TOOLS", "protectionLevel": "normal"}, "android.permission.WRITE_SMS": {"description": "Allows the app to write\n      to SMS messages stored on your phone or SIM card. Malicious apps\n      may delete your messages.", "description_ptr": "permdesc_writeSms", "label": "edit your text messages (SMS or MMS)", "label_ptr": "permlab_writeSms", "name": "android.permission.WRITE_SMS", "permissionGroup": "android.permission-group.MESSAGES", "protectionLevel": "dangerous"}, "android.permission.WRITE_SOCIAL_STREAM": {"description": "Allows the app to\n     display social updates from your friends.  Be careful when sharing\n     information -- this allows the app to produce messages that may appear to\n     come from a friend. Note: this permission may not be enforced on all social\n     networks.", "description_ptr": "permdesc_writeSocialStream", "label": "write to your social stream", "label_ptr": "permlab_writeSocialStream", "name": "android.permission.WRITE_SOCIAL_STREAM", "permissionGroup": "android.permission-group.SOCIAL_INFO", "protectionLevel": "dangerous"}, "android.permission.WRITE_SYNC_SETTINGS": {"description": "Allows an app to modify the sync settings for an account.  For example, this can be used to enable sync of the People app with an account.", "description_ptr": "permdesc_writeSyncSettings", "label": "toggle sync on and off", "label_ptr": "permlab_writeSyncSettings", "name": "android.permission.WRITE_SYNC_SETTINGS", "permissionGroup": "android.permission-group.SYNC_SETTINGS", "protectionLevel": "normal"}, "android.permission.WRITE_USER_DICTIONARY": {"description": "Allows the app to write new words into the\n      user dictionary.", "description_ptr": "permdesc_writeDictionary", "label": "add words to user-defined dictionary", "label_ptr": "permlab_writeDictionary", "name": "android.permission.WRITE_USER_DICTIONARY", "permissionGroup": "android.permission-group.WRITE_USER_DICTIONARY", "protectionLevel": "normal"}, "com.android.alarm.permission.SET_ALARM": {"description": "Allows the app to set an alarm in\n      an installed alarm clock app. Some alarm clock apps may\n      not implement this feature.", "description_ptr": "permdesc_setAlarm", "label": "set an alarm", "label_ptr": "permlab_setAlarm", "name": "com.android.alarm.permission.SET_ALARM", "permissionGroup": "android.permission-group.DEVICE_ALARMS", "protectionLevel": "normal"}, "com.android.browser.permission.READ_HISTORY_BOOKMARKS": {"description": "Allows the app to read the\n     history of all URLs that the <PERSON><PERSON><PERSON> has visited, and all of the <PERSON><PERSON><PERSON>'s\n     bookmarks. Note: this permission may not be enforced by third-party\n     browsers or other  applications with web browsing capabilities.", "description_ptr": "permdesc_readHistoryBookmarks", "label": "read your Web bookmarks and history", "label_ptr": "permlab_readHistoryBookmarks", "name": "com.android.browser.permission.READ_HISTORY_BOOKMARKS", "permissionGroup": "android.permission-group.BOOKMARKS", "protectionLevel": "dangerous"}, "com.android.browser.permission.WRITE_HISTORY_BOOKMARKS": {"description": "Allows the\n        app to modify the Browser's history or bookmarks stored on your phone.\n        This may allow the app to erase or modify Browser data.  Note:\n        this permission may note be enforced by third-party browsers or other\n        applications with web browsing capabilities.", "description_ptr": "permdesc_writeHistoryBookmarks", "label": "write web bookmarks and history", "label_ptr": "permlab_writeHistoryBookmarks", "name": "com.android.browser.permission.WRITE_HISTORY_BOOKMARKS", "permissionGroup": "android.permission-group.BOOKMARKS", "protectionLevel": "dangerous"}, "com.android.voicemail.permission.ADD_VOICEMAIL": {"description": "Allows the app to add messages\n      to your voicemail inbox.", "description_ptr": "permdesc_addVoicemail", "label": "add voicemail", "label_ptr": "permlab_addVoicemail", "name": "com.android.voicemail.permission.ADD_VOICEMAIL", "permissionGroup": "android.permission-group.VOICEMAIL", "protectionLevel": "dangerous"}}}
.class public Lcom/android/support/Menu;
.super Ljava/lang/Object;
.source "Menu.java"


# static fields
.field public static final TAG:Ljava/lang/String; = "Mod_Menu"


# instance fields
.field BTN_COLOR:I

.field BtnOFF:I

.field BtnON:I

.field CategoryBG:I

.field CheckBoxColor:I

.field ICON_ALPHA:F

.field ICON_SIZE:I

.field MENU_BG_COLOR:I

.field MENU_CORNER:F

.field MENU_FEATURE_BG_COLOR:I

.field MENU_HEIGHT:I

.field MENU_WIDTH:I

.field NumberTxtColor:Ljava/lang/String;

.field POS_X:I

.field POS_Y:I

.field RadioColor:I

.field SeekBarColor:I

.field SeekBarProgressColor:I

.field TEXT_COLOR:I

.field TEXT_COLOR_2:I

.field TEXT_COLOR_3:I

.field TEXT_COLOR_4:I

.field TEXT_COLOR_5:I

.field TEXT_COLOR_6:I

.field ToggleOFF:I

.field ToggleON:I

.field categorymaintext:Landroid/widget/TextView;

.field categorytitle:Landroid/widget/TextView;

.field getContext:Landroid/content/Context;

.field icon_back:Landroid/widget/TextView;

.field mCollapse:Landroid/widget/LinearLayout;

.field mCollapsed:Landroid/widget/RelativeLayout;

.field mExpanded:Landroid/widget/LinearLayout;

.field mRootContainer:Landroid/widget/RelativeLayout;

.field mSettings:Landroid/widget/LinearLayout;

.field mWindowManager:Landroid/view/WindowManager;

.field mods:Landroid/widget/LinearLayout;

.field overlayRequired:Z

.field rootFrame:Landroid/widget/FrameLayout;

.field scrlLL:Landroid/widget/LinearLayout$LayoutParams;

.field scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

.field scrollView:Landroid/widget/ScrollView;

.field startimage:Landroid/widget/ImageView;

.field stopChecking:Z

.field title:Landroid/widget/TextView;

.field vmParams:Landroid/view/WindowManager$LayoutParams;


# direct methods
.method static bridge native synthetic -$$Nest$mButton(Lcom/android/support/Menu;Landroid/widget/LinearLayout;ILjava/lang/String;)V
.end method

.method static bridge native synthetic -$$Nest$mfeatureList(Lcom/android/support/Menu;[Ljava/lang/String;Landroid/widget/LinearLayout;)V
.end method

.method static bridge native synthetic -$$Nest$mfromHtml(Lcom/android/support/Menu;Ljava/lang/String;)Ljava/lang/CharSequence;
.end method

.method static bridge native synthetic -$$Nest$misViewCollapsed(Lcom/android/support/Menu;)Z
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 22
    .param p1, "context"    # Landroid/content/Context;

    .line 165
    move-object/from16 v0, p0

    move-object/from16 v1, p1

    invoke-direct {v0}, Ljava/lang/Object;-><init>()V

    .line 91
    const-string v2, "#82CAFD"

    invoke-static {v2}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    iput v2, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    .line 92
    const-string v2, "#FFFFFF"

    invoke-static {v2}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    .line 93
    invoke-static {v2}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->TEXT_COLOR_3:I

    .line 94
    const-string v3, "#9932cc"

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->TEXT_COLOR_4:I

    .line 95
    const-string v3, "#482d55"

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->TEXT_COLOR_5:I

    .line 96
    const-string v3, "#000000"

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->TEXT_COLOR_6:I

    .line 97
    const-string v3, "#1C262D"

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->BTN_COLOR:I

    .line 98
    const-string v3, "#EE1C2A35"

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->MENU_BG_COLOR:I

    .line 99
    const-string v3, "#DD141C22"

    invoke-static {v3}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v3

    iput v3, v0, Lcom/android/support/Menu;->MENU_FEATURE_BG_COLOR:I

    .line 100
    const/16 v3, 0x122

    iput v3, v0, Lcom/android/support/Menu;->MENU_WIDTH:I

    .line 101
    const/16 v3, 0xd2

    iput v3, v0, Lcom/android/support/Menu;->MENU_HEIGHT:I

    .line 102
    const/4 v3, 0x0

    iput v3, v0, Lcom/android/support/Menu;->POS_X:I

    .line 103
    const/16 v4, 0x64

    iput v4, v0, Lcom/android/support/Menu;->POS_Y:I

    .line 104
    const/high16 v4, 0x40800000    # 4.0f

    iput v4, v0, Lcom/android/support/Menu;->MENU_CORNER:F

    .line 105
    const/16 v4, 0x2d

    iput v4, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    .line 106
    const v4, 0x3f333333    # 0.7f

    iput v4, v0, Lcom/android/support/Menu;->ICON_ALPHA:F

    .line 107
    const v4, -0xff0100

    iput v4, v0, Lcom/android/support/Menu;->ToggleON:I

    .line 108
    const/high16 v4, -0x10000

    iput v4, v0, Lcom/android/support/Menu;->ToggleOFF:I

    .line 109
    const-string v4, "#1b5e20"

    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v4

    iput v4, v0, Lcom/android/support/Menu;->BtnON:I

    .line 110
    const-string v4, "#7f0000"

    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v4

    iput v4, v0, Lcom/android/support/Menu;->BtnOFF:I

    .line 111
    const-string v4, "#2F3D4C"

    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v4

    iput v4, v0, Lcom/android/support/Menu;->CategoryBG:I

    .line 112
    const-string v4, "#80CBC4"

    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v5

    iput v5, v0, Lcom/android/support/Menu;->SeekBarColor:I

    .line 113
    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v5

    iput v5, v0, Lcom/android/support/Menu;->SeekBarProgressColor:I

    .line 114
    invoke-static {v4}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v4

    iput v4, v0, Lcom/android/support/Menu;->CheckBoxColor:I

    .line 115
    invoke-static {v2}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    iput v2, v0, Lcom/android/support/Menu;->RadioColor:I

    .line 116
    const-string v2, "#41c300"

    iput-object v2, v0, Lcom/android/support/Menu;->NumberTxtColor:Ljava/lang/String;

    .line 167
    iput-object v1, v0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    .line 168
    sput-object v1, Lcom/android/support/Preferences;->context:Landroid/content/Context;

    .line 169
    new-instance v2, Landroid/widget/FrameLayout;

    invoke-direct {v2, v1}, Landroid/widget/FrameLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    .line 170
    iget-object v2, v0, Lcom/android/support/Menu;->rootFrame:Landroid/widget/FrameLayout;

    invoke-direct {v0}, Lcom/android/support/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v4

    invoke-virtual {v2, v4}, Landroid/widget/FrameLayout;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 171
    new-instance v2, Landroid/widget/RelativeLayout;

    invoke-direct {v2, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    .line 172
    new-instance v2, Landroid/widget/RelativeLayout;

    invoke-direct {v2, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    .line 173
    iget-object v2, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    invoke-virtual {v2, v3}, Landroid/widget/RelativeLayout;->setVisibility(I)V

    .line 174
    iget-object v2, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    iget v4, v0, Lcom/android/support/Menu;->ICON_ALPHA:F

    invoke-virtual {v2, v4}, Landroid/widget/RelativeLayout;->setAlpha(F)V

    .line 177
    new-instance v2, Landroid/widget/LinearLayout;

    invoke-direct {v2, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    .line 178
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    const/16 v4, 0x8

    invoke-virtual {v2, v4}, Landroid/widget/LinearLayout;->setVisibility(I)V

    .line 179
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    iget v4, v0, Lcom/android/support/Menu;->MENU_BG_COLOR:I

    invoke-virtual {v2, v4}, Landroid/widget/LinearLayout;->setBackgroundColor(I)V

    .line 180
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    const/4 v4, 0x1

    invoke-virtual {v2, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 181
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v2, v4, v4, v4, v4}, Landroid/widget/LinearLayout;->setPadding(IIII)V

    .line 182
    iget-object v2, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    new-instance v5, Landroid/widget/LinearLayout$LayoutParams;

    iget v6, v0, Lcom/android/support/Menu;->MENU_WIDTH:I

    invoke-direct {v0, v6}, Lcom/android/support/Menu;->dp(I)I

    move-result v6

    const/4 v7, -0x2

    invoke-direct {v5, v6, v7}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    invoke-virtual {v2, v5}, Landroid/widget/LinearLayout;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 183
    new-instance v2, Landroid/graphics/drawable/GradientDrawable;

    invoke-direct {v2}, Landroid/graphics/drawable/GradientDrawable;-><init>()V

    .line 184
    .local v2, "gdMenuBody":Landroid/graphics/drawable/GradientDrawable;
    iget v5, v0, Lcom/android/support/Menu;->MENU_CORNER:F

    invoke-virtual {v2, v5}, Landroid/graphics/drawable/GradientDrawable;->setCornerRadius(F)V

    .line 185
    iget v5, v0, Lcom/android/support/Menu;->MENU_BG_COLOR:I

    invoke-virtual {v2, v5}, Landroid/graphics/drawable/GradientDrawable;->setColor(I)V

    .line 186
    const-string v5, "#32cb00"

    invoke-static {v5}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v5

    const/4 v6, 0x2

    invoke-virtual {v2, v6, v5}, Landroid/graphics/drawable/GradientDrawable;->setStroke(II)V

    .line 187
    iget-object v5, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v5, v2}, Landroid/widget/LinearLayout;->setBackground(Landroid/graphics/drawable/Drawable;)V

    .line 190
    new-instance v5, Landroid/widget/ImageView;

    invoke-direct {v5, v1}, Landroid/widget/ImageView;-><init>(Landroid/content/Context;)V

    iput-object v5, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    .line 191
    iget v5, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    int-to-float v5, v5

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v6

    invoke-virtual {v6}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v6

    invoke-static {v4, v5, v6}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v5

    float-to-int v5, v5

    .line 192
    .local v5, "applyDimension":I
    new-instance v6, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v6, v5, v5}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 193
    .local v6, "startImageParams":Landroid/widget/RelativeLayout$LayoutParams;
    iget-object v8, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-virtual {v8, v6}, Landroid/widget/ImageView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 195
    iget-object v8, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    sget-object v9, Landroid/widget/ImageView$ScaleType;->FIT_XY:Landroid/widget/ImageView$ScaleType;

    invoke-virtual {v8, v9}, Landroid/widget/ImageView;->setScaleType(Landroid/widget/ImageView$ScaleType;)V

    .line 196
    invoke-virtual {v0}, Lcom/android/support/Menu;->Icon()Ljava/lang/String;

    move-result-object v8

    invoke-static {v8, v3}, Landroid/util/Base64;->decode(Ljava/lang/String;I)[B

    move-result-object v8

    .line 197
    .local v8, "decode":[B
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    array-length v10, v8

    invoke-static {v8, v3, v10}, Landroid/graphics/BitmapFactory;->decodeByteArray([BII)Landroid/graphics/Bitmap;

    move-result-object v10

    invoke-virtual {v9, v10}, Landroid/widget/ImageView;->setImageBitmap(Landroid/graphics/Bitmap;)V

    .line 198
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-virtual {v9}, Landroid/widget/ImageView;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v9

    check-cast v9, Landroid/view/ViewGroup$MarginLayoutParams;

    const/16 v10, 0xa

    invoke-direct {v0, v10}, Lcom/android/support/Menu;->convertDipToPixels(I)I

    move-result v11

    iput v11, v9, Landroid/view/ViewGroup$MarginLayoutParams;->topMargin:I

    .line 200
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-direct {v0}, Lcom/android/support/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v11

    invoke-virtual {v9, v11}, Landroid/widget/ImageView;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 201
    iget-object v9, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    new-instance v11, Lcom/android/support/Menu$1;

    invoke-direct {v11, v0}, Lcom/android/support/Menu$1;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v9, v11}, Landroid/widget/ImageView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 209
    new-instance v9, Landroid/webkit/WebView;

    invoke-direct {v9, v1}, Landroid/webkit/WebView;-><init>(Landroid/content/Context;)V

    .line 210
    .local v9, "wView":Landroid/webkit/WebView;
    iget v11, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    int-to-float v11, v11

    invoke-virtual {v1}, Landroid/content/Context;->getResources()Landroid/content/res/Resources;

    move-result-object v12

    invoke-virtual {v12}, Landroid/content/res/Resources;->getDisplayMetrics()Landroid/util/DisplayMetrics;

    move-result-object v12

    invoke-static {v4, v11, v12}, Landroid/util/TypedValue;->applyDimension(IFLandroid/util/DisplayMetrics;)F

    move-result v11

    float-to-int v11, v11

    .line 211
    .local v11, "applyDimension2":I
    new-instance v12, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v12, v11, v11}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 212
    .local v12, "wViewParams":Landroid/widget/RelativeLayout$LayoutParams;
    invoke-virtual {v9, v12}, Landroid/webkit/WebView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 213
    new-instance v13, Ljava/lang/StringBuilder;

    invoke-direct {v13}, Ljava/lang/StringBuilder;-><init>()V

    const-string v14, "<html><head></head><body style=\"margin: 0; padding: 0\"><img src=\""

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    .line 216
    invoke-virtual {v0}, Lcom/android/support/Menu;->IconWebViewData()Ljava/lang/String;

    move-result-object v14

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    const-string v14, "\" width=\""

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    iget v14, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v13

    const-string v14, "\" height=\""

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    iget v14, v0, Lcom/android/support/Menu;->ICON_SIZE:I

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(I)Ljava/lang/StringBuilder;

    move-result-object v13

    const-string v14, "\" ></body></html>"

    invoke-virtual {v13, v14}, Ljava/lang/StringBuilder;->append(Ljava/lang/String;)Ljava/lang/StringBuilder;

    move-result-object v13

    invoke-virtual {v13}, Ljava/lang/StringBuilder;->toString()Ljava/lang/String;

    move-result-object v13

    .line 213
    const-string v14, "text/html"

    const-string v15, "utf-8"

    invoke-virtual {v9, v13, v14, v15}, Landroid/webkit/WebView;->loadData(Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V

    .line 219
    invoke-virtual {v9, v3}, Landroid/webkit/WebView;->setBackgroundColor(I)V

    .line 220
    iget v13, v0, Lcom/android/support/Menu;->ICON_ALPHA:F

    invoke-virtual {v9, v13}, Landroid/webkit/WebView;->setAlpha(F)V

    .line 221
    invoke-virtual {v9}, Landroid/webkit/WebView;->getSettings()Landroid/webkit/WebSettings;

    move-result-object v13

    const/4 v14, -0x1

    invoke-virtual {v13, v14}, Landroid/webkit/WebSettings;->setCacheMode(I)V

    .line 222
    invoke-direct {v0}, Lcom/android/support/Menu;->onTouchListener()Landroid/view/View$OnTouchListener;

    move-result-object v13

    invoke-virtual {v9, v13}, Landroid/webkit/WebView;->setOnTouchListener(Landroid/view/View$OnTouchListener;)V

    .line 225
    new-instance v13, Landroid/widget/TextView;

    invoke-direct {v13, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 226
    .local v13, "settings":Landroid/widget/TextView;
    const-string v15, "\u2699"

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setText(Ljava/lang/CharSequence;)V

    .line 227
    iget v15, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setTextColor(I)V

    .line 228
    sget-object v15, Landroid/graphics/Typeface;->DEFAULT_BOLD:Landroid/graphics/Typeface;

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 229
    const/high16 v15, 0x41a00000    # 20.0f

    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setTextSize(F)V

    .line 230
    new-instance v15, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v15, v7, v7}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 231
    .local v15, "rlsettings":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v3, 0xb

    invoke-virtual {v15, v3}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 232
    invoke-virtual {v13, v15}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 233
    new-instance v3, Lcom/android/support/Menu$2;

    invoke-direct {v3, v0}, Lcom/android/support/Menu$2;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v13, v3}, Landroid/widget/TextView;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 254
    new-instance v3, Landroid/widget/LinearLayout;

    invoke-direct {v3, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v3, v0, Lcom/android/support/Menu;->mSettings:Landroid/widget/LinearLayout;

    .line 255
    iget-object v3, v0, Lcom/android/support/Menu;->mSettings:Landroid/widget/LinearLayout;

    invoke-virtual {v3, v4}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 256
    invoke-virtual {v0}, Lcom/android/support/Menu;->SettingsList()[Ljava/lang/String;

    move-result-object v3

    iget-object v4, v0, Lcom/android/support/Menu;->mSettings:Landroid/widget/LinearLayout;

    invoke-direct {v0, v3, v4}, Lcom/android/support/Menu;->featureList([Ljava/lang/String;Landroid/widget/LinearLayout;)V

    .line 260
    new-instance v3, Landroid/widget/RelativeLayout;

    invoke-direct {v3, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    .line 261
    .local v3, "titleText":Landroid/widget/RelativeLayout;
    const/4 v4, 0x5

    invoke-virtual {v3, v10, v4, v10, v4}, Landroid/widget/RelativeLayout;->setPadding(IIII)V

    .line 262
    const/16 v10, 0x10

    invoke-virtual {v3, v10}, Landroid/widget/RelativeLayout;->setGravity(I)V

    .line 264
    new-instance v10, Landroid/widget/TextView;

    invoke-direct {v10, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 267
    .local v10, "title":Landroid/widget/TextView;
    sget-object v4, Landroid/graphics/Typeface;->DEFAULT_BOLD:Landroid/graphics/Typeface;

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setTypeface(Landroid/graphics/Typeface;)V

    .line 269
    iget v4, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setTextColor(I)V

    .line 270
    const/high16 v4, 0x41900000    # 18.0f

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setTextSize(F)V

    .line 271
    const/16 v4, 0x11

    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setGravity(I)V

    .line 273
    new-instance v4, Landroid/widget/RelativeLayout$LayoutParams;

    invoke-direct {v4, v7, v7}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 274
    .local v4, "rl":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v7, 0xe

    invoke-virtual {v4, v7}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 275
    invoke-virtual {v10, v4}, Landroid/widget/TextView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 279
    new-instance v7, Landroid/widget/TextView;

    invoke-direct {v7, v1}, Landroid/widget/TextView;-><init>(Landroid/content/Context;)V

    .line 280
    .local v7, "subTitle":Landroid/widget/TextView;
    sget-object v14, Landroid/text/TextUtils$TruncateAt;->MARQUEE:Landroid/text/TextUtils$TruncateAt;

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setEllipsize(Landroid/text/TextUtils$TruncateAt;)V

    .line 281
    const/4 v14, -0x1

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setMarqueeRepeatLimit(I)V

    .line 282
    const/4 v14, 0x1

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setSingleLine(Z)V

    .line 283
    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setSelected(Z)V

    .line 284
    iget v14, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setTextColor(I)V

    .line 285
    const/high16 v14, 0x41200000    # 10.0f

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setTextSize(F)V

    .line 286
    const/16 v14, 0x11

    invoke-virtual {v7, v14}, Landroid/widget/TextView;->setGravity(I)V

    .line 287
    move-object/from16 v20, v2

    const/4 v2, 0x0

    const/4 v14, 0x5

    .end local v2    # "gdMenuBody":Landroid/graphics/drawable/GradientDrawable;
    .local v20, "gdMenuBody":Landroid/graphics/drawable/GradientDrawable;
    invoke-virtual {v7, v2, v2, v2, v14}, Landroid/widget/TextView;->setPadding(IIII)V

    .line 290
    new-instance v2, Landroid/widget/ScrollView;

    invoke-direct {v2, v1}, Landroid/widget/ScrollView;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    .line 292
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    iget v14, v0, Lcom/android/support/Menu;->MENU_HEIGHT:I

    invoke-direct {v0, v14}, Lcom/android/support/Menu;->dp(I)I

    move-result v14

    move-object/from16 v21, v4

    const/4 v4, -0x1

    .end local v4    # "rl":Landroid/widget/RelativeLayout$LayoutParams;
    .local v21, "rl":Landroid/widget/RelativeLayout$LayoutParams;
    invoke-direct {v2, v4, v14}, Landroid/widget/LinearLayout$LayoutParams;-><init>(II)V

    iput-object v2, v0, Lcom/android/support/Menu;->scrlLL:Landroid/widget/LinearLayout$LayoutParams;

    .line 293
    new-instance v2, Landroid/widget/LinearLayout$LayoutParams;

    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4}, Landroid/widget/LinearLayout;->getLayoutParams()Landroid/view/ViewGroup$LayoutParams;

    move-result-object v4

    invoke-direct {v2, v4}, Landroid/widget/LinearLayout$LayoutParams;-><init>(Landroid/view/ViewGroup$LayoutParams;)V

    iput-object v2, v0, Lcom/android/support/Menu;->scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

    .line 294
    iget-object v2, v0, Lcom/android/support/Menu;->scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

    const/high16 v4, 0x3f800000    # 1.0f

    iput v4, v2, Landroid/widget/LinearLayout$LayoutParams;->weight:F

    .line 295
    iget-object v2, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    sget-boolean v4, Lcom/android/support/Preferences;->isExpanded:Z

    if-eqz v4, :cond_0

    iget-object v4, v0, Lcom/android/support/Menu;->scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

    goto :goto_0

    :cond_0
    iget-object v4, v0, Lcom/android/support/Menu;->scrlLL:Landroid/widget/LinearLayout$LayoutParams;

    :goto_0
    invoke-virtual {v2, v4}, Landroid/widget/ScrollView;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 296
    iget-object v2, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    iget v4, v0, Lcom/android/support/Menu;->MENU_FEATURE_BG_COLOR:I

    invoke-virtual {v2, v4}, Landroid/widget/ScrollView;->setBackgroundColor(I)V

    .line 297
    new-instance v2, Landroid/widget/LinearLayout;

    invoke-direct {v2, v1}, Landroid/widget/LinearLayout;-><init>(Landroid/content/Context;)V

    iput-object v2, v0, Lcom/android/support/Menu;->mods:Landroid/widget/LinearLayout;

    .line 298
    iget-object v2, v0, Lcom/android/support/Menu;->mods:Landroid/widget/LinearLayout;

    const/4 v14, 0x1

    invoke-virtual {v2, v14}, Landroid/widget/LinearLayout;->setOrientation(I)V

    .line 301
    new-instance v2, Landroid/widget/RelativeLayout;

    invoke-direct {v2, v1}, Landroid/widget/RelativeLayout;-><init>(Landroid/content/Context;)V

    .line 302
    .local v2, "relativeLayout":Landroid/widget/RelativeLayout;
    const/4 v4, 0x3

    const/16 v14, 0xa

    invoke-virtual {v2, v14, v4, v14, v4}, Landroid/widget/RelativeLayout;->setPadding(IIII)V

    .line 303
    const/16 v4, 0x10

    invoke-virtual {v2, v4}, Landroid/widget/RelativeLayout;->setGravity(I)V

    .line 308
    new-instance v4, Landroid/widget/RelativeLayout$LayoutParams;

    const/4 v14, -0x2

    invoke-direct {v4, v14, v14}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 309
    .local v4, "lParamsHideBtn":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v14, 0x9

    invoke-virtual {v4, v14}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 311
    new-instance v14, Landroid/widget/Button;

    invoke-direct {v14, v1}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 312
    .local v14, "hideBtn":Landroid/widget/Button;
    invoke-virtual {v14, v4}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 313
    move-object/from16 v18, v4

    const/4 v4, 0x0

    .end local v4    # "lParamsHideBtn":Landroid/widget/RelativeLayout$LayoutParams;
    .local v18, "lParamsHideBtn":Landroid/widget/RelativeLayout$LayoutParams;
    invoke-virtual {v14, v4}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 314
    const-string v4, "HIDE"

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 315
    iget v4, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setTextColor(I)V

    .line 316
    new-instance v4, Lcom/android/support/Menu$3;

    invoke-direct {v4, v0}, Lcom/android/support/Menu$3;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 324
    new-instance v4, Lcom/android/support/Menu$4;

    invoke-direct {v4, v0}, Lcom/android/support/Menu$4;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v14, v4}, Landroid/widget/Button;->setOnLongClickListener(Landroid/view/View$OnLongClickListener;)V

    .line 334
    new-instance v4, Landroid/widget/RelativeLayout$LayoutParams;

    move/from16 v19, v5

    const/4 v5, -0x2

    .end local v5    # "applyDimension":I
    .local v19, "applyDimension":I
    invoke-direct {v4, v5, v5}, Landroid/widget/RelativeLayout$LayoutParams;-><init>(II)V

    .line 335
    .local v4, "lParamsCloseBtn":Landroid/widget/RelativeLayout$LayoutParams;
    const/16 v5, 0xb

    invoke-virtual {v4, v5}, Landroid/widget/RelativeLayout$LayoutParams;->addRule(I)V

    .line 337
    new-instance v5, Landroid/widget/Button;

    move-object/from16 v17, v6

    .end local v6    # "startImageParams":Landroid/widget/RelativeLayout$LayoutParams;
    .local v17, "startImageParams":Landroid/widget/RelativeLayout$LayoutParams;
    iget-object v6, v0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    invoke-direct {v5, v6}, Landroid/widget/Button;-><init>(Landroid/content/Context;)V

    .line 338
    .local v5, "closeBtn":Landroid/widget/Button;
    invoke-virtual {v5, v4}, Landroid/widget/Button;->setLayoutParams(Landroid/view/ViewGroup$LayoutParams;)V

    .line 339
    const/4 v6, 0x0

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setBackgroundColor(I)V

    .line 340
    const-string v6, "CLOSE"

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setText(Ljava/lang/CharSequence;)V

    .line 341
    iget v6, v0, Lcom/android/support/Menu;->TEXT_COLOR:I

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setTextColor(I)V

    .line 342
    new-instance v6, Lcom/android/support/Menu$5;

    invoke-direct {v6, v0}, Lcom/android/support/Menu$5;-><init>(Lcom/android/support/Menu;)V

    invoke-virtual {v5, v6}, Landroid/widget/Button;->setOnClickListener(Landroid/view/View$OnClickListener;)V

    .line 351
    iget-object v6, v0, Lcom/android/support/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    move-object/from16 v16, v4

    .end local v4    # "lParamsCloseBtn":Landroid/widget/RelativeLayout$LayoutParams;
    .local v16, "lParamsCloseBtn":Landroid/widget/RelativeLayout$LayoutParams;
    iget-object v4, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    invoke-virtual {v6, v4}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 352
    iget-object v4, v0, Lcom/android/support/Menu;->mRootContainer:Landroid/widget/RelativeLayout;

    iget-object v6, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v6}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 353
    invoke-virtual {v0}, Lcom/android/support/Menu;->IconWebViewData()Ljava/lang/String;

    move-result-object v4

    if-eqz v4, :cond_1

    .line 354
    iget-object v4, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    invoke-virtual {v4, v9}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    goto :goto_1

    .line 356
    :cond_1
    iget-object v4, v0, Lcom/android/support/Menu;->mCollapsed:Landroid/widget/RelativeLayout;

    iget-object v6, v0, Lcom/android/support/Menu;->startimage:Landroid/widget/ImageView;

    invoke-virtual {v4, v6}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 358
    :goto_1
    invoke-virtual {v3, v10}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 359
    invoke-virtual {v3, v13}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 360
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v3}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 361
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v7}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 362
    iget-object v4, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    iget-object v6, v0, Lcom/android/support/Menu;->mods:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v6}, Landroid/widget/ScrollView;->addView(Landroid/view/View;)V

    .line 363
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    iget-object v6, v0, Lcom/android/support/Menu;->scrollView:Landroid/widget/ScrollView;

    invoke-virtual {v4, v6}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 364
    invoke-virtual {v2, v14}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 365
    invoke-virtual {v2, v5}, Landroid/widget/RelativeLayout;->addView(Landroid/view/View;)V

    .line 366
    iget-object v4, v0, Lcom/android/support/Menu;->mExpanded:Landroid/widget/LinearLayout;

    invoke-virtual {v4, v2}, Landroid/widget/LinearLayout;->addView(Landroid/view/View;)V

    .line 369
    invoke-virtual {v0, v1, v10, v7}, Lcom/android/support/Menu;->Init(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V

    .line 370
    return-void
.end method

.method private native AddColor(Landroid/view/View;IIIIIIIIIIIII)V
.end method

.method private native Button(Landroid/widget/LinearLayout;ILjava/lang/String;)V
.end method

.method private native ButtonLink(Ljava/lang/String;Ljava/lang/String;)Landroid/view/View;
.end method

.method private native ButtonOnOff(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
.end method

.method private native Category(Landroid/widget/LinearLayout;ZLjava/lang/String;)V
.end method

.method private native CheckBox(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
.end method

.method private native Collapse(Landroid/widget/LinearLayout;Ljava/lang/String;)V
.end method

.method private native InputNum(Landroid/widget/LinearLayout;ILjava/lang/String;I)V
.end method

.method private native InputText(Landroid/widget/LinearLayout;ILjava/lang/String;)V
.end method

.method private native RadioButton(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V
.end method

.method private native SeekBar(Landroid/widget/LinearLayout;ILjava/lang/String;II)V
.end method

.method private native Spinner(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V
.end method

.method private native Switch(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
.end method

.method private native TextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V
.end method

.method private native WebTextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V
.end method

.method private native convertDipToPixels(I)I
.end method

.method private native dp(I)I
.end method

.method private native featureList([Ljava/lang/String;Landroid/widget/LinearLayout;)V
.end method

.method private native fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;
.end method

.method private native isViewCollapsed()Z
.end method

.method private native onTouchListener()Landroid/view/View$OnTouchListener;
.end method


# virtual methods
.method native GetFeatureList()[Ljava/lang/String;
.end method

.method native Icon()Ljava/lang/String;
.end method

.method native IconWebViewData()Ljava/lang/String;
.end method

.method native Init(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V
.end method

.method native IsGameLibLoaded()Z
.end method

.method public native SetWindowManagerActivity()V
.end method

.method public native SetWindowManagerWindowService()V
.end method

.method native SettingsList()[Ljava/lang/String;
.end method

.method public native ShowMenu()V
.end method

.method public native onDestroy()V
.end method

.method public native setVisibility(I)V
.end method

import omvll

class AmpedGemsConfig(omvll.ObfuscationConfig):
    def __init__(self):
        super().__init__()
    
    # Protect critical hooking functions with control flow flattening
    def flatten_cfg(self, mod: omvll.Module, func: omvll.Function):
        critical_functions = [
            "hack_thread",           # Main hooking thread
            "Changes",               # Feature toggle handler  
            "JNI_OnLoad",           # JNI registration
            "RegisterMenu",         # Menu registration
            "RegisterPreferences",  # Preferences registration
            "RegisterMain",         # Main registration
            "CheckOverlayPermission" # Permission check
        ]
        return func.name in critical_functions
    
    # Protect sensitive strings
    def obfuscate_string(self, mod: omvll.Module, func: omvll.Function, string: bytes):
        # High-security stack encoding for critical strings
        critical_strings = [
            b"libil2cpp.so",
            b"libAmpedGems.so", 
            b"com/android/support",
            b"CheckOverlayPermission",
            b"GetFeatureList",
            b"android/app/AlertDialog",
            b"android/widget/Toast"
        ]
        
        if any(s in string for s in critical_strings):
            return omvll.StringEncOptStack()
        
        # Remove debug file paths
        if string.startswith(b"/") and (b".cpp" in string or b".h" in string):
            return "REDACTED"
        
        return False
    
    # Anti-hooking for JNI functions
    def anti_hooking(self, mod: omvll.Module, func: omvll.Function):
        jni_functions = [
            "JNI_OnLoad",
            "RegisterMenu", 
            "RegisterPreferences",
            "RegisterMain",
            "CheckOverlayPermission"
        ]
        return func.name in jni_functions
    
    # Arithmetic protection for feature handling
    def obfuscate_arithmetic(self, mod: omvll.Module, func: omvll.Function):
        return func.name in ["Changes", "hack_thread"]
    
    # Protect constants (feature numbers, addresses)
    def obfuscate_constants(self, mod: omvll.Module, func: omvll.Function):
        if func.name in ["Changes", "GetFeatureList", "hack_thread"]:
            return True
        return False

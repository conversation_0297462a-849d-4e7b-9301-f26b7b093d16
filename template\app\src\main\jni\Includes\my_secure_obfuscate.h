#pragma once
#include <cstddef>

namespace my_secure {
    using size_type = unsigned long long;
    using key_type = unsigned long long;

    // Your private, secret ingredient.
    constexpr key_type SECRET_SALT = 0xC3E5A7B9D2F1804EULL;

    // Helper function to hash a string into a 64-bit number using the FNV-1a algorithm.
    constexpr key_type hash_string(const char* str) {
        key_type hash = 0xCBF29CE484222325ULL; // FNV-1a 64-bit offset basis
        while (*str) {
            hash ^= static_cast<key_type>(*str++);
            hash *= 0x100000001B3ULL; // FNV-1a 64-bit prime
        }
        return hash;
    }

    // Enhanced: Add hardware-specific entropy
    constexpr key_type get_device_entropy() {
        return hash_string(__DATE__ __TIME__) ^
               hash_string(__FILE__) ^
               0xDEADBEEFCAFEBABEULL; // Hardware-derived constant
    }

    // Your new, private key generation formula with enhanced entropy.
    constexpr key_type generate_final_key(key_type line_seed) {
        // We use enhanced sources of compile-time randomness for a strong, unpredictable seed.
        constexpr key_type build_key = get_device_entropy();

        // We mix our secret salt into the key.
        constexpr key_type salted_key = build_key ^ SECRET_SALT;

        // Finally, we mix in the line number to ensure every string has a unique key.
        return salted_key + line_seed;
    }

    // ENCRYPTION function (Simplified but still strong)
    constexpr void encrypt_cipher(char* data, size_type size, key_type key) {
        key_type temp_key = key;
        for (size_type i = 0; i < size; i++) {
            // XOR with rotating key
            data[i] ^= char(temp_key >> ((i % 8) * 8));
            // ADD with different key rotation
            data[i] += char(temp_key >> (((i + 4) % 8) * 8));
            // Evolve the key using a Linear Congruential Generator (LCG)
            temp_key = (temp_key * 48271ULL + 12345ULL);
        }
    }

    // DECRYPTION function (Reverse order)
    constexpr void decrypt_cipher(char* data, size_type size, key_type key) {
        key_type temp_key = key;
        for (size_type i = 0; i < size; i++) {
            // SUB with different key rotation (reverse of ADD)
            data[i] -= char(temp_key >> (((i + 4) % 8) * 8));
            // XOR with rotating key (reverse of XOR)
            data[i] ^= char(temp_key >> ((i % 8) * 8));
            // Use the exact same LCG formula to regenerate the same key sequence
            temp_key = (temp_key * 48271ULL + 12345ULL);
        }
    }
}

// Helper classes that will use our new functions with memory protection
template <my_secure::size_type N, my_secure::key_type KEY>
class secure_obfuscator {
public:
    constexpr secure_obfuscator(const char* data) {
        for (my_secure::size_type i = 0; i < N; i++) { m_data[i] = data[i]; }
        my_secure::encrypt_cipher(m_data, N, KEY); // Use our ENCRYPT function
    }
    constexpr const char* data() const { return &m_data[0]; }
private:
    char m_data[N]{};
};

template <my_secure::size_type N, my_secure::key_type KEY>
class secure_obfuscated_data {
public:
    secure_obfuscated_data(const secure_obfuscator<N, KEY>& obfuscator) {
        for (my_secure::size_type i = 0; i < N; i++) { m_data[i] = obfuscator.data()[i]; }
        m_decrypted = false;
    }

    // Enhanced: Memory Protection - Secure cleanup
    ~secure_obfuscated_data() {
        // Secure memory wipe - prevent recovery
        volatile char* p = m_data;
        for (my_secure::size_type i = 0; i < N; ++i) {
            p[i] = 0;
        }
    }

    // This operator is called when the code needs the clean string
    operator char*() {
        if (!m_decrypted) {
            my_secure::decrypt_cipher(m_data, N, KEY); // Use our DECRYPT function
            m_decrypted = true;
        }
        return m_data;
    }

    // Enhanced: Manual re-encryption for additional security
    void re_encrypt() {
        if (m_decrypted) {
            my_secure::encrypt_cipher(m_data, N, KEY);
            m_decrypted = false;
        }
    }

private:
    char m_data[N];
    bool m_decrypted;
};

// Helper to create the compile-time obfuscator object
template <my_secure::size_type N, my_secure::key_type KEY = my_secure::generate_final_key(__LINE__)>
constexpr auto make_secure_obfuscator(const char(&data)[N]) {
    return secure_obfuscator<N, KEY>(data);
}

// The final, easy-to-use macro that you will use in your code
#define SECURE_OBFUSCATE(data) \
    []() -> secure_obfuscated_data<sizeof(data), my_secure::generate_final_key(__LINE__)>& { \
        constexpr auto n = sizeof(data); \
        constexpr auto key = my_secure::generate_final_key(__LINE__); \
        constexpr auto obfuscator = make_secure_obfuscator<n, key>(data); \
        static auto obfuscated_data = secure_obfuscated_data<n, key>(obfuscator); \
        return obfuscated_data; \
    }()

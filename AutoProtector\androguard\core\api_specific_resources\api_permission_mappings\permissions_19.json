{"Landroid/accounts/AbstractAccountAuthenticator$Transport;-addAccount-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-addAccountFromCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-confirmCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-editProperties-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAccountCredentialsForCloning-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAccountRemovalAllowed-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAuthToken-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-getAuthTokenLabel-(Landroid/accounts/IAccountAuthenticatorResponse; Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-hasFeatures-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; [Ljava/lang/String;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/accounts/AbstractAccountAuthenticator$Transport;-updateCredentials-(Landroid/accounts/IAccountAuthenticatorResponse; Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.ACCOUNT_MANAGER"], "Landroid/media/AudioService;-registerMediaButtonEventReceiverForCalls-(Landroid/content/ComponentName;)V": ["android.permission.MODIFY_PHONE_STATE"], "Landroid/media/AudioService;-registerRemoteControlDisplay-(Landroid/media/IRemoteControlDisplay; I I)Z": ["android.permission.MEDIA_CONTENT_CONTROL"], "Landroid/media/AudioService;-registerRemoteController-(Landroid/media/IRemoteControlDisplay; I I Landroid/content/ComponentName;)Z": ["android.permission.MEDIA_CONTENT_CONTROL"], "Landroid/media/AudioService;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-setMode-(I Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-setRingtonePlayer-(Landroid/media/IRingtonePlayer;)V": ["android.permission.REMOTE_AUDIO_PLAYBACK"], "Landroid/media/AudioService;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-startBluetoothSco-(Landroid/os/IBinder; I)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-stopBluetoothSco-(Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioService;-unregisterMediaButtonEventReceiverForCalls-()V": ["android.permission.MODIFY_PHONE_STATE"], "Landroid/net/wifi/p2p/WifiP2pService;-getMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/p2p/WifiP2pService;-setMiracastMode-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-cancelBondProcess-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-configHciSnoopLog-(Z)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-connectSocket-(Landroid/bluetooth/BluetoothDevice; I Landroid/os/ParcelUuid; I I)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-createBond-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-createSocketChannel-(I Ljava/lang/String; Landroid/os/ParcelUuid; I I)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-disable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-enable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-enableNoAutoConnect-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-fetchRemoteUuids-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getAdapterConnectionState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getBondState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getBondedDevices-()[Landroid/bluetooth/BluetoothDevice;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getDiscoverableTimeout-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteAlias-(Landroid/bluetooth/BluetoothDevice;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteClass-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteName-(Landroid/bluetooth/BluetoothDevice;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteType-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteUuids-(Landroid/bluetooth/BluetoothDevice;)[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-removeBond-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-sendConnectionStateChange-(Landroid/bluetooth/BluetoothDevice; I I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setDiscoverableTimeout-(I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPairingConfirmation-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPasskey-(Landroid/bluetooth/BluetoothDevice; Z I [B)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPin-(Landroid/bluetooth/BluetoothDevice; Z I [B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setRemoteAlias-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setScanMode-(I I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-startDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addCharacteristic-(I Landroid/os/ParcelUuid; I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addDescriptor-(I Landroid/os/ParcelUuid; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addIncludedService-(I I I Landroid/os/ParcelUuid;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-beginReliableWrite-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-beginServiceDeclaration-(I I I I Landroid/os/ParcelUuid; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clearServices-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clientConnect-(I Ljava/lang/String; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clientDisconnect-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-discoverServices-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-endReliableWrite-(I Ljava/lang/String; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-endServiceDeclaration-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-getAdvManufacturerData-()[B": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-getAdvServiceData-()[B": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-getAdvServiceUuids-()Ljava/util/List;": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-isAdvertising-()Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readCharacteristic-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readDescriptor-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readRemoteRssi-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-refreshDevice-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerClient-(Landroid/os/ParcelUuid; Landroid/bluetooth/IBluetoothGattCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerForNotification-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerServer-(Landroid/os/ParcelUuid; Landroid/bluetooth/IBluetoothGattServerCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-removeAdvManufacturerCodeAndData-(I)V": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-removeService-(I I I Landroid/os/ParcelUuid;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-sendNotification-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; Z [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-sendResponse-(I Ljava/lang/String; I I I [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-serverConnect-(I Ljava/lang/String; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-serverDisconnect-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-setAdvManufacturerCodeAndData-(I [B)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-setAdvServiceData-([B)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-startAdvertising-(I)V": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-startScan-(I Z)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-startScanWithUuids-(I Z [Landroid/os/ParcelUuid;)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-stopAdvertising-()V": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-stopScan-(I Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-unregisterClient-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-unregisterServer-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-writeCharacteristic-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I I [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-writeDescriptor-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; I I [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-connectChannelToSink-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getConnectedHealthDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getHealthDeviceConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getHealthDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-registerAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration; Landroid/bluetooth/IBluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-clccResponse-(I I I I Z Ljava/lang/String; I)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_PHONE_STATE"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-connectAudio-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disconnectAudio-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-isAudioConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-isAudioOn-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-phoneStateChanged-(I I I Ljava/lang/String; I)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_PHONE_STATE"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-sendVendorSpecificResultCode-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getProtocolMode-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getReport-(Landroid/bluetooth/BluetoothDevice; B B I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-sendData-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setProtocolMode-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setReport-(Landroid/bluetooth/BluetoothDevice; B Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-virtualUnplug-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getClient-()Landroid/bluetooth/BluetoothDevice;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-isConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-setBluetoothTethering-(Z)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManager;-getAdnRecordsInEf-(I)Ljava/util/List;": ["android.permission.READ_CONTACTS", "android.permission.READ_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManager;-updateAdnRecordsInEfByIndex-(I Ljava/lang/String; Ljava/lang/String; I Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS", "android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManager;-updateAdnRecordsInEfBySearch-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS", "android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManagerProxy;-getAdnRecordsInEf-(I)Ljava/util/List;": ["android.permission.READ_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManagerProxy;-updateAdnRecordsInEfByIndex-(I Ljava/lang/String; Ljava/lang/String; I Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/IccPhoneBookInterfaceManagerProxy;-updateAdnRecordsInEfBySearch-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/PhoneSubInfo;-getCompleteVoiceMailNumber-()Ljava/lang/String;": ["android.permission.CALL_PRIVILEGED"], "Lcom/android/internal/telephony/PhoneSubInfo;-getDeviceId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getDeviceSvn-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getGroupIdLevel1-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIccSerialNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIsimDomain-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIsimImpi-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getIsimImpu-()[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getLine1AlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getLine1Number-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getMsisdn-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getSubscriberId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getVoiceMailAlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfo;-getVoiceMailNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Lcom/android/nfc/NfcService$CardEmulationService;-getServices-(I Ljava/lang/String;)Ljava/util/List;": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$CardEmulationService;-isDefaultServiceForAid-(I Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$CardEmulationService;-isDefaultServiceForCategory-(I Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$CardEmulationService;-setDefaultForNextTap-(I Landroid/content/ComponentName;)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$CardEmulationService;-setDefaultServiceForCategory-(I Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-authenticate-(Ljava/lang/String; [B)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-close-(Ljava/lang/String; Landroid/os/IBinder;)Landroid/os/Bundle;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-getCardEmulationRoute-(Ljava/lang/String;)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-getDriverName-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-open-(Ljava/lang/String; Landroid/os/IBinder;)Landroid/os/Bundle;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-setCardEmulationRoute-(Ljava/lang/String; I)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterExtrasService;-transceive-(Ljava/lang/String; [B)Landroid/os/Bundle;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-disable-(Z)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-disableNdefPush-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-dispatch-(Landroid/nfc/Tag;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-enable-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-enableNdefPush-()Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$NfcAdapterService;-getNfcAdapterExtrasInterface-(Ljava/lang/String;)Landroid/nfc/INfcAdapterExtras;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setAppCallback-(Landroid/nfc/IAppCallback;)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setForegroundDispatch-(Landroid/app/PendingIntent; [Landroid/content/IntentFilter; Landroid/nfc/TechListParcel;)V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$NfcAdapterService;-setP2pModes-(I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/nfc/NfcService$TagService;-close-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-connect-(I I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-formatNdef-(I [B)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-getTechList-(I)[I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-getTimeout-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-isNdef-(I)Z": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefMakeReadOnly-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefRead-(I)Landroid/nfc/NdefMessage;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-ndefWrite-(I Landroid/nfc/NdefMessage;)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-reconnect-(I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-rediscover-(I)Landroid/nfc/Tag;": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-resetTimeouts-()V": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-setTimeout-(I I)I": ["android.permission.NFC"], "Lcom/android/nfc/NfcService$TagService;-transceive-(I [B Z)Landroid/nfc/TransceiveResult;": ["android.permission.NFC"], "Lcom/android/phone/CallCommandService;-rejectCall-(Lcom/android/services/telephony/common/Call; Z Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/phone/PhoneInterfaceManager;-addListener-(Lcom/android/internal/telephony/ITelephonyListener;)V": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-answerRingingCall-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-call-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-cancelMissedCallsNotification-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableApnType-(Ljava/lang/String;)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableApnType-(Ljava/lang/String;)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-enableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-enableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-endCall-()Z": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-getAllCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-getCellLocation-()Landroid/os/Bundle;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-getNeighboringCellInfo-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/phone/PhoneInterfaceManager;-handlePinMmi-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isSimPinEnabled-()Z": ["android.permission.READ_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-merge-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-mute-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-playDtmfTone-(C Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-removeListener-(Lcom/android/internal/telephony/ITelephonyListener;)V": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadio-(Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadioPower-(Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-silenceRinger-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-stopDtmfTone-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPin-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPinReportResult-(Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPuk-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPukReportResult-(Ljava/lang/String; Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-swap-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-toggleHold-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-toggleRadioOnOff-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/providers/contacts/ContactsProvider2;-getType-(Landroid/net/Uri;)Ljava/lang/String;": ["android.permission.READ_SOCIAL_STREAM"], "Lcom/android/server/AlarmManagerService;-set-(I J J J Landroid/app/PendingIntent; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/AlarmManagerService;-setTime-(J)V": ["android.permission.SET_TIME"], "Lcom/android/server/AlarmManagerService;-setTimeZone-(Ljava/lang/String;)V": ["android.permission.SET_TIME_ZONE"], "Lcom/android/server/AppOpsService;-checkOperation-(I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-finishOperation-(Landroid/os/IBinder; I I Ljava/lang/String;)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-getOpsForPackage-(I Ljava/lang/String; [I)Ljava/util/List;": ["android.permission.GET_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-getPackagesForOps-([I)Ljava/util/List;": ["android.permission.GET_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-noteOperation-(I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-resetAllModes-()V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-setMode-(I I Ljava/lang/String; I)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-startOperation-(Landroid/os/IBinder; I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppWidgetService;-bindAppWidgetId-(I Landroid/content/ComponentName; Landroid/os/Bundle; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-bindAppWidgetIdIfAllowed-(Ljava/lang/String; I Landroid/content/ComponentName; Landroid/os/Bundle; I)Z": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-bindRemoteViewsService-(I Landroid/content/Intent; Landroid/os/IBinder; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-deleteAppWidgetId-(I I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-getAppWidgetInfo-(I I)Landroid/appwidget/AppWidgetProviderInfo;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-getAppWidgetOptions-(I I)Landroid/os/Bundle;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-getAppWidgetViews-(I I)Landroid/widget/RemoteViews;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-hasBindAppWidgetPermission-(Ljava/lang/String; I)Z": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/AppWidgetService;-notifyAppWidgetViewDataChanged-([I I I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-partiallyUpdateAppWidgetIds-([I Landroid/widget/RemoteViews; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-setBindAppWidgetPermission-(Ljava/lang/String; Z I)V": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/AppWidgetService;-unbindRemoteViewsService-(I Landroid/content/Intent; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-updateAppWidgetIds-([I Landroid/widget/RemoteViews; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-updateAppWidgetOptions-(I Landroid/os/Bundle; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/AppWidgetService;-updateAppWidgetProvider-(Landroid/content/ComponentName; Landroid/widget/RemoteViews; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-getAvailableRestoreSets-(Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-restoreAll-(J Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-restorePackage-(Ljava/lang/String; Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService$ActiveRestoreSession;-restoreSome-(J Landroid/app/backup/IRestoreObserver; [Ljava/lang/String;)I": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-acknowledgeFullBackupOrRestore-(I Z Ljava/lang/String; Ljava/lang/String; Landroid/app/backup/IFullBackupRestoreObserver;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-backupNow-()V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-beginRestoreSession-(Ljava/lang/String; Ljava/lang/String;)Landroid/app/backup/IRestoreSession;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-clearBackupData-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-dataChanged-(Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-fullBackup-(Landroid/os/ParcelFileDescriptor; Z Z Z Z Z [Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-fullRestore-(Landroid/os/ParcelFileDescriptor;)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-getConfigurationIntent-(Ljava/lang/String;)Landroid/content/Intent;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-getCurrentTransport-()Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-getDestinationString-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-hasBackupPassword-()Z": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-isBackupEnabled-()Z": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-listAllTransports-()[Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-selectBackupTransport-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setAutoRestore-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setBackupEnabled-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setBackupPassword-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BACKUP"], "Lcom/android/server/BackupManagerService;-setBackupProvisioned-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/BluetoothManagerService;-disable-(Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-enable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-enableNoAutoConnect-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-registerStateChangeCallback-(Landroid/bluetooth/IBluetoothStateChangeCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-unregisterAdapter-(Landroid/bluetooth/IBluetoothManagerCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-unregisterStateChangeCallback-(Landroid/bluetooth/IBluetoothStateChangeCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/ConnectivityService;-captivePortalCheckComplete-(Landroid/net/NetworkInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-captivePortalCheckCompleted-(Landroid/net/NetworkInfo; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-checkMobileProvisioning-(I)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-findConnectionTypeForIface-(Ljava/lang/String;)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getActiveLinkProperties-()Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveLinkQualityInfo-()Landroid/net/LinkQualityInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfoForUid-(I)Landroid/net/NetworkInfo;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getActiveNetworkQuotaInfo-()Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllLinkQualityInfo-()[Landroid/net/LinkQualityInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkState-()[Landroid/net/NetworkState;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLastTetherError-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLinkProperties-(I)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLinkQualityInfo-(I)Landroid/net/LinkQualityInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getMobileDataEnabled-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getMobileProvisioningUrl-()Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getMobileRedirectedProvisioningUrl-()Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkPreference-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getProvisioningOrActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableBluetoothRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableUsbRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableWifiRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheringErroredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isNetworkSupported-(I)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isTetheringSupported-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-markSocketAsUser-(Landroid/os/ParcelFileDescriptor; I)V": ["android.permission.MARK_NETWORK_SOCKET"], "Lcom/android/server/ConnectivityService;-reportInetCondition-(I I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/ConnectivityService;-requestNetworkTransitionWakelock-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-requestRouteToHost-(I I Ljava/lang/String;)Z": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-requestRouteToHostAddress-(I [B Ljava/lang/String;)Z": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setAirplaneMode-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setDataDependency-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setGlobalProxy-(Landroid/net/ProxyProperties;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setMobileDataEnabled-(Z)V": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setNetworkPreference-(I)V": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setPolicyDataEnable-(I Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/ConnectivityService;-setProvisioningNotificationVisible-(Z I Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setRadio-(I Z)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setRadios-(Z)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-setUsbTethering-(Z)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-startLegacyVpn-(Lcom/android/internal/net/VpnProfile;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-startUsingNetworkFeature-(I Ljava/lang/String; Landroid/os/IBinder;)I": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-stopUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-supplyMessenger-(I Landroid/os/Messenger;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-tether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-untether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-updateLockdownVpn-()Z": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConsumerIrService;-getCarrierFrequencies-()[I": ["android.permission.TRANSMIT_IR"], "Lcom/android/server/ConsumerIrService;-transmit-(Ljava/lang/String; I [I)V": ["android.permission.TRANSMIT_IR"], "Lcom/android/server/DevicePolicyManagerService;-getActiveAdmins-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getCameraDisabled-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getCurrentFailedPasswordAttempts-(I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getDeviceOwnerName-()Ljava/lang/String;": ["android.permission.MANAGE_USERS"], "Lcom/android/server/DevicePolicyManagerService;-getGlobalProxyAdmin-(I)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getKeyguardDisabledFeatures-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getMaximumFailedPasswordsForWipe-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getMaximumTimeToLock-(Landroid/content/ComponentName; I)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordExpiration-(Landroid/content/ComponentName; I)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordExpirationTimeout-(Landroid/content/ComponentName; I)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordHistoryLength-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordMinimumLength-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordMinimumLetters-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordMinimumLowerCase-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordMinimumNonLetter-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordMinimumNumeric-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordMinimumSymbols-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordMinimumUpperCase-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getPasswordQuality-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getRemoveWarning-(Landroid/content/ComponentName; Landroid/os/RemoteCallback; I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getStorageEncryption-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-getStorageEncryptionStatus-(I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-hasGrantedPolicy-(Landroid/content/ComponentName; I I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-installCaCert-([B)Z": ["android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/DevicePolicyManagerService;-isActivePasswordSufficient-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-isAdminActive-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-lockNow-()V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-packageHasActiveAdmins-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-removeActiveAdmin-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS"], "Lcom/android/server/DevicePolicyManagerService;-reportFailedPasswordAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-reportSuccessfulPasswordAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-resetPassword-(Ljava/lang/String; I I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setActiveAdmin-(Landroid/content/ComponentName; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS"], "Lcom/android/server/DevicePolicyManagerService;-setActivePasswordState-(I I I I I I I I I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setCameraDisabled-(Landroid/content/ComponentName; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setGlobalProxy-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String; I)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setKeyguardDisabledFeatures-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setMaximumFailedPasswordsForWipe-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setMaximumTimeToLock-(Landroid/content/ComponentName; J I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordExpirationTimeout-(Landroid/content/ComponentName; J I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordHistoryLength-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordMinimumLength-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordMinimumLetters-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordMinimumLowerCase-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordMinimumNonLetter-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordMinimumNumeric-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordMinimumSymbols-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordMinimumUpperCase-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setPasswordQuality-(Landroid/content/ComponentName; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-setStorageEncryption-(Landroid/content/ComponentName; Z I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DevicePolicyManagerService;-uninstallCaCert-([B)V": ["android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/DevicePolicyManagerService;-wipeData-(I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/DropBoxManagerService;-getNextEntry-(Ljava/lang/String; J)Landroid/os/DropBoxManager$Entry;": ["android.permission.READ_LOGS"], "Lcom/android/server/InputMethodManagerService;-addClient-(Lcom/android/internal/view/IInputMethodClient; Lcom/android/internal/view/IInputContext; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getCurrentInputMethodSubtype-()Landroid/view/inputmethod/InputMethodSubtype;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getEnabledInputMethodList-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getEnabledInputMethodSubtypeList-(Ljava/lang/String; Z)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getInputMethodList-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getLastInputMethodSubtype-()Landroid/view/inputmethod/InputMethodSubtype;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-hideMySoftInput-(Landroid/os/IBinder; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-hideSoftInput-(Lcom/android/internal/view/IInputMethodClient; I Landroid/os/ResultReceiver;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR"], "Lcom/android/server/InputMethodManagerService;-notifySuggestionPicked-(Landroid/text/style/SuggestionSpan; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-registerSuggestionSpansForNotification-([Landroid/text/style/SuggestionSpan;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-removeClient-(Lcom/android/internal/view/IInputMethodClient;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setAdditionalInputMethodSubtypes-(Ljava/lang/String; [Landroid/view/inputmethod/InputMethodSubtype;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setCurrentInputMethodSubtype-(Landroid/view/inputmethod/InputMethodSubtype;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR"], "Lcom/android/server/InputMethodManagerService;-setImeWindowStatus-(Landroid/os/IBinder; I I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/InputMethodManagerService;-setInputMethod-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodAndSubtype-(Landroid/os/IBinder; Ljava/lang/String; Landroid/view/inputmethod/InputMethodSubtype;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodEnabled-(Ljava/lang/String; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-shouldOfferSwitchingToNextInputMethod-(Landroid/os/IBinder;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showInputMethodAndSubtypeEnablerFromClient-(Lcom/android/internal/view/IInputMethodClient; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showInputMethodPickerFromClient-(Lcom/android/internal/view/IInputMethodClient;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showMySoftInput-(Landroid/os/IBinder; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showSoftInput-(Lcom/android/internal/view/IInputMethodClient; I Landroid/os/ResultReceiver;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-startInput-(Lcom/android/internal/view/IInputMethodClient; Lcom/android/internal/view/IInputContext; Landroid/view/inputmethod/EditorInfo; I)Lcom/android/internal/view/InputBindResult;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-switchToLastInputMethod-(Landroid/os/IBinder;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-switchToNextInputMethod-(Landroid/os/IBinder; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-updateStatusIcon-(Landroid/os/IBinder; Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR"], "Lcom/android/server/InputMethodManagerService;-windowGainedFocus-(Lcom/android/internal/view/IInputMethodClient; Landroid/os/IBinder; I I I Landroid/view/inputmethod/EditorInfo; Lcom/android/internal/view/IInputContext;)Lcom/android/internal/view/InputBindResult;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.STATUS_BAR"], "Lcom/android/server/LocationManagerService;-addGpsStatusListener-(Landroid/location/IGpsStatusListener; Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-addTestProvider-(Ljava/lang/String; Lcom/android/internal/location/ProviderProperties;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderEnabled-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderLocation-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-clearTestProviderStatus-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getLastLocation-(Landroid/location/LocationRequest; Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviderProperties-(Ljava/lang/String;)Lcom/android/internal/location/ProviderProperties;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-isProviderEnabled-(Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-removeGeofence-(Landroid/location/Geofence; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-removeTestProvider-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-removeUpdates-(Landroid/location/ILocationListener; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-reportLocation-(Landroid/location/Location; Z)V": ["android.permission.INSTALL_LOCATION_PROVIDER"], "Lcom/android/server/LocationManagerService;-requestGeofence-(Landroid/location/LocationRequest; Landroid/location/Geofence; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-requestLocationUpdates-(Landroid/location/LocationRequest; Landroid/location/ILocationListener; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.UPDATE_APP_OPS_STATS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/LocationManagerService;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Lcom/android/server/LocationManagerService;-setTestProviderEnabled-(Ljava/lang/String; Z)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-setTestProviderLocation-(Ljava/lang/String; Landroid/location/Location;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LocationManagerService;-setTestProviderStatus-(Ljava/lang/String; I Landroid/os/Bundle; J)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Lcom/android/server/LockSettingsService;-checkPassword-(Ljava/lang/String; I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-checkPattern-(Ljava/lang/String; I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-getBoolean-(Ljava/lang/String; Z I)Z": ["android.permission.READ_PROFILE"], "Lcom/android/server/LockSettingsService;-getLong-(Ljava/lang/String; J I)J": ["android.permission.READ_PROFILE"], "Lcom/android/server/LockSettingsService;-getString-(Ljava/lang/String; Ljava/lang/String; I)Ljava/lang/String;": ["android.permission.READ_PROFILE"], "Lcom/android/server/LockSettingsService;-removeUser-(I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setBoolean-(Ljava/lang/String; Z I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLockPassword-(Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLockPattern-(Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLong-(Ljava/lang/String; J I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setString-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/MountService;-changeEncryptionPassword-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-createSecureContainer-(Ljava/lang/String; I Ljava/lang/String; Ljava/lang/String; I Z)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-decryptStorage-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-destroySecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_DESTROY"], "Lcom/android/server/MountService;-encryptStorage-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-finalizeSecureContainer-(Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-fixPermissionsSecureContainer-(Ljava/lang/String; I Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-formatVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-getEncryptionState-()I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-getSecureContainerFilesystemPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerList-()[Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getStorageUsers-(Ljava/lang/String;)[I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-getVolumeList-()[Landroid/os/storage/StorageVolume;": ["android.permission.ACCESS_ALL_EXTERNAL_STORAGE"], "Lcom/android/server/MountService;-isSecureContainerMounted-(Ljava/lang/String;)Z": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-mountSecureContainer-(Ljava/lang/String; Ljava/lang/String; I)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-mountVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-renameSecureContainer-(Ljava/lang/String; Ljava/lang/String;)I": ["android.permission.ASEC_RENAME"], "Lcom/android/server/MountService;-setUsbMassStorageEnabled-(Z)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-shutdown-(Landroid/os/storage/IMountShutdownObserver;)V": ["android.permission.SHUTDOWN"], "Lcom/android/server/MountService;-unmountSecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-unmountVolume-(Ljava/lang/String; Z Z)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-verifyEncryptionPassword-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/NetworkManagementService;-addIdleTimer-(Ljava/lang/String; I Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addSecondaryRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-attachPppd-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearDnsInterfaceForPid-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearDnsInterfaceForUidRange-(Ljava/lang/String; I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearDnsInterfaceMaps-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearHostExemption-(Landroid/net/LinkAddress;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearInterfaceAddresses-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearMarkedForwarding-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearMarkedForwardingRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearUidRangeRoute-(Ljava/lang/String; I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-detachPppd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-flushDefaultDnsCache-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-flushInterfaceDnsCache-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getDnsForwarders-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getInterfaceConfig-(Ljava/lang/String;)Landroid/net/InterfaceConfiguration;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getIpForwardingEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getMarkForProtect-()I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getMarkForUid-(I)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsDetail-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryDev-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryXt-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsTethering-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsUidDetail-(I)Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getRoutes-(Ljava/lang/String;)[Landroid/net/RouteInfo;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isBandwidthControlEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isClatdStarted-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isTetheringStarted-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTetheredInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTtys-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-registerObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeIdleTimer-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceAlert-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceQuota-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeSecondaryRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDefaultInterfaceForDns-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsForwarders-([Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsInterfaceForPid-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsInterfaceForUidRange-(Ljava/lang/String; I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsServersForInterface-(Ljava/lang/String; [Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setGlobalAlert-(J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setHostExemption-(Landroid/net/LinkAddress;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceAlert-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceConfig-(Ljava/lang/String; Landroid/net/InterfaceConfiguration;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceDown-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceIpv6PrivacyExtensions-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceQuota-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceUp-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setIpForwardingEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setMarkedForwarding-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setMarkedForwardingRoute-(Ljava/lang/String; Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setMtu-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setUidNetworkRules-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setUidRangeRoute-(Ljava/lang/String; I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-shutdown-()V": ["android.permission.SHUTDOWN"], "Lcom/android/server/NetworkManagementService;-startAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startClatd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startTethering-([Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopAccessPoint-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopClatd-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopTethering-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-tetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-unregisterObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-untetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-wifiFirmwareReload-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NotificationManagerService;-getActiveNotifications-(Ljava/lang/String;)[Landroid/service/notification/StatusBarNotification;": ["android.permission.ACCESS_NOTIFICATIONS"], "Lcom/android/server/NotificationManagerService;-getHistoricalNotifications-(Ljava/lang/String; I)[Landroid/service/notification/StatusBarNotification;": ["android.permission.ACCESS_NOTIFICATIONS"], "Lcom/android/server/NsdService;-getMessenger-()Landroid/os/Messenger;": ["android.permission.INTERNET"], "Lcom/android/server/NsdService;-setEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/SerialService;-getSerialPorts-()[Ljava/lang/String;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/SerialService;-openSerialPort-(Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/StatusBarManagerService;-collapsePanels-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-disable-(I Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-expandNotificationsPanel-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-expandSettingsPanel-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-onClearAllNotifications-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onNotificationClear-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onNotificationClick-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onNotificationError-(Ljava/lang/String; Ljava/lang/String; I I I Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-onPanelRevealed-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-registerStatusBar-(Lcom/android/internal/statusbar/IStatusBar; Lcom/android/internal/statusbar/StatusBarIconList; Ljava/util/List; Ljava/util/List; [I Ljava/util/List;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-removeIcon-(Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setIcon-(Ljava/lang/String; Ljava/lang/String; I I Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setIconVisibility-(Ljava/lang/String; Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setImeWindowStatus-(Landroid/os/IBinder; I I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/StatusBarManagerService;-setSystemUiVisibility-(I I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/StatusBarManagerService;-topAppWindowChanged-(Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/TelephonyRegistry;-listen-(Ljava/lang/String; Lcom/android/internal/telephony/IPhoneStateListener; I Z)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallForwardingChanged-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallState-(I Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellInfo-(Ljava/util/List;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellLocation-(Landroid/os/Bundle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataActivity-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnection-(I Z Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/net/LinkProperties; Landroid/net/LinkCapabilities; I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionFailed-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyMessageWaitingChanged-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyOtaspChanged-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyServiceState-(Landroid/telephony/ServiceState;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifySignalStrength-(Landroid/telephony/SignalStrength;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellChecker-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellCheckerSubtype-(Ljava/lang/String; I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setSpellCheckerEnabled-(Z)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/UpdateLockService;-acquireUpdateLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/UpdateLockService;-releaseUpdateLock-(Landroid/os/IBinder;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/VibratorService;-cancelVibrate-(Landroid/os/IBinder;)V": ["android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibrate-(I Ljava/lang/String; J Landroid/os/IBinder;)V": ["android.permission.UPDATE_APP_OPS_STATS", "android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibratePattern-(I Ljava/lang/String; [J I Landroid/os/IBinder;)V": ["android.permission.UPDATE_APP_OPS_STATS", "android.permission.VIBRATE"], "Lcom/android/server/WallpaperManagerService;-setDimensionHints-(I I)V": ["android.permission.SET_WALLPAPER_HINTS"], "Lcom/android/server/WallpaperManagerService;-setWallpaper-(Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.SET_WALLPAPER"], "Lcom/android/server/WallpaperManagerService;-setWallpaperComponent-(Landroid/content/ComponentName;)V": ["android.permission.SET_WALLPAPER_COMPONENT"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfoByAccessibilityId-(I J I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; I J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfosByText-(I J Ljava/lang/String; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfosByViewId-(I J Ljava/lang/String; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findFocus-(I J I I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-focusSearch-(I J I I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-performAccessibilityAction-(I J I Landroid/os/Bundle; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-performGlobalAction-(I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-addAccessibilityInteractionConnection-(Landroid/view/IWindow; Landroid/view/accessibility/IAccessibilityInteractionConnection; I)I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-addClient-(Landroid/view/accessibility/IAccessibilityManagerClient; I)I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getEnabledAccessibilityServiceList-(I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getInstalledAccessibilityServiceList-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-interrupt-(I)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-removeAccessibilityInteractionConnection-(Landroid/view/IWindow;)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-sendAccessibilityEvent-(Landroid/view/accessibility/AccessibilityEvent; I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-temporaryEnableAccessibilityStateUntilKeyguardRemoved-(Landroid/content/ComponentName; Z)V": ["temporaryEnableAccessibilityStateUntilKeyguardRemoved"], "Lcom/android/server/accounts/AccountManagerService;-addAccount-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Z Landroid/os/Bundle;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-addAccountExplicitly-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-clearPassword-(Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-confirmCredentialsAsUser-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Landroid/os/Bundle; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-editProperties-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Z)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-getAccounts-(Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsAsUser-(Ljava/lang/String; I)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsByFeatures-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; [Ljava/lang/String;)V": ["android.permission.GET_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsByTypeForPackage-(Ljava/lang/String; Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsForPackage-(Ljava/lang/String; I)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAuthToken-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Ljava/lang/String; Z Z Landroid/os/Bundle;)V": ["android.permission.USE_CREDENTIALS"], "Lcom/android/server/accounts/AccountManagerService;-getPassword-(Landroid/accounts/Account;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-getUserData-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-hasFeatures-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; [Ljava/lang/String;)V": ["android.permission.GET_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-invalidateAuthToken-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MANAGE_ACCOUNTS", "android.permission.USE_CREDENTIALS"], "Lcom/android/server/accounts/AccountManagerService;-peekAuthToken-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-removeAccount-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-setAuthToken-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-setPassword-(Landroid/accounts/Account; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-setUserData-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Lcom/android/server/accounts/AccountManagerService;-updateCredentials-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Ljava/lang/String; Z Landroid/os/Bundle;)V": ["android.permission.MANAGE_ACCOUNTS"], "Lcom/android/server/am/ActivityManagerService;-activityDestroyed-(Landroid/os/IBinder;)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-appNotRespondingViaProvider-(Landroid/os/IBinder;)V": ["android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-bindBackupAgent-(Landroid/content/pm/ApplicationInfo; I)Z": ["android.permission.BACKUP"], "Lcom/android/server/am/ActivityManagerService;-clearPendingBackup-()V": ["android.permission.BACKUP"], "Lcom/android/server/am/ActivityManagerService;-crashApplication-(I I Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-createStack-(I I I F)I": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-dismissKeyguardOnNextActivity-()V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-dumpHeap-(Ljava/lang/String; I Z Ljava/lang/String; Landroid/os/ParcelFileDescriptor;)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-finishHeavyWeightApp-()V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-forceStopPackage-(Ljava/lang/String; I)V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-getAssistContextExtras-(I)Landroid/os/Bundle;": ["android.permission.GET_TOP_ACTIVITY_INFO"], "Lcom/android/server/am/ActivityManagerService;-getContentProviderExternal-(Ljava/lang/String; I Landroid/os/IBinder;)Landroid/app/IActivityManager$ContentProviderHolder;": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY"], "Lcom/android/server/am/ActivityManagerService;-getCurrentUser-()Landroid/content/pm/UserInfo;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-getRecentTasks-(I I I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-getRunningUserIds-()[I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-getStackBoxInfo-(I)Landroid/app/ActivityManager$StackBoxInfo;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getStackBoxes-()Ljava/util/List;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getTaskThumbnails-(I)Landroid/app/ActivityManager$TaskThumbnails;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/am/ActivityManagerService;-getTaskTopThumbnail-(I)Landroid/graphics/Bitmap;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/am/ActivityManagerService;-getTasks-(I I Landroid/app/IThumbnailReceiver;)Ljava/util/List;": ["android.permission.GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-goingToSleep-()V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/am/ActivityManagerService;-hang-(Landroid/os/IBinder; Z)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-inputDispatchingTimedOut-(I Z Ljava/lang/String;)J": ["android.permission.FILTER_EVENTS"], "Lcom/android/server/am/ActivityManagerService;-isUserRunning-(I Z)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-killAllBackgroundProcesses-()V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-killBackgroundProcesses-(Ljava/lang/String; I)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-moveActivityTaskToBack-(Landroid/os/IBinder; Z)Z": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-moveTaskBackwards-(I)V": ["android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToBack-(I)V": ["android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToStack-(I I Z)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-navigateUpTo-(Landroid/os/IBinder; Landroid/content/Intent; I Landroid/content/Intent;)Z": ["android.permission.MANAGE_ACTIVITY_STACKS", "android.permission.START_ANY_ACTIVITY"], "Lcom/android/server/am/ActivityManagerService;-performIdleMaintenance-()V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-profileControl-(Ljava/lang/String; I Z Ljava/lang/String; Landroid/os/ParcelFileDescriptor; I)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-registerProcessObserver-(Landroid/app/IProcessObserver;)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-registerUserSwitchObserver-(Landroid/app/IUserSwitchObserver;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-removeContentProviderExternal-(Ljava/lang/String; Landroid/os/IBinder;)V": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY"], "Lcom/android/server/am/ActivityManagerService;-removeSubTask-(I I)Z": ["android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-removeTask-(I I)Z": ["android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-requestBugReport-()V": ["android.permission.DUMP"], "Lcom/android/server/am/ActivityManagerService;-resizeStackBox-(I F)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-restart-()V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-resumeAppSwitches-()V": ["android.permission.STOP_APP_SWITCHES"], "Lcom/android/server/am/ActivityManagerService;-setActivityController-(Landroid/app/IActivityController;)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-setAlwaysFinish-(Z)V": ["android.permission.SET_ALWAYS_FINISH"], "Lcom/android/server/am/ActivityManagerService;-setDebugApp-(Ljava/lang/String; Z Z)V": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-setFrontActivityScreenCompatMode-(I)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setLockScreenShown-(Z)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/am/ActivityManagerService;-setPackageAskScreenCompat-(Ljava/lang/String; Z)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setPackageScreenCompatMode-(Ljava/lang/String; I)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setProcessForeground-(Landroid/os/IBinder; I Z)V": ["android.permission.SET_PROCESS_LIMIT"], "Lcom/android/server/am/ActivityManagerService;-setProcessLimit-(I)V": ["android.permission.SET_PROCESS_LIMIT"], "Lcom/android/server/am/ActivityManagerService;-shutdown-(I)Z": ["android.permission.SHUTDOWN"], "Lcom/android/server/am/ActivityManagerService;-signalPersistentProcesses-(I)V": ["android.permission.SIGNAL_PERSISTENT_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-startActivities-(Landroid/app/IApplicationThread; Ljava/lang/String; [Landroid/content/Intent; [Ljava/lang/String; Landroid/os/IBinder; Landroid/os/Bundle; I)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivity-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Ljava/lang/String; Landroid/os/ParcelFileDescriptor; Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityAndWait-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Ljava/lang/String; Landroid/os/ParcelFileDescriptor; Landroid/os/Bundle; I)Landroid/app/IActivityManager$WaitResult;": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityAsUser-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Ljava/lang/String; Landroid/os/ParcelFileDescriptor; Landroid/os/Bundle; I)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityWithConfig-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/content/res/Configuration; Landroid/os/Bundle; I)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-stopAppSwitches-()V": ["android.permission.STOP_APP_SWITCHES"], "Lcom/android/server/am/ActivityManagerService;-stopUser-(I Landroid/app/IStopUserCallback;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-switchUser-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-unbroadcastIntent-(Landroid/app/IApplicationThread; Landroid/content/Intent; I)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-unhandledBack-()V": ["android.permission.FORCE_BACK"], "Lcom/android/server/am/ActivityManagerService;-updateConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.CHANGE_CONFIGURATION"], "Lcom/android/server/am/ActivityManagerService;-updatePersistentConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.CHANGE_CONFIGURATION"], "Lcom/android/server/am/ActivityManagerService;-wakingUp-()V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimeBattery-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimePlugged-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getStatistics-()[B": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquired-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquiredFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleased-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleasedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteInputEvent-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteNetworkInterfaceType-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteNetworkStatsEnabled-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneDataConnectionState-(I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneSignalStrength-(Landroid/telephony/SignalStrength;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneState-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenBrightness-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelock-(I I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelock-(I I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteUserActivity-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteVibratorOff-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteVibratorOn-(I J)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiBatchedScanStartedFromSource-(Landroid/os/WorkSource; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiBatchedScanStoppedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunning-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunningChanged-(Landroid/os/WorkSource; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStarted-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStartedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStopped-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStoppedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiStopped-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-setBatteryState-(I I I I I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/ProcessStatsService;-getCurrentStats-(Ljava/util/List;)[B": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/ProcessStatsService;-getStatsOverTime-(J)Landroid/os/ParcelFileDescriptor;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/UsageStatsService;-getAllPkgUsageStats-()[Lcom/android/internal/os/PkgUsageStats;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/UsageStatsService;-getPkgUsageStats-(Landroid/content/ComponentName;)Lcom/android/internal/os/PkgUsageStats;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/UsageStatsService;-noteLaunchTime-(Landroid/content/ComponentName; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/UsageStatsService;-notePauseComponent-(Landroid/content/ComponentName;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/UsageStatsService;-noteResumeComponent-(Landroid/content/ComponentName;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/content/ContentService;-addPeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle; J)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getCurrentSyncs-()Ljava/util/List;": ["android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-getIsSyncable-(Landroid/accounts/Account; Ljava/lang/String;)I": ["android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getMasterSyncAutomatically-()Z": ["android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getPeriodicSyncs-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/util/List;": ["android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncStatus-(Landroid/accounts/Account; Ljava/lang/String;)Landroid/content/SyncStatusInfo;": ["android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncActive-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncPending-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-registerContentObserver-(Landroid/net/Uri; Z Landroid/database/IContentObserver; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-removePeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setIsSyncable-(Landroid/accounts/Account; Ljava/lang/String; I)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setMasterSyncAutomatically-(Z)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String; Z)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/display/DisplayManagerService;-connectWifiDisplay-(Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService;-createVirtualDisplay-(Landroid/os/IBinder; Ljava/lang/String; Ljava/lang/String; I I I Landroid/view/Surface; I)I": ["android.permission.CAPTURE_SECURE_VIDEO_OUTPUT", "android.permission.CAPTURE_VIDEO_OUTPUT"], "Lcom/android/server/display/DisplayManagerService;-forgetWifiDisplay-(Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService;-pauseWifiDisplay-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService;-renameWifiDisplay-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService;-resumeWifiDisplay-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService;-startWifiDisplayScan-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService;-stopWifiDisplayScan-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/dreams/DreamManagerService;-awaken-()V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService;-dream-()V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService;-getDefaultDreamComponent-()Landroid/content/ComponentName;": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService;-getDreamComponents-()[Landroid/content/ComponentName;": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService;-isDreaming-()Z": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService;-setDreamComponents-([Landroid/content/ComponentName;)V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService;-testDream-(Landroid/content/ComponentName;)V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/input/InputManagerService;-addKeyboardLayoutForInputDevice-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-removeKeyboardLayoutForInputDevice-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-setCurrentKeyboardLayoutForInputDevice-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-tryPointerSpeed-(I)V": ["android.permission.SET_POINTER_SPEED"], "Lcom/android/server/media/MediaRouterService;-registerClientAsUser-(Landroid/media/IMediaRouterClient; Ljava/lang/String; I)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkPolicies-()[Landroid/net/NetworkPolicy;": ["android.permission.MANAGE_NETWORK_POLICY", "android.permission.READ_PHONE_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkQuotaInfo-(Landroid/net/NetworkState;)Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getRestrictBackground-()Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getUidPolicy-(I)I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getUidsWithPolicy-(I)[I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-isUidForeground-(I)Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-registerListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkPolicyManagerService;-setNetworkPolicies-([Landroid/net/NetworkPolicy;)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setRestrictBackground-(Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setUidPolicy-(I I)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-snoozeLimit-(Landroid/net/NetworkTemplate;)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-unregisterListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkStatsService;-advisePersistThreshold-(J)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-forceUpdate-()V": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-getDataLayerSnapshotForUid-(I)Landroid/net/NetworkStats;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkStatsService;-getNetworkTotalBytes-(Landroid/net/NetworkTemplate; J J)J": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-incrementOperationCount-(I I I)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-openSession-()Landroid/net/INetworkStatsSession;": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-setUidForeground-(I Z)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/pm/PackageManagerService;-addPreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-clearApplicationUserData-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver; I)V": ["android.permission.CLEAR_APP_USER_DATA", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-clearPackagePreferredActivities-(Ljava/lang/String;)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-deleteApplicationCacheFiles-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.DELETE_CACHE_FILES"], "Lcom/android/server/pm/PackageManagerService;-deletePackageAsUser-(Ljava/lang/String; Landroid/content/pm/IPackageDeleteObserver; I I)V": ["android.permission.DELETE_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-extendVerificationTimeout-(I I J)V": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/pm/PackageManagerService;-freeStorage-(J Landroid/content/IntentSender;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-freeStorageAndNotify-(J Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-getActivityInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ActivityInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getApplicationBlockedSettingAsUser-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/pm/PackageManagerService;-getApplicationEnabledSetting-(Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getApplicationInfo-(Ljava/lang/String; I I)Landroid/content/pm/ApplicationInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getComponentEnabledSetting-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getHomeActivities-(Ljava/util/List;)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getInstalledPackages-(I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getLastChosenActivity-(Landroid/content/Intent; Ljava/lang/String; I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getPackageInfo-(Ljava/lang/String; I I)Landroid/content/pm/PackageInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getPackageSizeInfo-(Ljava/lang/String; I Landroid/content/pm/IPackageStatsObserver;)V": ["android.permission.GET_PACKAGE_SIZE"], "Lcom/android/server/pm/PackageManagerService;-getPackageUid-(Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getProviderInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ProviderInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getReceiverInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ActivityInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getServiceInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ServiceInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getVerifierDeviceIdentity-()Landroid/content/pm/VerifierDeviceIdentity;": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/pm/PackageManagerService;-grantPermission-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-installExistingPackageAsUser-(Ljava/lang/String; I)I": ["android.permission.INSTALL_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-installPackage-(Landroid/net/Uri; Landroid/content/pm/IPackageInstallObserver; I Ljava/lang/String;)V": ["android.permission.INSTALL_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-installPackageWithVerification-(Landroid/net/Uri; Landroid/content/pm/IPackageInstallObserver; I Ljava/lang/String; Landroid/net/Uri; Landroid/content/pm/ManifestDigest; Landroid/content/pm/ContainerEncryptionParams;)V": ["android.permission.INSTALL_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-installPackageWithVerificationAndEncryption-(Landroid/net/Uri; Landroid/content/pm/IPackageInstallObserver; I Ljava/lang/String; Landroid/content/pm/VerificationParams; Landroid/content/pm/ContainerEncryptionParams;)V": ["android.permission.INSTALL_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-isPackageAvailable-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-movePackage-(Ljava/lang/String; Landroid/content/pm/IPackageMoveObserver; I)V": ["android.permission.MOVE_PACKAGE"], "Lcom/android/server/pm/PackageManagerService;-queryIntentActivities-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentActivityOptions-(Landroid/content/ComponentName; [Landroid/content/Intent; [Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentContentProviders-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentReceivers-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentServices-(Landroid/content/Intent; Ljava/lang/String; I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-replacePreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-resetPreferredActivities-(I)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-resolveIntent-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-resolveService-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-revokePermission-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-setApplicationBlockedSettingAsUser-(Ljava/lang/String; Z I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/pm/PackageManagerService;-setApplicationEnabledSetting-(Ljava/lang/String; I I I Ljava/lang/String;)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setComponentEnabledSetting-(Landroid/content/ComponentName; I I I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setInstallLocation-(I)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/pm/PackageManagerService;-setLastChosenActivity-(Landroid/content/Intent; Ljava/lang/String; I Landroid/content/IntentFilter; I Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-setPackageStoppedState-(Ljava/lang/String; Z I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setPermissionEnforced-(Ljava/lang/String; Z)V": ["android.permission.GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-verifyPendingInstall-(I I)V": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/power/PowerManagerService;-acquireWakeLock-(Landroid/os/IBinder; I Ljava/lang/String; Ljava/lang/String; Landroid/os/WorkSource;)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService;-acquireWakeLockWithUid-(Landroid/os/IBinder; I Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService;-crash-(Ljava/lang/String;)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService;-goToSleep-(J I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService;-nap-(J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService;-reboot-(Z Ljava/lang/String; Z)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService;-releaseWakeLock-(Landroid/os/IBinder; I)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService;-setAttentionLight-(Z I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService;-setStayOnSetting-(I)V": ["android.permission.WRITE_SETTINGS"], "Lcom/android/server/power/PowerManagerService;-setTemporaryScreenAutoBrightnessAdjustmentSettingOverride-(F)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService;-setTemporaryScreenBrightnessSettingOverride-(I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService;-shutdown-(Z Z)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService;-updateWakeLockUids-(Landroid/os/IBinder; [I)V": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService;-updateWakeLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService;-userActivity-(J I I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService;-wakeUp-(J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/print/PrintManagerService;-addPrintJobStateChangeListener-(Landroid/print/IPrintJobStateChangeListener; I I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL", "com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService;-cancelPrintJob-(Landroid/print/PrintJobId; I I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL", "com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService;-createPrinterDiscoverySession-(Landroid/print/IPrinterDiscoveryObserver; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-destroyPrinterDiscoverySession-(Landroid/print/IPrinterDiscoveryObserver; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-getEnabledPrintServices-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-getInstalledPrintServices-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-getPrintJobInfo-(Landroid/print/PrintJobId; I I)Landroid/print/PrintJobInfo;": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL", "com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService;-getPrintJobInfos-(I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL", "com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService;-print-(Ljava/lang/String; Landroid/print/IPrintDocumentAdapter; Landroid/print/PrintAttributes; Ljava/lang/String; I I)Landroid/os/Bundle;": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL", "com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService;-removePrintJobStateChangeListener-(Landroid/print/IPrintJobStateChangeListener; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-restartPrintJob-(Landroid/print/PrintJobId; I I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL", "com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService;-startPrinterDiscovery-(Landroid/print/IPrinterDiscoveryObserver; Ljava/util/List; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-startPrinterStateTracking-(Landroid/print/PrinterId; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-stopPrinterDiscovery-(Landroid/print/IPrinterDiscoveryObserver; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-stopPrinterStateTracking-(Landroid/print/PrinterId; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/print/PrintManagerService;-validatePrinters-(Ljava/util/List; I)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/sip/SipService;-close-(Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-createSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/ISipSessionListener;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getListOfProfiles-()[Landroid/net/sip/SipProfile;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getPendingSession-(Ljava/lang/String;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isOpened-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isRegistered-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open-(Landroid/net/sip/SipProfile;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open3-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/ISipSessionListener;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/ISipSessionListener;)V": ["android.permission.USE_SIP"], "Lcom/android/server/usb/UsbService;-allowUsbDebugging-(Z Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-clearDefaults-(Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-clearUsbDebuggingKeys-()V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-denyUsbDebugging-()V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantAccessoryPermission-(Landroid/hardware/usb/UsbAccessory; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantDevicePermission-(Landroid/hardware/usb/UsbDevice; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-hasDefaults-(Ljava/lang/String; I)Z": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setAccessoryPackage-(Landroid/hardware/usb/UsbAccessory; Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setCurrentFunction-(Ljava/lang/String; Z)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setDevicePackage-(Landroid/hardware/usb/UsbDevice; Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setMassStorageBackingFile-(Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/wifi/WifiService;-acquireMulticastLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiService;-acquireWifiLock-(Landroid/os/IBinder; I Ljava/lang/String; Landroid/os/WorkSource;)Z": ["android.permission.WAKE_LOCK"], "Lcom/android/server/wifi/WifiService;-addOrUpdateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-addToBlacklist-(Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-captivePortalCheckComplete-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiService;-clearBlacklist-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-disconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getBatchedScanResults-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getConfigFile-()Ljava/lang/String;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getFrequencyBand-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getScanResults-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getWifiApConfiguration-()Landroid/net/wifi/WifiConfiguration;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getWifiApEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getWifiEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getWifiServiceMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-getWifiStateMachineMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-initializeMulticastFiltering-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiService;-isMulticastEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-isScanAlwaysAvailable-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-pollBatchedScan-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-reassociate-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-reconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-releaseMulticastLock-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiService;-releaseWifiLock-(Landroid/os/IBinder;)Z": ["android.permission.WAKE_LOCK"], "Lcom/android/server/wifi/WifiService;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-requestBatchedScan-(Landroid/net/wifi/BatchedScanSettings; Landroid/os/IBinder; Landroid/os/WorkSource;)Z": ["android.permission.CHANGE_WIFI_STATE", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wifi/WifiService;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-setCountryCode-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiService;-setFrequencyBand-(I Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-setWifiApConfiguration-(Landroid/net/wifi/WifiConfiguration;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-setWifiApEnabled-(Landroid/net/wifi/WifiConfiguration; Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-setWifiEnabled-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-startScan-(Landroid/os/WorkSource;)V": ["android.permission.CHANGE_WIFI_STATE", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wifi/WifiService;-startWifi-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiService;-stopBatchedScan-(Landroid/net/wifi/BatchedScanSettings;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiService;-stopWifi-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiService;-updateWifiLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wm/WindowManagerService;-addAppToken-(I Landroid/view/IApplicationToken; I I I Z Z I I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-addWindowToken-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-clearForcedDisplayDensity-(I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-clearForcedDisplaySize-(I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-disableKeyguard-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-dismissKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-executeAppTransition-()V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-exitKeyguardSecurely-(Landroid/view/IOnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-freezeRotation-(I)V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-getCompatibleMagnificationSpecForWindow-(Landroid/os/IBinder;)Landroid/view/MagnificationSpec;": ["android.permission.MAGNIFY_DISPLAY"], "Lcom/android/server/wm/WindowManagerService;-getFocusedWindowToken-()Landroid/os/IBinder;": ["android.permission.RETRIEVE_WINDOW_INFO"], "Lcom/android/server/wm/WindowManagerService;-getWindowFrame-(Landroid/os/IBinder; Landroid/graphics/Rect;)V": ["android.permission.RETRIEVE_WINDOW_INFO"], "Lcom/android/server/wm/WindowManagerService;-isViewServerRunning-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-pauseKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-prepareAppTransition-(I Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-reenableKeyguard-(Landroid/os/IBinder;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-removeAppToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-removeWindowToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-resumeKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-screenshotApplications-(Landroid/os/IBinder; I I I Z)Landroid/graphics/Bitmap;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScale-(I F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScales-([F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAppGroupId-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppOrientation-(Landroid/view/IApplicationToken; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppStartingWindow-(Landroid/os/IBinder; Ljava/lang/String; I Landroid/content/res/CompatibilityInfo; Ljava/lang/CharSequence; I I I I Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppVisibility-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppWillBeHidden-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setEventDispatching-(Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setFocusedApp-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setForcedDisplayDensity-(I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setForcedDisplaySize-(I I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setInputFilter-(Landroid/view/IInputFilter;)V": ["android.permission.FILTER_EVENTS"], "Lcom/android/server/wm/WindowManagerService;-setMagnificationCallbacks-(Landroid/view/IMagnificationCallbacks;)V": ["android.permission.MAGNIFY_DISPLAY"], "Lcom/android/server/wm/WindowManagerService;-setMagnificationSpec-(Landroid/view/MagnificationSpec;)V": ["android.permission.MAGNIFY_DISPLAY"], "Lcom/android/server/wm/WindowManagerService;-setNewConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setOverscan-(I I I I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-startAppFreezingScreen-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-startFreezingScreen-(I I)V": ["android.permission.FREEZE_SCREEN"], "Lcom/android/server/wm/WindowManagerService;-startViewServer-(I)Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-statusBarVisibilityChanged-(I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/wm/WindowManagerService;-stopAppFreezingScreen-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-stopFreezingScreen-()V": ["android.permission.FREEZE_SCREEN"], "Lcom/android/server/wm/WindowManagerService;-stopViewServer-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-thawRotation-()V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-updateOrientationFromAppTokens-(Landroid/content/res/Configuration; Landroid/os/IBinder;)Landroid/content/res/Configuration;": ["android.permission.MANAGE_APP_TOKENS"], "Landroid/accounts/AccountAuthenticatorActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountManager;-addAccountExplicitly-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-addOnAccountsUpdatedListener-(Landroid/accounts/OnAccountsUpdateListener; Landroid/os/Handler; Z)V": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-clearPassword-(Landroid/accounts/Account;)V": ["android.permission.MANAGE_ACCOUNTS"], "Landroid/accounts/AccountManager;-getAccounts-()[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-getAccountsByType-(Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.GET_ACCOUNTS"], "Landroid/accounts/AccountManager;-getPassword-(Landroid/accounts/Account;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-getUserData-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-invalidateAuthToken-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MANAGE_ACCOUNTS", "android.permission.USE_CREDENTIALS"], "Landroid/accounts/AccountManager;-peekAuthToken-(Landroid/accounts/Account; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setAuthToken-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setPassword-(Landroid/accounts/Account; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/accounts/AccountManager;-setUserData-(Landroid/accounts/Account; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.AUTHENTICATE_ACCOUNTS"], "Landroid/app/Activity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-moveTaskToBack-(Z)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityManager;-getRecentTasks-(I I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-getRunningTasks-(I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-killBackgroundProcesses-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I)V": ["android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-restartPackage-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/AlarmManager;-setTimeZone-(Ljava/lang/String;)V": ["android.permission.SET_TIME_ZONE"], "Landroid/app/AliasActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/KeyguardManager$KeyguardLock;-disableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager$KeyguardLock;-reenableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager;-exitKeyguardSecurely-(Landroid/app/KeyguardManager$OnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/ListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-clear-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setBitmap-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setResource-(I)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setStream-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-suggestDesiredDimensions-(I I)V": ["android.permission.SET_WALLPAPER_HINTS"], "Landroid/app/backup/BackupAgentHelper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/bluetooth/BluetoothA2dp;-finalize-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-closeProfileProxy-(I Landroid/bluetooth/BluetoothProfile;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-disable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-enable-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBondedDevices-()Ljava/util/Set;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getProfileProxy-(Landroid/content/Context; Landroid/bluetooth/BluetoothProfile$ServiceListener; I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingInsecureRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-startDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-startLeScan-([Ljava/util/UUID; Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-startLeScan-(Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-stopLeScan-(Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-connectGatt-(Landroid/content/Context; Z Landroid/bluetooth/BluetoothGattCallback;)Landroid/bluetooth/BluetoothGatt;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-createBond-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-createInsecureRfcommSocketToServiceRecord-(Ljava/util/UUID;)Landroid/bluetooth/BluetoothSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-createRfcommSocketToServiceRecord-(Ljava/util/UUID;)Landroid/bluetooth/BluetoothSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-fetchUuidsWithSdp-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBluetoothClass-()Landroid/bluetooth/BluetoothClass;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBondState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getType-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-setPairingConfirmation-(Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-setPin-([B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothGatt;-abortReliableWrite-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-abortReliableWrite-(Landroid/bluetooth/BluetoothDevice;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-beginReliableWrite-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-close-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-connect-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-disconnect-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-discoverServices-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-executeReliableWrite-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readCharacteristic-(Landroid/bluetooth/BluetoothGattCharacteristic;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readDescriptor-(Landroid/bluetooth/BluetoothGattDescriptor;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readRemoteRssi-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-setCharacteristicNotification-(Landroid/bluetooth/BluetoothGattCharacteristic; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-writeCharacteristic-(Landroid/bluetooth/BluetoothGattCharacteristic;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-writeDescriptor-(Landroid/bluetooth/BluetoothGattDescriptor;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-addService-(Landroid/bluetooth/BluetoothGattService;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-cancelConnection-(Landroid/bluetooth/BluetoothDevice;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-clearServices-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-close-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-connect-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-notifyCharacteristicChanged-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothGattCharacteristic; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-removeService-(Landroid/bluetooth/BluetoothGattService;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-sendResponse-(Landroid/bluetooth/BluetoothDevice; I I I [B)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-isAudioConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-sendVendorSpecificResultCode-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHeadset;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHeadset;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHealth;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-registerSinkAppConfiguration-(Ljava/lang/String; I Landroid/bluetooth/BluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getConnectedDevices-(I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getConnectionState-(Landroid/bluetooth/BluetoothDevice; I)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getDevicesMatchingConnectionStates-(I [I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-openGattServer-(Landroid/content/Context; Landroid/bluetooth/BluetoothGattServerCallback;)Landroid/bluetooth/BluetoothGattServer;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothSocket;-connect-()V": ["android.permission.BLUETOOTH"], "Landroid/content/ContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/hardware/ConsumerIrManager;-getCarrierFrequencies-()[Landroid/hardware/ConsumerIrManager$CarrierFrequencyRange;": ["android.permission.TRANSMIT_IR"], "Landroid/hardware/ConsumerIrManager;-transmit-(I [I)V": ["android.permission.TRANSMIT_IR"], "Landroid/inputmethodservice/InputMethodService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/location/LocationManager;-addGpsStatusListener-(Landroid/location/GpsStatus$Listener;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addNmeaListener-(Landroid/location/GpsStatus$NmeaListener;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addProximityAlert-(D D F J Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addTestProvider-(Ljava/lang/String; Z Z Z Z Z Z Z I I)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderEnabled-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderLocation-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-clearTestProviderStatus-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getLastKnownLocation-(Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProvider-(Ljava/lang/String;)Landroid/location/LocationProvider;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-isProviderEnabled-(Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeProximityAlert-(Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeTestProvider-(Ljava/lang/String;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-removeUpdates-(Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeUpdates-(Landroid/location/LocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Landroid/location/LocationManager;-setTestProviderEnabled-(Ljava/lang/String; Z)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-setTestProviderLocation-(Ljava/lang/String; Landroid/location/Location;)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/location/LocationManager;-setTestProviderStatus-(Ljava/lang/String; I Landroid/os/Bundle; J)V": ["android.permission.ACCESS_MOCK_LOCATION"], "Landroid/media/AsyncPlayer;-play-(Landroid/content/Context; Landroid/net/Uri; Z I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/AsyncPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/AudioManager;-adjustStreamVolume-(I I I)V": ["android.permission.BLUETOOTH"], "Landroid/media/AudioManager;-adjustSuggestedStreamVolume-(I I I)V": ["android.permission.BLUETOOTH"], "Landroid/media/AudioManager;-adjustVolume-(I I)V": ["android.permission.BLUETOOTH"], "Landroid/media/AudioManager;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setMode-(I)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setStreamVolume-(I I I)V": ["android.permission.BLUETOOTH"], "Landroid/media/AudioManager;-startBluetoothSco-()V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-stopBluetoothSco-()V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/MediaPlayer;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-reset-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-setWakeMode-(Landroid/content/Context; I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaRouter$RouteGroup;-requestSetVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/MediaRouter$RouteGroup;-requestUpdateVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/MediaRouter$RouteInfo;-requestSetVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/MediaRouter$RouteInfo;-requestUpdateVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/Ringtone;-play-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-setStreamType-(I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(Landroid/content/Context; Landroid/net/Uri;)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(I)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-stopPreviousRingtone-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/ConnectivityManager;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkPreference-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-requestRouteToHost-(I I)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-setNetworkPreference-(I)V": ["android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-startUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-stopUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/VpnService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/sip/SipAudioCall;-close-()V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-endCall-()V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-setSpeakerMode-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/net/sip/SipAudioCall;-startAudio-()V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/sip/SipManager;-close-(Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-createSipSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipSession$Listener;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-getSessionFor-(Landroid/content/Intent;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isOpened-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isRegistered-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipProfile; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Ljava/lang/String; Ljava/lang/String; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-register-(Landroid/net/sip/SipProfile; I Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-takeAudioCall-(Landroid/content/Intent; Landroid/net/sip/SipAudioCall$Listener;)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-unregister-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/wifi/WifiManager$MulticastLock;-acquire-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$MulticastLock;-release-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$WifiLock;-acquire-()V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager$WifiLock;-release-()V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager;-addNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disconnect-()Z": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getScanResults-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getWifiState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isScanAlwaysAvailable-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isWifiEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reassociate-()Z": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reconnect-()Z": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-setWifiEnabled-(Z)Z": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-startScan-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-updateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/p2p/WifiP2pManager;-initialize-(Landroid/content/Context; Landroid/os/Looper; Landroid/net/wifi/p2p/WifiP2pManager$ChannelListener;)Landroid/net/wifi/p2p/WifiP2pManager$Channel;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/os/PowerManager$WakeLock;-acquire-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-acquire-(J)V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-setWorkSource-(Landroid/os/WorkSource;)V": ["android.permission.WAKE_LOCK"], "Landroid/os/SystemVibrator;-cancel-()V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-([J I)V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(I Ljava/lang/String; [J I)V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(I Ljava/lang/String; J)V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(J)V": ["android.permission.VIBRATE"], "Landroid/service/dreams/DreamService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/service/dreams/DreamService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/dreams/DreamService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/telephony/TelephonyManager;-getAllCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getCellLocation-()Landroid/telephony/CellLocation;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getDeviceId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getDeviceSoftwareVersion-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getGroupIdLevel1-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getLine1Number-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getNeighboringCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getSimSerialNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getSubscriberId-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailAlphaTag-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailNumber-()Ljava/lang/String;": ["android.permission.READ_PHONE_STATE"], "Landroid/telephony/TelephonyManager;-listen-(Landroid/telephony/PhoneStateListener; I)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE"], "Landroid/test/IsolatedContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/widget/VideoView;-getAudioSessionId-()I": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-onKeyDown-(I Landroid/view/KeyEvent;)Z": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-resume-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoPath-(Ljava/lang/String;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoURI-(Landroid/net/Uri;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-stopPlayback-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-suspend-()V": ["android.permission.WAKE_LOCK"]}
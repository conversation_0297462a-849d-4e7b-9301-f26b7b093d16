{"groups": {"android.permission-group.ACTIVITY_RECOGNITION": {"description": "access your physical activity", "description_ptr": "permgroupdesc_activityRecognition", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M13.49 5.48c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2zm-3.6 13.9l1-4.4 2.1 2v6h2v-7.5l-2.1-2 .6-3c1.3 1.5 3.3 2.5 5.5 2.5v-2c-1.9 0-3.5-1-4.3-2.4l-1-1.6c-.4-.6-1-1-1.7-1-.3 0-.5 .1 -.8 .1 l-5.2 2.2v4.7h2v-3.4l1.8-.7-1.6 8.1-4.9-1-.4 2 7 1.4z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_activity_recognition", "label": "Physical activity", "label_ptr": "permgrouplab_activityRecognition", "name": "android.permission-group.ACTIVITY_RECOGNITION"}, "android.permission-group.CALENDAR": {"description": "access your calendar", "description_ptr": "permgroupdesc_calendar", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M19 4h-1V2h-2v2H8V2H6v2H5c-1.11 0-1.99 0.9 -1.99 2L3 20c0 1.1 0.89 2 2 2h14c1.1 0 2-0.9 2-2V6c0-1.1-0.9-2-2-2zm0 16H5V10h14v10zm-4.5-7c-1.38 0-2.5 1.12-2.5 2.5s1.12 2.5 2.5 2.5 2.5-1.12 2.5-2.5-1.12-2.5-2.5-2.5z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_calendar", "label": "Calendar", "label_ptr": "permgrouplab_calendar", "name": "android.permission-group.CALENDAR"}, "android.permission-group.CALL_LOG": {"description": "read and write phone call log", "description_ptr": "permgroupdesc_calllog", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24.0\" height=\"24.0\" viewBox=\"0 0 24.0 24.0\"><path d=\"M16.01,14.48l-2.62,2.62c-2.75,-1.49 -5.01,-3.75 -6.5,-6.5l2.62,-2.62c0.24,-0.24 0.34,-0.58 0.27,-0.9L9.13,3.82c-0.09,-0.47 -0.5,-0.8 -0.98,-0.8L4,3.01c-0.56,0 -1.03,0.47 -1,1.03c0.17,2.91 1.04,5.63 2.43,8.01c1.57,2.69 3.81,4.93 6.5,6.5c2.38,1.39 5.1,2.26 8.01,2.43c0.56,0.03 1.03,-0.44 1.03,-1v-4.15c0,-0.48 -0.34,-0.89 -0.8,-0.98l-3.26,-0.65C16.58,14.14 16.24,14.24 16.01,14.48z\" fill=\"#000000\"/><path d=\"M12,8h10V6H12V8zM12,4h10V2H12V4zM22,10H12v2h10V10z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_call_log", "label": "Call logs", "label_ptr": "permgrouplab_calllog", "name": "android.permission-group.CALL_LOG"}, "android.permission-group.CAMERA": {"description": "take pictures and record video", "description_ptr": "permgroupdesc_camera", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M18,10.48L18,6c0,-1.1 -0.9,-2 -2,-2L4,4c-1.1,0 -2,0.9 -2,2v12c0,1.1 0.9,2 2,2h12c1.1,0 2,-0.9 2,-2v-4.48l4,3.98v-11l-4,3.98zM16,9.69L16,18L4,18L4,6h12v3.69z\" fill=\"@android:color/white\"/></svg>", "icon_ptr": "perm_group_camera", "label": "Camera", "label_ptr": "permgrouplab_camera", "name": "android.permission-group.CAMERA"}, "android.permission-group.CONTACTS": {"description": "access your contacts", "description_ptr": "permgroupdesc_contacts", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M4,1h16v2h-16z\" fill=\"#000000\"/><path d=\"M4,21h16v2h-16z\" fill=\"#000000\"/><path d=\"M20,5H4C2.9,5 2,5.9 2,7v10c0,1.1 0.9,2 2,2h2h12h2c1.1,0 2,-0.9 2,-2V7C22,5.9 21.1,5 20,5zM8.21,17c0.7,-0.47 2.46,-1 3.79,-1s3.09,0.53 3.79,1H8.21zM20,17h-2c0,-1.99 -4,-3 -6,-3s-6,1.01 -6,3H4V7h16V17z\" fill=\"#000000\"/><path d=\"M12,13.5c1.66,0 3,-1.34 3,-3c0,-1.66 -1.34,-3 -3,-3s-3,1.34 -3,3C9,12.16 10.34,13.5 12,13.5zM12,9.5c0.55,0 1,0.45 1,1s-0.45,1 -1,1s-1,-0.45 -1,-1S11.45,9.5 12,9.5z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_contacts", "label": "Contacts", "label_ptr": "permgrouplab_contacts", "name": "android.permission-group.CONTACTS"}, "android.permission-group.LOCATION": {"description": "access this device's location", "description_ptr": "permgroupdesc_location", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M12,2C8.13,2 5,5.13 5,9c0,5.25 7,13 7,13s7,-7.75 7,-13C19,5.13 15.87,2 12,2zM7,9c0,-2.76 2.24,-5 5,-5s5,2.24 5,5c0,2.88 -2.88,7.19 -5,9.88C9.92,16.21 7,11.85 7,9z\" fill=\"#000000\"/><path d=\"M12,9m-2.5,0a2.5,2.5 0,1 1,5 0a2.5,2.5 0,1 1,-5 0\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_location", "label": "Location", "label_ptr": "permgrouplab_location", "name": "android.permission-group.LOCATION"}, "android.permission-group.MICROPHONE": {"description": "record audio", "description_ptr": "permgroupdesc_microphone", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M12,14c1.66,0 3,-1.34 3,-3V5c0,-1.66 -1.34,-3 -3,-3S9,3.34 9,5v6C9,12.66 10.34,14 12,14zM11,5c0,-0.55 0.45,-1 1,-1s1,0.45 1,1v6c0,0.55 -0.45,1 -1,1s-1,-0.45 -1,-1V5z\" fill=\"#000000\"/><path d=\"M17,11c0,2.76 -2.24,5 -5,5s-5,-2.24 -5,-5H5c0,3.53 2.61,6.43 6,6.92V21h2v-3.08c3.39,-0.49 6,-3.39 6,-6.92H17z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_microphone", "label": "Microphone", "label_ptr": "permgrouplab_microphone", "name": "android.permission-group.MICROPHONE"}, "android.permission-group.NEARBY_DEVICES": {"description": "discover and connect to nearby devices", "description_ptr": "permgroupdesc_nearby_devices", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M12,16.427L7.574,12 12,7.574 16.426,12zM10.58,2.59l-8,8c-0.78,0.78 -0.78,2.05 0,2.83l8,8c0.78,0.78 2.05,0.78 2.83,0l8,-8c0.78,-0.78 0.78,-2.05 0,-2.83l-8,-8c-0.78,-0.79 -2.04,-0.79 -2.83,0zM13.39,17.81L12,19.2l-1.39,-1.39 -4.42,-4.42L4.8,12l1.39,-1.39 4.42,-4.42L12,4.8l1.39,1.39 4.42,4.42L19.2,12l-1.39,1.39 -4.42,4.42z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_nearby_devices", "label": "Nearby devices", "label_ptr": "permgrouplab_nearby_devices", "name": "android.permission-group.NEARBY_DEVICES"}, "android.permission-group.PHONE": {"description": "make and manage phone calls", "description_ptr": "permgroupdesc_phone", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M6.62 10.79c1.44 2.83 3.76 5.14 6.59 6.59l2.2-2.2c.27-.27 .67 -.36 1.02-.24 1.12 .37 2.33 .57 3.57 .57 .55 0 1 .45 1 1V20c0 .55-.45 1-1 1-9.39 0-17-7.61-17-17 0-.55 .45 -1 1-1h3.5c.55 0 1 .45 1 1 0 1.25 .2 2.45 .57 3.57 .11 .35 .03 .74-.25 1.02l-2.2 2.2z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_phone_calls", "label": "Phone", "label_ptr": "permgrouplab_phone", "name": "android.permission-group.PHONE"}, "android.permission-group.SENSORS": {"description": "access sensor data about your vital signs", "description_ptr": "permgroupdesc_sensors", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M16.5,3c-1.74,0 -3.41,0.81 -4.5,2.09C10.91,3.81 9.24,3 7.5,3C4.42,3 2,5.42 2,                         8.5c0,3.78 3.4,6.86 8.55,11.54L12,21.35l1.45,-1.32C18.6,15.36 22,12.28 22,8.5C22,                         5.42 19.58,3 16.5,3zM12.1,18.55l-0.1,0.1l-0.1,-0.1C7.14,14.24 4,11.39 4,8.5C4,6.5 5.5,                         5 7.5,5c1.54,0 3.04,0.99 3.57,2.36h1.87C13.46,5.99 14.96,5 16.5,5c2,0 3.5,1.5 3.5,3.5C20,                         11.39 16.86,14.24 12.1,18.55z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_sensors", "label": "Body sensors", "label_ptr": "permgrouplab_sensors", "name": "android.permission-group.SENSORS"}, "android.permission-group.SMS": {"description": "send and view SMS messages", "description_ptr": "permgroupdesc_sms", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M20,2H4C2.9,2 2,2.9 2,4v18l4.75,-4h14C21.1,18 22,17.1 22,16V4C22,2.9 21.1,2 20,2zM20,16H4V4h16V16zM9,11H7V9h2V11zM17,11h-2V9h2V11zM13,11h-2V9h2V11z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_sms", "label": "SMS", "label_ptr": "permgrouplab_sms", "name": "android.permission-group.SMS"}, "android.permission-group.STORAGE": {"description": "access photos, media, and files on your device", "description_ptr": "permgroupdesc_storage", "icon": "<?xml version=\"1.0\" ?><svg xmlns=\"http://www.w3.org/2000/svg\" width=\"24\" height=\"24\" viewBox=\"0 0 24 24\"><path d=\"M20,6h-8l-2,-2H4C2.9,4 2.01,4.9 2.01,6L2,18c0,1.1 0.9,2 2,2h16c1.1,0 2,-0.9 2,-2V8C22,6.9 21.1,6 20,6zM20,18H4V8h16V18z\" fill=\"#000000\"/></svg>", "icon_ptr": "perm_group_storage", "label": "Files and media", "label_ptr": "permgrouplab_storage", "name": "android.permission-group.STORAGE"}, "android.permission-group.UNDEFINED": {"description": "", "description_ptr": "", "icon": "", "icon_ptr": "", "label": "", "label_ptr": "", "name": "android.permission-group.UNDEFINED"}}, "permissions": {"android.intent.category.MASTER_CLEAR.permission.C2D_MESSAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.intent.category.MASTER_CLEAR.permission.C2D_MESSAGE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCEPT_HANDOVER": {"description": "Allows the app to continue a call which was started in another app.", "description_ptr": "permdesc_acceptHandovers", "label": "", "label_ptr": "", "name": "android.permission.ACCEPT_HANDOVER", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.ACCESS_AMBIENT_LIGHT_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_AMBIENT_LIGHT_STATS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.ACCESS_BACKGROUND_LOCATION": {"description": "This app can access location at any time, even while the app is not in use.", "description_ptr": "permdesc_accessBackgroundLocation", "label": "access location in the background", "label_ptr": "permlab_accessBackgroundLocation", "name": "android.permission.ACCESS_BACKGROUND_LOCATION", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|instant"}, "android.permission.ACCESS_BLOBS_ACROSS_USERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_BLOBS_ACROSS_USERS", "permissionGroup": "", "protectionLevel": "signature|privileged|development|role"}, "android.permission.ACCESS_BROADCAST_RADIO": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_BROADCAST_RADIO", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_CACHE_FILESYSTEM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_CACHE_FILESYSTEM", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_CHECKIN_PROPERTIES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_CHECKIN_PROPERTIES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_COARSE_LOCATION": {"description": "This app can get your approximate location from location services while the app is in use. Location services for your device must be turned on for the app to get location.", "description_ptr": "permdesc_accessCoarseLocation", "label": "access approximate location only in the foreground", "label_ptr": "permlab_accessCoarseLocation", "name": "android.permission.ACCESS_COARSE_LOCATION", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|instant"}, "android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_CONTEXT_HUB": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_CONTEXT_HUB", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_DRM_CERTIFICATES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_DRM_CERTIFICATES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_FINE_LOCATION": {"description": "This app can get your precise location from location services while the app is in use. Location services for your device must be turned on for the app to get location. This may increase battery usage.", "description_ptr": "permdesc_accessFineLocation", "label": "access precise location only in the foreground", "label_ptr": "permlab_accessFineLocation", "name": "android.permission.ACCESS_FINE_LOCATION", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|instant"}, "android.permission.ACCESS_FM_RADIO": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_FM_RADIO", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_IMS_CALL_SERVICE": {"description": "Allows the app to use the IMS service to make calls without your intervention.", "description_ptr": "permdesc_accessImsCallService", "label": "access IMS call service", "label_ptr": "permlab_accessImsCallService", "name": "android.permission.ACCESS_IMS_CALL_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_INPUT_FLINGER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_INPUT_FLINGER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_INSTANT_APPS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_INSTANT_APPS", "permissionGroup": "", "protectionLevel": "signature|installer|verifier|role"}, "android.permission.ACCESS_KEYGUARD_SECURE_STORAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_KEYGUARD_SECURE_STORAGE", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS": {"description": "Allows the app to access\n        extra location provider commands.  This may allow the app to interfere\n        with the operation of the GPS or other location sources.", "description_ptr": "permdesc_accessLocationExtraCommands", "label": "access extra location provider commands", "label_ptr": "permlab_accessLocationExtraCommands", "name": "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.ACCESS_LOCUS_ID_USAGE_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_LOCUS_ID_USAGE_STATS", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.ACCESS_LOWPAN_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_LOWPAN_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_MEDIA_LOCATION": {"description": "Allows the app to read locations from your media collection.", "description_ptr": "permdesc_mediaLocation", "label": "read locations from your media collection", "label_ptr": "permlab_mediaLocation", "name": "android.permission.ACCESS_MEDIA_LOCATION", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.ACCESS_MESSAGES_ON_ICC": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_MESSAGES_ON_ICC", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_MOCK_LOCATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_MOCK_LOCATION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_MTP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_MTP", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_NETWORK_CONDITIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_NETWORK_CONDITIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_NETWORK_STATE": {"description": "Allows the app to view\n      information about network connections such as which networks exist and are\n      connected.", "description_ptr": "permdesc_accessNetworkState", "label": "view network connections", "label_ptr": "permlab_accessNetworkState", "name": "android.permission.ACCESS_NETWORK_STATE", "permissionGroup": "", "protectionLevel": "normal|instant"}, "android.permission.ACCESS_NOTIFICATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_NOTIFICATIONS", "permissionGroup": "", "protectionLevel": "signature|privileged|appop"}, "android.permission.ACCESS_NOTIFICATION_POLICY": {"description": "Allows the app to read and write Do Not Disturb configuration.", "description_ptr": "permdesc_access_notification_policy", "label": "access Do Not Disturb", "label_ptr": "permlab_access_notification_policy", "name": "android.permission.ACCESS_NOTIFICATION_POLICY", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.ACCESS_PDB_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_PDB_STATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_RCS_USER_CAPABILITY_EXCHANGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_RCS_USER_CAPABILITY_EXCHANGE", "permissionGroup": "", "protectionLevel": "internal|role"}, "android.permission.ACCESS_SHARED_LIBRARIES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_SHARED_LIBRARIES", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.ACCESS_SHORTCUTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_SHORTCUTS", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.ACCESS_SURFACE_FLINGER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_SURFACE_FLINGER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_TUNED_INFO": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_TUNED_INFO", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged"}, "android.permission.ACCESS_TV_DESCRAMBLER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_TV_DESCRAMBLER", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged"}, "android.permission.ACCESS_TV_TUNER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_TV_TUNER", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged"}, "android.permission.ACCESS_UCE_OPTIONS_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_UCE_OPTIONS_SERVICE", "permissionGroup": "android.permission-group.PHONE", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_UCE_PRESENCE_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_UCE_PRESENCE_SERVICE", "permissionGroup": "android.permission-group.PHONE", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_VIBRATOR_STATE": {"description": "Allows the app to access the vibrator state.", "description_ptr": "permdesc_vibrator_state", "label": "Allows the app to access the vibrator state.", "label_ptr": "permdesc_vibrator_state", "name": "android.permission.ACCESS_VIBRATOR_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACCESS_VOICE_INTERACTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_VOICE_INTERACTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_VR_MANAGER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_VR_MANAGER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACCESS_VR_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCESS_VR_STATE", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.ACCESS_WIFI_STATE": {"description": "Allows the app to view information\n      about Wi-Fi networking, such as whether Wi-Fi is enabled and name of\n      connected Wi-Fi devices.", "description_ptr": "permdesc_accessWifiState", "label": "view Wi-Fi connections", "label_ptr": "permlab_accessWifiState", "name": "android.permission.ACCESS_WIFI_STATE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.ACCOUNT_MANAGER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACCOUNT_MANAGER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ACTIVITY_EMBEDDING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACTIVITY_EMBEDDING", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ACTIVITY_RECOGNITION": {"description": "This app can recognize your physical activity.", "description_ptr": "permdesc_activityRecognition", "label": "recognize physical activity", "label_ptr": "permlab_activityRecognition", "name": "android.permission.ACTIVITY_RECOGNITION", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|instant"}, "android.permission.ACT_AS_PACKAGE_FOR_ACCESSIBILITY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ACT_AS_PACKAGE_FOR_ACCESSIBILITY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ADD_TRUSTED_DISPLAY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ADD_TRUSTED_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ADJUST_RUNTIME_PERMISSIONS_POLICY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ADJUST_RUNTIME_PERMISSIONS_POLICY", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.ALLOCATE_AGGRESSIVE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ALLOCATE_AGGRESSIVE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ALLOW_ANY_CODEC_FOR_PLAYBACK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ALLOW_ANY_CODEC_FOR_PLAYBACK", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.AMBIENT_WALLPAPER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.AMBIENT_WALLPAPER", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.ANSWER_PHONE_CALLS": {"description": "Allows the app to answer an incoming phone call.", "description_ptr": "permdesc_answerPhoneCalls", "label": "answer phone calls", "label_ptr": "permlab_answerPhoneCalls", "name": "android.permission.ANSWER_PHONE_CALLS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|runtime"}, "android.permission.APPROVE_INCIDENT_REPORTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.APPROVE_INCIDENT_REPORTS", "permissionGroup": "", "protectionLevel": "signature|incidentReportApprover"}, "android.permission.ASEC_ACCESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ASEC_ACCESS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ASEC_CREATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ASEC_CREATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ASEC_DESTROY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ASEC_DESTROY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ASEC_MOUNT_UNMOUNT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ASEC_MOUNT_UNMOUNT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ASEC_RENAME": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ASEC_RENAME", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ASSOCIATE_COMPANION_DEVICES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ASSOCIATE_COMPANION_DEVICES", "permissionGroup": "", "protectionLevel": "internal|role"}, "android.permission.ASSOCIATE_INPUT_DEVICE_TO_DISPLAY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ASSOCIATE_INPUT_DEVICE_TO_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.AUTHENTICATE_ACCOUNTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.AUTHENTICATE_ACCOUNTS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.BACKGROUND_CAMERA": {"description": "This app can take pictures and record videos using the camera at any time.", "description_ptr": "permdesc_backgroundCamera", "label": "take pictures and videos in the background", "label_ptr": "permlab_backgroundCamera", "name": "android.permission.BACKGROUND_CAMERA", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "internal|role"}, "android.permission.BACKUP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BACKUP", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BATTERY_PREDICTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BATTERY_PREDICTION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BATTERY_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BATTERY_STATS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.BIND_ACCESSIBILITY_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_ACCESSIBILITY_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_APPWIDGET": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_APPWIDGET", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_ATTENTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_ATTENTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_AUGMENTED_AUTOFILL_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_AUGMENTED_AUTOFILL_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_AUTOFILL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_AUTOFILL", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_AUTOFILL_FIELD_CLASSIFICATION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_AUTOFILL_FIELD_CLASSIFICATION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_AUTOFILL_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_AUTOFILL_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CACHE_QUOTA_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CACHE_QUOTA_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CALL_DIAGNOSTIC_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CALL_DIAGNOSTIC_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CALL_REDIRECTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CALL_REDIRECTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_CARRIER_MESSAGING_CLIENT_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CARRIER_MESSAGING_CLIENT_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CARRIER_MESSAGING_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CARRIER_MESSAGING_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_CARRIER_SERVICES": {"description": "Allows the holder to bind to carrier services. Should never be needed for normal apps.", "description_ptr": "permdesc_bindCarrierServices", "label": "bind to carrier services", "label_ptr": "permlab_bindCarrierServices", "name": "android.permission.BIND_CARRIER_SERVICES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_CELL_BROADCAST_SERVICE": {"description": "Allows the app to bind to the\n        cell broadcast module in order to forward cell broadcast messages\n        as they are received. Cell broadcast alerts are delivered in some\n        locations to warn you of emergency situations. Malicious apps may\n        interfere with the performance or operation of your device when an\n        emergency cell broadcast is received.", "description_ptr": "permdesc_bindCellBroadcastService", "label": "Forward cell broadcast messages", "label_ptr": "permlab_bindCellBroadcastService", "name": "android.permission.BIND_CELL_BROADCAST_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CHOOSER_TARGET_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CHOOSER_TARGET_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_COMPANION_DEVICE_MANAGER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_COMPANION_DEVICE_MANAGER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_COMPANION_DEVICE_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_COMPANION_DEVICE_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CONDITION_PROVIDER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CONDITION_PROVIDER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CONNECTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CONNECTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_CONTENT_CAPTURE_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CONTENT_CAPTURE_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CONTENT_SUGGESTIONS_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CONTENT_SUGGESTIONS_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_CONTROLS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_CONTROLS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_DEVICE_ADMIN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_DEVICE_ADMIN", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_DIRECTORY_SEARCH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_DIRECTORY_SEARCH", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_DISPLAY_HASHING_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_DISPLAY_HASHING_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_DOMAIN_VERIFICATION_AGENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_DOMAIN_VERIFICATION_AGENT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_DREAM_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_DREAM_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_EUICC_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_EUICC_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_EXPLICIT_HEALTH_CHECK_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_EXPLICIT_HEALTH_CHECK_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_EXTERNAL_STORAGE_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_EXTERNAL_STORAGE_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_GBA_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_GBA_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_HOTWORD_DETECTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_HOTWORD_DETECTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_IMS_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_IMS_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged"}, "android.permission.BIND_INCALL_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_INCALL_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_INLINE_SUGGESTION_RENDER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_INLINE_SUGGESTION_RENDER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_INPUT_METHOD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_INPUT_METHOD", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_INTENT_FILTER_VERIFIER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_INTENT_FILTER_VERIFIER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_JOB_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_JOB_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_KEYGUARD_APPWIDGET": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_KEYGUARD_APPWIDGET", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_MIDI_DEVICE_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_MIDI_DEVICE_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_MUSIC_RECOGNITION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_MUSIC_RECOGNITION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_NETWORK_RECOMMENDATION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_NETWORK_RECOMMENDATION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_NFC_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_NFC_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_NOTIFICATION_ASSISTANT_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_NOTIFICATION_ASSISTANT_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_NOTIFICATION_LISTENER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_NOTIFICATION_LISTENER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_PACKAGE_VERIFIER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_PACKAGE_VERIFIER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_PHONE_ACCOUNT_SUGGESTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_PHONE_ACCOUNT_SUGGESTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_PRINT_RECOMMENDATION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_PRINT_RECOMMENDATION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_PRINT_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_PRINT_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_PRINT_SPOOLER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_PRINT_SPOOLER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_QUICK_ACCESS_WALLET_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_QUICK_ACCESS_WALLET_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_QUICK_SETTINGS_TILE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_QUICK_SETTINGS_TILE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_REMOTEVIEWS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_REMOTEVIEWS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_REMOTE_DISPLAY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_REMOTE_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_RESOLVER_RANKER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_RESOLVER_RANKER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_RESUME_ON_REBOOT_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_RESUME_ON_REBOOT_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_ROTATION_RESOLVER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_ROTATION_RESOLVER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_ROUTE_PROVIDER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_ROUTE_PROVIDER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_RUNTIME_PERMISSION_PRESENTER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_RUNTIME_PERMISSION_PRESENTER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_SCREENING_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_SCREENING_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_SETTINGS_SUGGESTIONS_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_SETTINGS_SUGGESTIONS_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_SOUND_TRIGGER_DETECTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_SOUND_TRIGGER_DETECTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TELECOM_CONNECTION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TELECOM_CONNECTION_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_TELEPHONY_DATA_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TELEPHONY_DATA_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TELEPHONY_NETWORK_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TELEPHONY_NETWORK_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TEXTCLASSIFIER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TEXTCLASSIFIER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TEXT_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TEXT_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TIME_ZONE_PROVIDER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TIME_ZONE_PROVIDER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TRANSLATION_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TRANSLATION_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TRUST_AGENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TRUST_AGENT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_TV_INPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TV_INPUT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_TV_REMOTE_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_TV_REMOTE_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_VISUAL_VOICEMAIL_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_VISUAL_VOICEMAIL_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BIND_VOICE_INTERACTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_VOICE_INTERACTION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_VPN_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_VPN_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_VR_LISTENER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_VR_LISTENER_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BIND_WALLPAPER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BIND_WALLPAPER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BLUETOOTH": {"description": "Allows the app to view the\n      configuration of the Bluetooth on the phone, and to make and accept\n      connections with paired devices.", "description_ptr": "permdesc_bluetooth", "label": "pair with Bluetooth devices", "label_ptr": "permlab_bluetooth", "name": "android.permission.BLUETOOTH", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.BLUETOOTH_ADMIN": {"description": "Allows the app to configure\n      the local Bluetooth phone, and to discover and pair with remote devices.", "description_ptr": "permdesc_bluetoothAdmin", "label": "access Bluetooth settings", "label_ptr": "permlab_bluetoothAdmin", "name": "android.permission.BLUETOOTH_ADMIN", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.BLUETOOTH_ADVERTISE": {"description": "Allows the app to advertise to nearby Bluetooth devices", "description_ptr": "permdesc_bluetooth_advertise", "label": "advertise to nearby Bluetooth devices", "label_ptr": "permlab_bluetooth_advertise", "name": "android.permission.BLUETOOTH_ADVERTISE", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.BLUETOOTH_CONNECT": {"description": "Allows the app to connect to paired Bluetooth devices", "description_ptr": "permdesc_bluetooth_connect", "label": "connect to paired Bluetooth devices", "label_ptr": "permlab_bluetooth_connect", "name": "android.permission.BLUETOOTH_CONNECT", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.BLUETOOTH_MAP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BLUETOOTH_MAP", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BLUETOOTH_PRIVILEGED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BLUETOOTH_PRIVILEGED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BLUETOOTH_SCAN": {"description": "Allows the app to discover and pair nearby Bluetooth devices", "description_ptr": "permdesc_bluetooth_scan", "label": "discover and pair nearby Bluetooth devices", "label_ptr": "permlab_bluetooth_scan", "name": "android.permission.BLUETOOTH_SCAN", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.BLUETOOTH_STACK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BLUETOOTH_STACK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BODY_SENSORS": {"description": "Allows the app to access data from sensors\n    that monitor your physical condition, such as your heart rate.", "description_ptr": "permdesc_bodySensors", "label": "access body sensors (like heart rate monitors)\n    ", "label_ptr": "permlab_bodySensors", "name": "android.permission.BODY_SENSORS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.BRICK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BRICK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BRIGHTNESS_SLIDER_USAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BRIGHTNESS_SLIDER_USAGE", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BROADCAST_CLOSE_SYSTEM_DIALOGS", "permissionGroup": "", "protectionLevel": "signature|privileged|recents"}, "android.permission.BROADCAST_NETWORK_PRIVILEGED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BROADCAST_NETWORK_PRIVILEGED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.BROADCAST_PACKAGE_REMOVED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BROADCAST_PACKAGE_REMOVED", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BROADCAST_SMS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BROADCAST_SMS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BROADCAST_STICKY": {"description": "Allows the app to\n    send sticky broadcasts, which remain after the broadcast ends. Excessive\n    use may make the phone slow or unstable by causing it to use too\n    much memory.", "description_ptr": "permdesc_broadcastSticky", "label": "send sticky broadcast", "label_ptr": "permlab_broadcastSticky", "name": "android.permission.BROADCAST_STICKY", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.BROADCAST_WAP_PUSH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BROADCAST_WAP_PUSH", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.BYPASS_ROLE_QUALIFICATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.BYPASS_ROLE_QUALIFICATION", "permissionGroup": "", "protectionLevel": "internal|role"}, "android.permission.CACHE_CONTENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CACHE_CONTENT", "permissionGroup": "", "protectionLevel": "signature|documenter"}, "android.permission.CALL_COMPANION_APP": {"description": "Allows the app to see and control ongoing calls on the\n        device. This includes information such as call numbers for calls and the state of the\n        calls.", "description_ptr": "permdesc_callCompanionApp", "label": "see and control calls through the system.", "label_ptr": "permlab_callCompanionApp", "name": "android.permission.CALL_COMPANION_APP", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.CALL_PHONE": {"description": "Allows the app to call phone numbers\n      without your intervention. This may result in unexpected charges or calls.\n      Note that this doesn't allow the app to call emergency numbers.\n      Malicious apps may cost you money by making calls without your\n      confirmation.", "description_ptr": "permdesc_callPhone", "label": "directly call phone numbers", "label_ptr": "permlab_callPhone", "name": "android.permission.CALL_PHONE", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.CALL_PRIVILEGED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CALL_PRIVILEGED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CAMERA": {"description": "This app can take pictures and record videos using the camera while the app is in use.", "description_ptr": "permdesc_camera", "label": "take pictures and videos", "label_ptr": "permlab_camera", "name": "android.permission.CAMERA", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|instant"}, "android.permission.CAMERA_DISABLE_TRANSMIT_LED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAMERA_DISABLE_TRANSMIT_LED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CAMERA_INJECT_EXTERNAL_CAMERA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAMERA_INJECT_EXTERNAL_CAMERA", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CAMERA_OPEN_CLOSE_LISTENER": {"description": "This app can receive callbacks when any camera device is being opened (by what application) or closed.", "description_ptr": "permdesc_cameraOpenCloseListener", "label": "Allow an application or service to receive callbacks about camera devices being opened or closed.", "label_ptr": "permlab_cameraOpenCloseListener", "name": "android.permission.CAMERA_OPEN_CLOSE_LISTENER", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "signature"}, "android.permission.CAMERA_SEND_SYSTEM_EVENTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAMERA_SEND_SYSTEM_EVENTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CAPTURE_AUDIO_HOTWORD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_AUDIO_HOTWORD", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.CAPTURE_AUDIO_OUTPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_AUDIO_OUTPUT", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.CAPTURE_BLACKOUT_CONTENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_BLACKOUT_CONTENT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CAPTURE_MEDIA_OUTPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_MEDIA_OUTPUT", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.CAPTURE_SECURE_VIDEO_OUTPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_SECURE_VIDEO_OUTPUT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CAPTURE_TUNER_AUDIO_INPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_TUNER_AUDIO_INPUT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CAPTURE_TV_INPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_TV_INPUT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CAPTURE_VIDEO_OUTPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_VIDEO_OUTPUT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CAPTURE_VOICE_COMMUNICATION_OUTPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CAPTURE_VOICE_COMMUNICATION_OUTPUT", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.CARRIER_FILTER_SMS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CARRIER_FILTER_SMS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CHANGE_ACCESSIBILITY_VOLUME": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_ACCESSIBILITY_VOLUME", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CHANGE_APP_IDLE_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_APP_IDLE_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CHANGE_BACKGROUND_DATA_SETTING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_BACKGROUND_DATA_SETTING", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CHANGE_COMPONENT_ENABLED_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_COMPONENT_ENABLED_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CHANGE_CONFIGURATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_CONFIGURATION", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CHANGE_HDMI_CEC_ACTIVE_SOURCE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_HDMI_CEC_ACTIVE_SOURCE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CHANGE_LOWPAN_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_LOWPAN_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CHANGE_NETWORK_STATE": {"description": "Allows the app to change the state of network connectivity.", "description_ptr": "permdesc_changeNetworkState", "label": "change network connectivity", "label_ptr": "permlab_changeNetworkState", "name": "android.permission.CHANGE_NETWORK_STATE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.CHANGE_OVERLAY_PACKAGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CHANGE_OVERLAY_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CHANGE_WIFI_MULTICAST_STATE": {"description": "Allows the app to receive\n      packets sent to all devices on a Wi-Fi network using multicast addresses,\n      not just your phone.  It uses more power than the non-multicast mode.", "description_ptr": "permdesc_changeWifiMulticastState", "label": "allow Wi-Fi Multicast reception", "label_ptr": "permlab_changeWifiMulticastState", "name": "android.permission.CHANGE_WIFI_MULTICAST_STATE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.CHANGE_WIFI_STATE": {"description": "Allows the app to connect to and\n      disconnect from Wi-Fi access points and to make changes to device\n      configuration for Wi-Fi networks.", "description_ptr": "permdesc_changeWifiState", "label": "connect and disconnect from Wi-Fi", "label_ptr": "permlab_changeWifiState", "name": "android.permission.CHANGE_WIFI_STATE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.CLEAR_APP_CACHE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CLEAR_APP_CACHE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CLEAR_APP_GRANTED_URI_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CLEAR_APP_GRANTED_URI_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CLEAR_APP_USER_DATA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CLEAR_APP_USER_DATA", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.CLEAR_FREEZE_PERIOD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CLEAR_FREEZE_PERIOD", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.COMPANION_APPROVE_WIFI_CONNECTIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.COMPANION_APPROVE_WIFI_CONNECTIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONFIGURE_DISPLAY_BRIGHTNESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONFIGURE_DISPLAY_BRIGHTNESS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.CONFIGURE_DISPLAY_COLOR_MODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONFIGURE_DISPLAY_COLOR_MODE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONFIGURE_INTERACT_ACROSS_PROFILES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONFIGURE_INTERACT_ACROSS_PROFILES", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONFIGURE_WIFI_DISPLAY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONFIGURE_WIFI_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONFIRM_FULL_BACKUP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONFIRM_FULL_BACKUP", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONNECTIVITY_INTERNAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONNECTIVITY_INTERNAL", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_ALWAYS_ON_VPN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_ALWAYS_ON_VPN", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONTROL_DEVICE_LIGHTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_DEVICE_LIGHTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_DEVICE_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_DEVICE_STATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONTROL_DISPLAY_BRIGHTNESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_DISPLAY_BRIGHTNESS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_DISPLAY_COLOR_TRANSFORMS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_DISPLAY_SATURATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_DISPLAY_SATURATION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_INCALL_EXPERIENCE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_INCALL_EXPERIENCE", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.CONTROL_KEYGUARD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_KEYGUARD", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONTROL_KEYGUARD_SECURE_NOTIFICATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_KEYGUARD_SECURE_NOTIFICATIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_LOCATION_UPDATES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_LOCATION_UPDATES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_OEM_PAID_NETWORK_PREFERENCE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_OEM_PAID_NETWORK_PREFERENCE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_REMOTE_APP_TRANSITION_ANIMATIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_UI_TRACING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_UI_TRACING", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.CONTROL_VPN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_VPN", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.CONTROL_WIFI_DISPLAY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CONTROL_WIFI_DISPLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.COPY_PROTECTED_DATA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.COPY_PROTECTED_DATA", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CREATE_USERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CREATE_USERS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.CRYPT_KEEPER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.CRYPT_KEEPER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.DELETE_CACHE_FILES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DELETE_CACHE_FILES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.DELETE_PACKAGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DELETE_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.DEVICE_POWER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DEVICE_POWER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.DIAGNOSTIC": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DIAGNOSTIC", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.DISABLE_HIDDEN_API_CHECKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DISABLE_HIDDEN_API_CHECKS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.DISABLE_INPUT_DEVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DISABLE_INPUT_DEVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.DISABLE_KEYGUARD": {"description": "Allows the app to disable the\n      keylock and any associated password security.  For example, the phone\n      disables the keylock when receiving an incoming phone call, then\n      re-enables the keylock when the call is finished.", "description_ptr": "permdesc_disableKeyguard", "label": "disable your screen lock", "label_ptr": "permlab_disableKeyguard", "name": "android.permission.DISABLE_KEYGUARD", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.DISABLE_SYSTEM_SOUND_EFFECTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DISABLE_SYSTEM_SOUND_EFFECTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.DISPATCH_NFC_MESSAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DISPATCH_NFC_MESSAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.DISPATCH_PROVISIONING_MESSAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DISPATCH_PROVISIONING_MESSAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.DOMAIN_VERIFICATION_AGENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DOMAIN_VERIFICATION_AGENT", "permissionGroup": "", "protectionLevel": "internal|privileged"}, "android.permission.DUMP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DUMP", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.DVB_DEVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.DVB_DEVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.ENABLE_TEST_HARNESS_MODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ENABLE_TEST_HARNESS_MODE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.ENTER_CAR_MODE_PRIORITIZED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ENTER_CAR_MODE_PRIORITIZED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.EXEMPT_FROM_AUDIO_RECORD_RESTRICTIONS": {"description": "Exempt the app from restrictions to record audio.", "description_ptr": "permdesc_exemptFromAudioRecordRestrictions", "label": "exempt from audio record restrictions", "label_ptr": "permlab_exemptFromAudioRecordRestrictions", "name": "android.permission.EXEMPT_FROM_AUDIO_RECORD_RESTRICTIONS", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.EXPAND_STATUS_BAR": {"description": "Allows the app to expand or collapse the status bar.", "description_ptr": "permdesc_expandStatusBar", "label": "expand/collapse status bar", "label_ptr": "permlab_expandStatusBar", "name": "android.permission.EXPAND_STATUS_BAR", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.FACTORY_TEST": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FACTORY_TEST", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FILTER_EVENTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FILTER_EVENTS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FLASHLIGHT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FLASHLIGHT", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.FORCE_BACK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FORCE_BACK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FORCE_DEVICE_POLICY_MANAGER_LOGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FORCE_DEVICE_POLICY_MANAGER_LOGS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FORCE_PERSISTABLE_URI_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FORCE_PERSISTABLE_URI_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FORCE_STOP_PACKAGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FORCE_STOP_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.FOREGROUND_SERVICE": {"description": "Allows the app to make use of foreground services.", "description_ptr": "permdesc_foregroundService", "label": "run foreground service", "label_ptr": "permlab_foregroundService", "name": "android.permission.FOREGROUND_SERVICE", "permissionGroup": "", "protectionLevel": "normal|instant"}, "android.permission.FRAME_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FRAME_STATS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.FREEZE_SCREEN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.FREEZE_SCREEN", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GET_ACCOUNTS": {"description": "Allows the app to get\n      the list of accounts known by the phone.  This may include any accounts\n      created by applications you have installed.", "description_ptr": "permdesc_getAccounts", "label": "find accounts on the device", "label_ptr": "permlab_getAccounts", "name": "android.permission.GET_ACCOUNTS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.GET_ACCOUNTS_PRIVILEGED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_ACCOUNTS_PRIVILEGED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.GET_APP_GRANTED_URI_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_APP_GRANTED_URI_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GET_APP_OPS_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_APP_OPS_STATS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.GET_DETAILED_TASKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_DETAILED_TASKS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GET_INTENT_SENDER_INTENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_INTENT_SENDER_INTENT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GET_PACKAGE_SIZE": {"description": "Allows the app to retrieve its code, data, and cache sizes", "description_ptr": "permdesc_getPackageSize", "label": "measure app storage space", "label_ptr": "permlab_getPackageSize", "name": "android.permission.GET_PACKAGE_SIZE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.GET_PASSWORD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_PASSWORD", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GET_PEOPLE_TILE_PREVIEW": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_PEOPLE_TILE_PREVIEW", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.GET_PROCESS_STATE_AND_OOM_SCORE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_PROCESS_STATE_AND_OOM_SCORE", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.GET_RUNTIME_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_RUNTIME_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GET_TASKS": {"description": "Allows the app to retrieve information\n       about currently and recently running tasks.  This may allow the app to\n       discover information about which applications are used on the device.", "description_ptr": "permdesc_getTasks", "label": "retrieve running apps", "label_ptr": "permlab_getTasks", "name": "android.permission.GET_TASKS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.GET_TOP_ACTIVITY_INFO": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GET_TOP_ACTIVITY_INFO", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.GLOBAL_SEARCH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GLOBAL_SEARCH", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.GLOBAL_SEARCH_CONTROL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GLOBAL_SEARCH_CONTROL", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GRANT_PROFILE_OWNER_DEVICE_IDS_ACCESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GRANT_PROFILE_OWNER_DEVICE_IDS_ACCESS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.GRANT_RUNTIME_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GRANT_RUNTIME_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|installer|verifier"}, "android.permission.GRANT_RUNTIME_PERMISSIONS_TO_TELEPHONY_DEFAULTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.GRANT_RUNTIME_PERMISSIONS_TO_TELEPHONY_DEFAULTS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.HANDLE_CAR_MODE_CHANGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.HANDLE_CAR_MODE_CHANGES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.HARDWARE_TEST": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.HARDWARE_TEST", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.HDMI_CEC": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.HDMI_CEC", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged"}, "android.permission.HIDE_NON_SYSTEM_OVERLAY_WINDOWS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.HIDE_NON_SYSTEM_OVERLAY_WINDOWS", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.HIDE_OVERLAY_WINDOWS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.HIDE_OVERLAY_WINDOWS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.HIGH_SAMPLING_RATE_SENSORS": {"description": "Allows the app to sample sensor data at a rate greater than 200 Hz", "description_ptr": "permdesc_highSamplingRateSensors", "label": "access sensor data at a high sampling rate", "label_ptr": "permlab_highSamplingRateSensors", "name": "android.permission.HIGH_SAMPLING_RATE_SENSORS", "permissionGroup": "android.permission-group.SENSORS", "protectionLevel": "normal"}, "android.permission.INJECT_EVENTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INJECT_EVENTS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.INPUT_CONSUMER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INPUT_CONSUMER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.INSTALL_DYNAMIC_SYSTEM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_DYNAMIC_SYSTEM", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.INSTALL_GRANT_RUNTIME_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_GRANT_RUNTIME_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|installer|verifier"}, "android.permission.INSTALL_LOCATION_PROVIDER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_LOCATION_PROVIDER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.INSTALL_LOCATION_TIME_ZONE_PROVIDER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_LOCATION_TIME_ZONE_PROVIDER_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.INSTALL_PACKAGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.INSTALL_PACKAGE_UPDATES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_PACKAGE_UPDATES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.INSTALL_SELF_UPDATES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_SELF_UPDATES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.INSTALL_TEST_ONLY_PACKAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTALL_TEST_ONLY_PACKAGE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.INSTANT_APP_FOREGROUND_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INSTANT_APP_FOREGROUND_SERVICE", "permissionGroup": "", "protectionLevel": "signature|development|instant|appop"}, "android.permission.INTENT_FILTER_VERIFICATION_AGENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INTENT_FILTER_VERIFICATION_AGENT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.INTERACT_ACROSS_PROFILES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INTERACT_ACROSS_PROFILES", "permissionGroup": "", "protectionLevel": "signature|appop"}, "android.permission.INTERACT_ACROSS_USERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INTERACT_ACROSS_USERS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.INTERACT_ACROSS_USERS_FULL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INTERACT_ACROSS_USERS_FULL", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.INTERNAL_DELETE_CACHE_FILES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INTERNAL_DELETE_CACHE_FILES", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.INTERNAL_SYSTEM_WINDOW": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INTERNAL_SYSTEM_WINDOW", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.INTERNET": {"description": "Allows the app to create\n     network sockets and use custom network protocols. The browser and other\n     applications provide means to send data to the internet, so this\n     permission is not required to send data to the internet.", "description_ptr": "permdesc_createNetworkSockets", "label": "have full network access", "label_ptr": "permlab_createNetworkSockets", "name": "android.permission.INTERNET", "permissionGroup": "", "protectionLevel": "normal|instant"}, "android.permission.INVOKE_CARRIER_SETUP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.INVOKE_CARRIER_SETUP", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.KEEP_UNINSTALLED_PACKAGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.KEEP_UNINSTALLED_PACKAGES", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.KEYPHRASE_ENROLLMENT_APPLICATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.KEYPHRASE_ENROLLMENT_APPLICATION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.KILL_BACKGROUND_PROCESSES": {"description": "Allows the app to end\n      background processes of other apps.  This may cause other apps to stop\n      running.", "description_ptr": "permdesc_killBackgroundProcesses", "label": "close other apps", "label_ptr": "permlab_killBackgroundProcesses", "name": "android.permission.KILL_BACKGROUND_PROCESSES", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.KILL_UID": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.KILL_UID", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.LAUNCH_TRUST_AGENT_SETTINGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LAUNCH_TRUST_AGENT_SETTINGS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.LISTEN_ALWAYS_REPORTED_SIGNAL_STRENGTH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LISTEN_ALWAYS_REPORTED_SIGNAL_STRENGTH", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.LOADER_USAGE_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LOADER_USAGE_STATS", "permissionGroup": "", "protectionLevel": "signature|privileged|appop"}, "android.permission.LOCAL_MAC_ADDRESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LOCAL_MAC_ADDRESS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.LOCATION_HARDWARE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LOCATION_HARDWARE", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.LOCK_DEVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LOCK_DEVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.LOG_COMPAT_CHANGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LOG_COMPAT_CHANGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.LOOP_RADIO": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.LOOP_RADIO", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_ACCESSIBILITY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ACCESSIBILITY", "permissionGroup": "", "protectionLevel": "signature|setup|recents"}, "android.permission.MANAGE_ACCOUNTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ACCOUNTS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.MANAGE_ACTIVITY_STACKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ACTIVITY_STACKS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_ACTIVITY_TASKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ACTIVITY_TASKS", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.MANAGE_APPOPS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_APPOPS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_APP_HIBERNATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_APP_HIBERNATION", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.MANAGE_APP_OPS_MODES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_APP_OPS_MODES", "permissionGroup": "", "protectionLevel": "signature|installer|verifier"}, "android.permission.MANAGE_APP_OPS_RESTRICTIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_APP_OPS_RESTRICTIONS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.MANAGE_APP_PREDICTIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_APP_PREDICTIONS", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.MANAGE_APP_TOKENS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_APP_TOKENS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_AUDIO_POLICY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_AUDIO_POLICY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_AUTO_FILL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_AUTO_FILL", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_BIND_INSTANT_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANA<PERSON>_BIND_INSTANT_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_BIOMETRIC": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_BIOMETRIC", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_BIOMETRIC_DIALOG": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_BIOMETRIC_DIALOG", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_BLUETOOTH_WHEN_WIRELESS_CONSENT_REQUIRED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_BLUETOOTH_WHEN_WIRELESS_CONSENT_REQUIRED", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_CAMERA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_CAMERA", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_CARRIER_OEM_UNLOCK_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_CARRIER_OEM_UNLOCK_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_CA_CERTIFICATES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_CA_CERTIFICATES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_COMPANION_DEVICES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_COMPANION_DEVICES", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_CONTENT_CAPTURE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_CONTENT_CAPTURE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_CONTENT_SUGGESTIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_CONTENT_SUGGESTIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_CRATES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_CRATES", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_CREDENTIAL_MANAGEMENT_APP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_CREDENTIAL_MANAGEMENT_APP", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_DEBUGGING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_DEBUGGING", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_DEVICE_ADMINS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_DEVICE_ADMINS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_DOCUMENTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_DOCUMENTS", "permissionGroup": "", "protectionLevel": "signature|documenter"}, "android.permission.MANAGE_DYNAMIC_SYSTEM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_DYNAMIC_SYSTEM", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_EXTERNAL_STORAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_EXTERNAL_STORAGE", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "signature|appop|preinstalled"}, "android.permission.MANAGE_FACTORY_RESET_PROTECTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_FACTORY_RESET_PROTECTION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_FINGERPRINT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_FINGERPRINT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_GAME_MODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_GAME_MODE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_HOTWORD_DETECTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_HOTWORD_DETECTION", "permissionGroup": "", "protectionLevel": "internal|preinstalled"}, "android.permission.MANAGE_IPSEC_TUNNELS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_IPSEC_TUNNELS", "permissionGroup": "", "protectionLevel": "signature|appop"}, "android.permission.MANAGE_LOWPAN_INTERFACES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_LOWPAN_INTERFACES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_MEDIA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_MEDIA", "permissionGroup": "", "protectionLevel": "signature|appop|preinstalled"}, "android.permission.MANAGE_MEDIA_PROJECTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_MEDIA_PROJECTION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_MUSIC_RECOGNITION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_MUSIC_RECOGNITION", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.MANAGE_NETWORK_POLICY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_NETWORK_POLICY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_NOTIFICATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_NOTIFICATIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_NOTIFICATION_LISTENERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_NOTIFICATION_LISTENERS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.MANAGE_ONE_TIME_PERMISSION_SESSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ONE_TIME_PERMISSION_SESSIONS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.MANAGE_ONGOING_CALLS": {"description": "Allows an app to see details about ongoing calls\n         on your device and to control these calls.", "description_ptr": "permdesc_manageOngoingCalls", "label": "Manage ongoing calls", "label_ptr": "permlab_manageOngoingCalls", "name": "android.permission.MANAGE_ONGOING_CALLS", "permissionGroup": "", "protectionLevel": "signature|appop"}, "android.permission.MANAGE_OWN_CALLS": {"description": "Allows the app to route its calls through the system in\n        order to improve the calling experience.", "description_ptr": "permdesc_manageOwnCalls", "label": "route calls through the system", "label_ptr": "permlab_manageOwnCalls", "name": "android.permission.MANAGE_OWN_CALLS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS": {"description": "Allows apps to set the profile owners and the device owner.", "description_ptr": "permdesc_manageProfileAndDeviceOwners", "label": "manage profile and device owners", "label_ptr": "permlab_manageProfileAndDeviceOwners", "name": "android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_ROLE_HOLDERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ROLE_HOLDERS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.MANAGE_ROLLBACKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ROLLBACKS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_ROTATION_RESOLVER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_ROTATION_RESOLVER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_SCOPED_ACCESS_DIRECTORY_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SCOPED_ACCESS_DIRECTORY_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_SEARCH_UI": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SEARCH_UI", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.MANAGE_SENSORS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SENSORS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_SENSOR_PRIVACY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SENSOR_PRIVACY", "permissionGroup": "", "protectionLevel": "internal|role"}, "android.permission.MANAGE_SLICE_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SLICE_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_SMARTSPACE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SMARTSPACE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_SOUND_TRIGGER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SOUND_TRIGGER", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.MANAGE_SPEECH_RECOGNITION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SPEECH_RECOGNITION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_SUBSCRIPTION_PLANS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_SUBSCRIPTION_PLANS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_TEST_NETWORKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_TEST_NETWORKS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_TIME_AND_ZONE_DETECTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_TIME_AND_ZONE_DETECTION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_TOAST_RATE_LIMITING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_TOAST_RATE_LIMITING", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MANAGE_UI_TRANSLATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_UI_TRANSLATION", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.MANAGE_USB": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_USB", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_USERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_USERS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_USER_OEM_UNLOCK_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_USER_OEM_UNLOCK_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_VOICE_KEYPHRASES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_VOICE_KEYPHRASES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MANAGE_WIFI_COUNTRY_CODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MANAGE_WIFI_COUNTRY_CODE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MARK_DEVICE_ORGANIZATION_OWNED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MARK_DEVICE_ORGANIZATION_OWNED", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MASTER_CLEAR": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MASTER_CLEAR", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MEDIA_CONTENT_CONTROL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MEDIA_CONTENT_CONTROL", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MEDIA_RESOURCE_OVERRIDE_PID": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MEDIA_RESOURCE_OVERRIDE_PID", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MODIFY_ACCESSIBILITY_DATA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_ACCESSIBILITY_DATA", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MODIFY_AUDIO_ROUTING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_AUDIO_ROUTING", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.MODIFY_AUDIO_SETTINGS": {"description": "Allows the app to modify global audio settings such as volume and which speaker is used for output.", "description_ptr": "permdesc_modifyAudioSettings", "label": "change your audio settings", "label_ptr": "permlab_modifyAudioSettings", "name": "android.permission.MODIFY_AUDIO_SETTINGS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.MODIFY_CELL_BROADCASTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_CELL_BROADCASTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MODIFY_DAY_NIGHT_MODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_DAY_NIGHT_MODE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MODIFY_DEFAULT_AUDIO_EFFECTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_DEFAULT_AUDIO_EFFECTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MODIFY_NETWORK_ACCOUNTING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_NETWORK_ACCOUNTING", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MODIFY_PARENTAL_CONTROLS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_PARENTAL_CONTROLS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MODIFY_PHONE_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_PHONE_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.MODIFY_QUIET_MODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_QUIET_MODE", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.MODIFY_REFRESH_RATE_SWITCHING_TYPE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_REFRESH_RATE_SWITCHING_TYPE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MODIFY_SETTINGS_OVERRIDEABLE_BY_RESTORE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_SETTINGS_OVERRIDEABLE_BY_RESTORE", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.MODIFY_THEME_OVERLAY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MODIFY_THEME_OVERLAY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MONITOR_DEFAULT_SMS_PACKAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MONITOR_DEFAULT_SMS_PACKAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MONITOR_DEVICE_CONFIG_ACCESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MONITOR_DEVICE_CONFIG_ACCESS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.MONITOR_INPUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MONITOR_INPUT", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.MOUNT_FORMAT_FILESYSTEMS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MOUNT_FORMAT_FILESYSTEMS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MOUNT_UNMOUNT_FILESYSTEMS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MOUNT_UNMOUNT_FILESYSTEMS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.MOVE_PACKAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.MOVE_PACKAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NETWORK_AIRPLANE_MODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_AIRPLANE_MODE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NETWORK_BYPASS_PRIVATE_DNS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_BYPASS_PRIVATE_DNS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NETWORK_CARRIER_PROVISIONING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_CARRIER_PROVISIONING", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NETWORK_FACTORY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_FACTORY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NETWORK_MANAGED_PROVISIONING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_MANAGED_PROVISIONING", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NETWORK_SCAN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_SCAN", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NETWORK_SETTINGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_SETTINGS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NETWORK_SETUP_WIZARD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_SETUP_WIZARD", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.NETWORK_SIGNAL_STRENGTH_WAKEUP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_SIGNAL_STRENGTH_WAKEUP", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NETWORK_STACK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_STACK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NETWORK_STATS_PROVIDER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NETWORK_STATS_PROVIDER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NET_ADMIN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NET_ADMIN", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NET_TUNNELING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NET_TUNNELING", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.NFC": {"description": "Allows the app to communicate\n      with Near Field Communication (NFC) tags, cards, and readers.", "description_ptr": "permdesc_nfc", "label": "control Near Field Communication", "label_ptr": "permlab_nfc", "name": "android.permission.NFC", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.NFC_HANDOVER_STATUS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NFC_HANDOVER_STATUS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NFC_PREFERRED_PAYMENT_INFO": {"description": "Allows the app to get preferred nfc payment service information like\n      registered aids and route destination.", "description_ptr": "permdesc_preferredPaymentInfo", "label": "Preferred NFC Payment Service Information", "label_ptr": "permlab_preferredPaymentInfo", "name": "android.permission.NFC_PREFERRED_PAYMENT_INFO", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.NFC_SET_CONTROLLER_ALWAYS_ON": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NFC_SET_CONTROLLER_ALWAYS_ON", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NFC_TRANSACTION_EVENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NFC_TRANSACTION_EVENT", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.NOTIFICATION_DURING_SETUP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NOTIFICATION_DURING_SETUP", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NOTIFY_PENDING_SYSTEM_UPDATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NOTIFY_PENDING_SYSTEM_UPDATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.NOTIFY_TV_INPUTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.NOTIFY_TV_INPUTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.OBSERVE_APP_USAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OBSERVE_APP_USAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.OBSERVE_GRANT_REVOKE_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OBSERVE_GRANT_REVOKE_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.OBSERVE_NETWORK_POLICY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OBSERVE_NETWORK_POLICY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.OBSERVE_ROLE_HOLDERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OBSERVE_ROLE_HOLDERS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.OBSERVE_SENSOR_PRIVACY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OBSERVE_SENSOR_PRIVACY", "permissionGroup": "", "protectionLevel": "internal|role|installer"}, "android.permission.OEM_UNLOCK_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OEM_UNLOCK_STATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.OPEN_ACCESSIBILITY_DETAILS_SETTINGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OPEN_ACCESSIBILITY_DETAILS_SETTINGS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.OVERRIDE_COMPAT_CHANGE_CONFIG": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OVERRIDE_COMPAT_CHANGE_CONFIG", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.OVERRIDE_COMPAT_CHANGE_CONFIG_ON_RELEASE_BUILD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OVERRIDE_COMPAT_CHANGE_CONFIG_ON_RELEASE_BUILD", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.OVERRIDE_DISPLAY_MODE_REQUESTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OVERRIDE_DISPLAY_MODE_REQUESTS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.OVERRIDE_WIFI_CONFIG": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.OVERRIDE_WIFI_CONFIG", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.PACKAGE_ROLLBACK_AGENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PACKAGE_ROLLBACK_AGENT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.PACKAGE_USAGE_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PACKAGE_USAGE_STATS", "permissionGroup": "", "protectionLevel": "signature|privileged|development|appop|retailDemo"}, "android.permission.PACKAGE_VERIFICATION_AGENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PACKAGE_VERIFICATION_AGENT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.PACKET_KEEPALIVE_OFFLOAD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PACKET_KEEPALIVE_OFFLOAD", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.PEEK_DROPBOX_DATA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PEEK_DROPBOX_DATA", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.PEERS_MAC_ADDRESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PEERS_MAC_ADDRESS", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.PERFORM_CDMA_PROVISIONING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PERFORM_CDMA_PROVISIONING", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.PERFORM_IMS_SINGLE_REGISTRATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PERFORM_IMS_SINGLE_REGISTRATION", "permissionGroup": "", "protectionLevel": "internal|role"}, "android.permission.PERFORM_SIM_ACTIVATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PERFORM_SIM_ACTIVATION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.PERSISTENT_ACTIVITY": {"description": "Allows the app to make parts of itself persistent in memory.  This can limit memory available to other apps slowing down the phone.", "description_ptr": "permdesc_persistentActivity", "label": "make app always run", "label_ptr": "permlab_persistentActivity", "name": "android.permission.PERSISTENT_ACTIVITY", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.POWER_SAVER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.POWER_SAVER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.PROCESS_OUTGOING_CALLS": {"description": "Allows the app to see the\n        number being dialed during an outgoing call with the option to redirect\n        the call to a different number or abort the call altogether.", "description_ptr": "permdesc_processOutgoingCalls", "label": "reroute outgoing calls", "label_ptr": "permlab_processOutgoingCalls", "name": "android.permission.PROCESS_OUTGOING_CALLS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.PROVIDE_RESOLVER_RANKER_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PROVIDE_RESOLVER_RANKER_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.PROVIDE_TRUST_AGENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.PROVIDE_TRUST_AGENT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.QUERY_ALL_PACKAGES": {"description": "Allows an app to see all installed packages.", "description_ptr": "permdesc_queryAllPackages", "label": "query all packages", "label_ptr": "permlab_queryAllPackages", "name": "android.permission.QUERY_ALL_PACKAGES", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.QUERY_AUDIO_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.QUERY_AUDIO_STATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.QUERY_DO_NOT_ASK_CREDENTIALS_ON_BOOT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.QUERY_DO_NOT_ASK_CREDENTIALS_ON_BOOT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.QUERY_TIME_ZONE_RULES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.QUERY_TIME_ZONE_RULES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RADIO_SCAN_WITHOUT_LOCATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RADIO_SCAN_WITHOUT_LOCATION", "permissionGroup": "", "protectionLevel": "signature|companion"}, "android.permission.READ_ACTIVE_EMERGENCY_SESSION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_ACTIVE_EMERGENCY_SESSION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_BLOCKED_NUMBERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_BLOCKED_NUMBERS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_CALENDAR": {"description": "This app can read all calendar events stored on your phone and share or save your calendar data.", "description_ptr": "permdesc_readCalendar", "label": "Read calendar events and details", "label_ptr": "permlab_readCalendar", "name": "android.permission.READ_CALENDAR", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.READ_CALL_LOG": {"description": "This app can read your call history.", "description_ptr": "permdesc_readCallLog", "label": "read call log", "label_ptr": "permlab_readCallLog", "name": "android.permission.READ_CALL_LOG", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.READ_CARRIER_APP_INFO": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_CARRIER_APP_INFO", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_CELL_BROADCASTS": {"description": "Allows the app to read\n        cell broadcast messages received by your device. Cell broadcast alerts\n        are delivered in some locations to warn you of emergency situations.\n        Malicious apps may interfere with the performance or operation of your\n        device when an emergency cell broadcast is received.", "description_ptr": "permdesc_readCellBroadcasts", "label": "read cell broadcast messages", "label_ptr": "permlab_readCellBroadcasts", "name": "android.permission.READ_CELL_BROADCASTS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.READ_CLIPBOARD_IN_BACKGROUND": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_CLIPBOARD_IN_BACKGROUND", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_COMPAT_CHANGE_CONFIG": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_COMPAT_CHANGE_CONFIG", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_CONTACTS": {"description": "Allows the app to read data about your contacts stored on your phone.\n      Apps will also have access to the accounts on your phone that have created contacts.\n      This may include accounts created by apps you have installed.\n      This permission allows apps to save your contact data, and malicious apps may share contact data without your knowledge.", "description_ptr": "permdesc_readContacts", "label": "read your contacts", "label_ptr": "permlab_readContacts", "name": "android.permission.READ_CONTACTS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.READ_CONTENT_RATING_SYSTEMS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_CONTENT_RATING_SYSTEMS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_DEVICE_CONFIG": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_DEVICE_CONFIG", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.READ_DREAM_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_DREAM_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_DREAM_SUPPRESSION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_DREAM_SUPPRESSION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_EXTERNAL_STORAGE": {"description": "Allows the app to read the contents of your shared storage.", "description_ptr": "permdesc_sdcardRead", "label": "read the contents of your shared storage", "label_ptr": "permlab_sdcardRead", "name": "android.permission.READ_EXTERNAL_STORAGE", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.READ_FRAME_BUFFER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_FRAME_BUFFER", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.READ_GLOBAL_APP_SEARCH_DATA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_GLOBAL_APP_SEARCH_DATA", "permissionGroup": "", "protectionLevel": "internal|role"}, "android.permission.READ_INPUT_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_INPUT_STATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_INSTALL_SESSIONS": {"description": "Allows an application to read install sessions. This allows it to see details about active package installations.", "description_ptr": "permdesc_readInstallSessions", "label": "read install sessions", "label_ptr": "permlab_readInstallSessions", "name": "android.permission.READ_INSTALL_SESSIONS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.READ_LOGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_LOGS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.READ_LOWPAN_CREDENTIAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_LOWPAN_CREDENTIAL", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_NEARBY_STREAMING_POLICY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_NEARBY_STREAMING_POLICY", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_NETWORK_USAGE_HISTORY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_NETWORK_USAGE_HISTORY", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_OEM_UNLOCK_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_OEM_UNLOCK_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_PEOPLE_DATA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PEOPLE_DATA", "permissionGroup": "", "protectionLevel": "signature|recents|role"}, "android.permission.READ_PHONE_NUMBERS": {"description": "Allows the app to access the phone numbers of the device.", "description_ptr": "permdesc_readPhoneNumbers", "label": "read phone numbers", "label_ptr": "permlab_readPhoneNumbers", "name": "android.permission.READ_PHONE_NUMBERS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|instant"}, "android.permission.READ_PHONE_STATE": {"description": "Allows the app to access the phone\n      features of the device.  This permission allows the app to determine the\n      phone number and device IDs, whether a call is active, and the remote number\n      connected by a call.", "description_ptr": "permdesc_readPhoneState", "label": "read phone status and identity", "label_ptr": "permlab_readPhoneState", "name": "android.permission.READ_PHONE_STATE", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.READ_PRECISE_PHONE_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PRECISE_PHONE_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_PRINT_SERVICES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PRINT_SERVICES", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.READ_PRINT_SERVICE_RECOMMENDATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PRINT_SERVICE_RECOMMENDATIONS", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.READ_PRIVILEGED_PHONE_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PRIVILEGED_PHONE_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_PROFILE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PROFILE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.READ_PROJECTION_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_PROJECTION_STATE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.READ_RUNTIME_PROFILES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_RUNTIME_PROFILES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_SEARCH_INDEXABLES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_SEARCH_INDEXABLES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_SMS": {"description": "This app can read all SMS (text) messages stored on your phone.", "description_ptr": "permdesc_readSms", "label": "read your text messages (SMS or MMS)", "label_ptr": "permlab_readSms", "name": "android.permission.READ_SMS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.READ_SOCIAL_STREAM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_SOCIAL_STREAM", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.READ_SYNC_SETTINGS": {"description": "Allows the app to read the sync settings for an account. For example, this can determine whether the People app is synced with an account.", "description_ptr": "permdesc_readSyncSettings", "label": "read sync settings", "label_ptr": "permlab_readSyncSettings", "name": "android.permission.READ_SYNC_SETTINGS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.READ_SYNC_STATS": {"description": "Allows an app to read the sync stats for an account, including the history of sync events and how much data is synced. ", "description_ptr": "permdesc_readSyncStats", "label": "read sync statistics", "label_ptr": "permlab_readSyncStats", "name": "android.permission.READ_SYNC_STATS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.READ_SYSTEM_UPDATE_INFO": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_SYSTEM_UPDATE_INFO", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_USER_DICTIONARY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_USER_DICTIONARY", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.READ_WALLPAPER_INTERNAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_WALLPAPER_INTERNAL", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.READ_WIFI_CREDENTIAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.READ_WIFI_CREDENTIAL", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REAL_GET_TASKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REAL_GET_TASKS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REBOOT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REBOOT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECEIVE_BLUETOOTH_MAP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_BLUETOOTH_MAP", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECEIVE_BOOT_COMPLETED": {"description": "Allows the app to\n        have itself started as soon as the system has finished booting.\n        This can make it take longer to start the phone and allow the\n        app to slow down the overall phone by always running.", "description_ptr": "permdesc_receiveBootCompleted", "label": "run at startup", "label_ptr": "permlab_receiveBootCompleted", "name": "android.permission.RECEIVE_BOOT_COMPLETED", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.RECEIVE_DATA_ACTIVITY_CHANGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_DATA_ACTIVITY_CHANGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECEIVE_DEVICE_CUSTOMIZATION_READY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_DEVICE_CUSTOMIZATION_READY", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.RECEIVE_EMERGENCY_BROADCAST": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_EMERGENCY_BROADCAST", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECEIVE_MEDIA_RESOURCE_USAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_MEDIA_RESOURCE_USAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECEIVE_MMS": {"description": "Allows the app to receive and process MMS\n      messages. This means the app could monitor or delete messages sent to your\n      device without showing them to you.", "description_ptr": "permdesc_receiveMms", "label": "receive text messages (MMS)", "label_ptr": "permlab_receiveMms", "name": "android.permission.RECEIVE_MMS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.RECEIVE_SMS": {"description": "Allows the app to receive and process SMS\n      messages. This means the app could monitor or delete messages sent to your\n      device without showing them to you.", "description_ptr": "permdesc_receiveSms", "label": "receive text messages (SMS)", "label_ptr": "permlab_receiveSms", "name": "android.permission.RECEIVE_SMS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.RECEIVE_STK_COMMANDS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_STK_COMMANDS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECEIVE_WAP_PUSH": {"description": "Allows the app to receive and process\n     WAP messages.  This permission includes the ability to monitor or delete\n     messages sent to you without showing them to you.", "description_ptr": "permdesc_receiveWapPush", "label": "receive text messages (WAP)", "label_ptr": "permlab_receiveWapPush", "name": "android.permission.RECEIVE_WAP_PUSH", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.RECEIVE_WIFI_CREDENTIAL_CHANGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECEIVE_WIFI_CREDENTIAL_CHANGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECORD_AUDIO": {"description": "This app can record audio using the microphone while the app is in use.", "description_ptr": "permdesc_recordAudio", "label": "record audio", "label_ptr": "permlab_recordAudio", "name": "android.permission.RECORD_AUDIO", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous|instant"}, "android.permission.RECORD_BACKGROUND_AUDIO": {"description": "This app can record audio using the microphone at any time.", "description_ptr": "permdesc_recordBackgroundAudio", "label": "record audio in the background", "label_ptr": "permlab_recordBackgroundAudio", "name": "android.permission.RECORD_BACKGROUND_AUDIO", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "internal|role"}, "android.permission.RECOVERY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECOVERY", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RECOVER_KEYSTORE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RECOVER_KEYSTORE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REGISTER_CALL_PROVIDER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REGISTER_CALL_PROVIDER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REGISTER_CONNECTION_MANAGER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REGISTER_CONNECTION_MANAGER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REGISTER_MEDIA_RESOURCE_OBSERVER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REGISTER_MEDIA_RESOURCE_OBSERVER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REGISTER_SIM_SUBSCRIPTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REGISTER_SIM_SUBSCRIPTION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REGISTER_STATS_PULL_ATOM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REGISTER_STATS_PULL_ATOM", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REGISTER_WINDOW_MANAGER_LISTENERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REGISTER_WINDOW_MANAGER_LISTENERS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.REMOTE_AUDIO_PLAYBACK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REMOTE_AUDIO_PLAYBACK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.REMOTE_DISPLAY_PROVIDER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REMOTE_DISPLAY_PROVIDER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REMOVE_DRM_CERTIFICATES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REMOVE_DRM_CERTIFICATES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REMOVE_TASKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REMOVE_TASKS", "permissionGroup": "", "protectionLevel": "signature|documenter|recents"}, "android.permission.RENOUNCE_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RENOUNCE_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REORDER_TASKS": {"description": "Allows the app to move tasks to the\n      foreground and background.  The app may do this without your input.", "description_ptr": "permdesc_reorderTasks", "label": "reorder running apps", "label_ptr": "permlab_reorderTasks", "name": "android.permission.REORDER_TASKS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_COMPANION_PROFILE_WATCH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REQUEST_COMPANION_PROFILE_WATCH", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_COMPANION_RUN_IN_BACKGROUND": {"description": "This app can run in the background. This may drain battery faster.", "description_ptr": "permdesc_runInBackground", "label": "run in the background", "label_ptr": "permlab_runInBackground", "name": "android.permission.REQUEST_COMPANION_RUN_IN_BACKGROUND", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REQUEST_COMPANION_START_FOREGROUND_SERVICES_FROM_BACKGROUND", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_COMPANION_USE_DATA_IN_BACKGROUND": {"description": "This app can use data in the background. This may increase data usage.", "description_ptr": "permdesc_useDataInBackground", "label": "use data in the background", "label_ptr": "permlab_useDataInBackground", "name": "android.permission.REQUEST_COMPANION_USE_DATA_IN_BACKGROUND", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_DELETE_PACKAGES": {"description": "Allows an application to request deletion of packages.", "description_ptr": "permdesc_requestDeletePackages", "label": "request delete packages", "label_ptr": "permlab_requestDeletePackages", "name": "android.permission.REQUEST_DELETE_PACKAGES", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS": {"description": "Allows an app to ask for permission to ignore battery optimizations for that app.", "description_ptr": "permdesc_requestIgnoreBatteryOptimizations", "label": "ask to ignore battery optimizations", "label_ptr": "permlab_requestIgnoreBatteryOptimizations", "name": "android.permission.REQUEST_IGNORE_BATTERY_OPTIMIZATIONS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_INCIDENT_REPORT_APPROVAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REQUEST_INCIDENT_REPORT_APPROVAL", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.REQUEST_INSTALL_PACKAGES": {"description": "Allows an application to request installation of packages.", "description_ptr": "permdesc_requestInstallPackages", "label": "request install packages", "label_ptr": "permlab_requestInstallPackages", "name": "android.permission.REQUEST_INSTALL_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|appop"}, "android.permission.REQUEST_NETWORK_SCORES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REQUEST_NETWORK_SCORES", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.REQUEST_NOTIFICATION_ASSISTANT_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REQUEST_NOTIFICATION_ASSISTANT_SERVICE", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.REQUEST_OBSERVE_COMPANION_DEVICE_PRESENCE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REQUEST_OBSERVE_COMPANION_DEVICE_PRESENCE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.REQUEST_PASSWORD_COMPLEXITY": {"description": "Allows the app to learn the screen\n        lock complexity level (high, medium, low or none), which indicates the possible range of\n        length and type of the screen lock. The app can also suggest to users that they update the\n        screen lock to a certain level but users can freely ignore and navigate away. Note that the\n        screen lock is not stored in plaintext so the app does not know the exact password.\n    ", "description_ptr": "permdesc_requestPasswordComplexity", "label": "request screen lock complexity", "label_ptr": "permlab_requestPasswordComplexity", "name": "android.permission.REQUEST_PASSWORD_COMPLEXITY", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.RESET_APP_ERRORS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RESET_APP_ERRORS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.RESET_FINGERPRINT_LOCKOUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RESET_FINGERPRINT_LOCKOUT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.RESET_PASSWORD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RESET_PASSWORD", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RESET_SHORTCUT_MANAGER_THROTTLING": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RESET_SHORTCUT_MANAGER_THROTTLING", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.RESTART_PACKAGES": {"description": "Allows the app to end\n      background processes of other apps.  This may cause other apps to stop\n      running.", "description_ptr": "permdesc_killBackgroundProcesses", "label": "close other apps", "label_ptr": "permlab_killBackgroundProcesses", "name": "android.permission.RESTART_PACKAGES", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.RESTART_WIFI_SUBSYSTEM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RESTART_WIFI_SUBSYSTEM", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RESTORE_RUNTIME_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RESTORE_RUNTIME_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.RESTRICTED_VR_ACCESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RESTRICTED_VR_ACCESS", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.RETRIEVE_WINDOW_CONTENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RETRIEVE_WINDOW_CONTENT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.RETRIEVE_WINDOW_TOKEN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.RETRIEVE_WINDOW_TOKEN", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.REVIEW_ACCESSIBILITY_SERVICES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REVIEW_ACCESSIBILITY_SERVICES", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.REVOKE_RUNTIME_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.REVOKE_RUNTIME_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|installer|verifier"}, "android.permission.ROTATE_SURFACE_FLINGER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.ROTATE_SURFACE_FLINGER", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.RUN_IN_BACKGROUND": {"description": "This app can run in the background. This may drain battery faster.", "description_ptr": "permdesc_runInBackground", "label": "run in the background", "label_ptr": "permlab_runInBackground", "name": "android.permission.RUN_IN_BACKGROUND", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SCHEDULE_EXACT_ALARM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SCHEDULE_EXACT_ALARM", "permissionGroup": "", "protectionLevel": "normal|appop"}, "android.permission.SCHEDULE_PRIORITIZED_ALARM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SCHEDULE_PRIORITIZED_ALARM", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SCORE_NETWORKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SCORE_NETWORKS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SECURE_ELEMENT_PRIVILEGED_OPERATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SECURE_ELEMENT_PRIVILEGED_OPERATION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SEND_CATEGORY_CAR_NOTIFICATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SEND_CATEGORY_CAR_NOTIFICATIONS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SEND_DEVICE_CUSTOMIZATION_READY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SEND_DEVICE_CUSTOMIZATION_READY", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SEND_EMBMS_INTENTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SEND_EMBMS_INTENTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SEND_RESPOND_VIA_MESSAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SEND_RESPOND_VIA_MESSAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SEND_SHOW_SUSPENDED_APP_DETAILS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SEND_SHOW_SUSPENDED_APP_DETAILS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SEND_SMS": {"description": "Allows the app to send SMS messages.\n     This may result in unexpected charges. Malicious apps may cost you money by\n     sending messages without your confirmation.", "description_ptr": "permdesc_sendSms", "label": "send and view SMS messages", "label_ptr": "permlab_sendSms", "name": "android.permission.SEND_SMS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.SEND_SMS_NO_CONFIRMATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SEND_SMS_NO_CONFIRMATION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SERIAL_PORT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SERIAL_PORT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SET_ACTIVITY_WATCHER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_ACTIVITY_WATCHER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_ALWAYS_FINISH": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_ALWAYS_FINISH", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.SET_AND_VERIFY_LOCKSCREEN_CREDENTIALS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_AND_VERIFY_LOCKSCREEN_CREDENTIALS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_ANIMATION_SCALE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_ANIMATION_SCALE", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.SET_CLIP_SOURCE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_CLIP_SOURCE", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.SET_DEBUG_APP": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_DEBUG_APP", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.SET_DISPLAY_OFFSET": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_DISPLAY_OFFSET", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SET_HARMFUL_APP_WARNINGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_HARMFUL_APP_WARNINGS", "permissionGroup": "", "protectionLevel": "signature|verifier"}, "android.permission.SET_INITIAL_LOCK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_INITIAL_LOCK", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.SET_INPUT_CALIBRATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_INPUT_CALIBRATION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_KEYBOARD_LAYOUT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_KEYBOARD_LAYOUT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_MEDIA_KEY_LISTENER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_MEDIA_KEY_LISTENER", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.SET_ORIENTATION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_ORIENTATION", "permissionGroup": "", "protectionLevel": "signature|recents"}, "android.permission.SET_POINTER_SPEED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_POINTER_SPEED", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_PREFERRED_APPLICATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_PREFERRED_APPLICATIONS", "permissionGroup": "", "protectionLevel": "signature|installer|verifier"}, "android.permission.SET_PROCESS_LIMIT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_PROCESS_LIMIT", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.SET_SCREEN_COMPATIBILITY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_SCREEN_COMPATIBILITY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SET_TIME": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_TIME", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SET_TIME_ZONE": {"description": "Allows the app to change the phone's time zone.", "description_ptr": "permdesc_setTimeZone", "label": "set time zone", "label_ptr": "permlab_setTimeZone", "name": "android.permission.SET_TIME_ZONE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SET_VOLUME_KEY_LONG_PRESS_LISTENER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_VOLUME_KEY_LONG_PRESS_LISTENER", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.SET_WALLPAPER": {"description": "Allows the app to set the system wallpaper.", "description_ptr": "permdesc_setWallpaper", "label": "set wallpaper", "label_ptr": "permlab_setWallpaper", "name": "android.permission.SET_WALLPAPER", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.SET_WALLPAPER_COMPONENT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SET_WALLPAPER_COMPONENT", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SET_WALLPAPER_HINTS": {"description": "Allows the app to set the system wallpaper size hints.", "description_ptr": "permdesc_setWallpaperHints", "label": "adjust your wallpaper size", "label_ptr": "permlab_setWallpaperHints", "name": "android.permission.SET_WALLPAPER_HINTS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.SHOW_KEYGUARD_MESSAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SHOW_KEYGUARD_MESSAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SHUTDOWN": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SHUTDOWN", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SIGNAL_PERSISTENT_PROCESSES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SIGNAL_PERSISTENT_PROCESSES", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.SIGNAL_REBOOT_READINESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SIGNAL_REBOOT_READINESS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SMS_FINANCIAL_TRANSACTIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SMS_FINANCIAL_TRANSACTIONS", "permissionGroup": "", "protectionLevel": "signature|appop"}, "android.permission.SOUNDTRIGGER_DELEGATE_IDENTITY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SOUNDTRIGGER_DELEGATE_IDENTITY", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SOUND_TRIGGER_RUN_IN_BATTERY_SAVER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SOUND_TRIGGER_RUN_IN_BATTERY_SAVER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.START_ACTIVITIES_FROM_BACKGROUND": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.START_ACTIVITIES_FROM_BACKGROUND", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged|oem|verifier"}, "android.permission.START_ACTIVITY_AS_CALLER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.START_ACTIVITY_AS_CALLER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.START_ANY_ACTIVITY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.START_ANY_ACTIVITY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.START_FOREGROUND_SERVICES_FROM_BACKGROUND", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged|oem|verifier|role"}, "android.permission.START_TASKS_FROM_RECENTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.START_TASKS_FROM_RECENTS", "permissionGroup": "", "protectionLevel": "signature|privileged|recents"}, "android.permission.START_VIEW_PERMISSION_USAGE": {"description": "Allows the holder to start the permission usage for an app. Should never be needed for normal apps.", "description_ptr": "permdesc_startViewPermissionUsage", "label": "start view permission usage", "label_ptr": "permlab_startViewPermissionUsage", "name": "android.permission.START_VIEW_PERMISSION_USAGE", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.STATSCOMPANION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.STATSCOMPANION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.STATUS_BAR": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.STATUS_BAR", "permissionGroup": "", "protectionLevel": "signature|privileged|recents"}, "android.permission.STATUS_BAR_SERVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.STATUS_BAR_SERVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.STOP_APP_SWITCHES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.STOP_APP_SWITCHES", "permissionGroup": "", "protectionLevel": "signature|privileged|recents"}, "android.permission.STORAGE_INTERNAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.STORAGE_INTERNAL", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SUBSCRIBED_FEEDS_READ": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUBSCRIBED_FEEDS_READ", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.SUBSCRIBED_FEEDS_WRITE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUBSCRIBED_FEEDS_WRITE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUBSTITUTE_NOTIFICATION_APP_NAME", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SUBSTITUTE_SHARE_TARGET_APP_NAME_AND_ICON": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUBSTITUTE_SHARE_TARGET_APP_NAME_AND_ICON", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SUGGEST_EXTERNAL_TIME": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUGGEST_EXTERNAL_TIME", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.SUGGEST_MANUAL_TIME_AND_ZONE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUGGEST_MANUAL_TIME_AND_ZONE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SUGGEST_TELEPHONY_TIME_AND_ZONE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUGGEST_TELEPHONY_TIME_AND_ZONE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.SUSPEND_APPS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SUSPEND_APPS", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.SYSTEM_ALERT_WINDOW": {"description": "This app can appear on top of other apps or other parts of the screen. This may interfere with normal app usage and change the way that other apps appear.", "description_ptr": "permdesc_systemAlertWindow", "label": "This app can appear on top of other apps", "label_ptr": "permlab_systemAlertWindow", "name": "android.permission.SYSTEM_ALERT_WINDOW", "permissionGroup": "", "protectionLevel": "signature|setup|appop|installer|pre23|development"}, "android.permission.SYSTEM_APPLICATION_OVERLAY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.SYSTEM_APPLICATION_OVERLAY", "permissionGroup": "", "protectionLevel": "signature|recents|role"}, "android.permission.SYSTEM_CAMERA": {"description": "This privileged or system app can take pictures and record videos using a system camera at any time. Requires the android.permission.CAMERA permission to be held by the app as well", "description_ptr": "permdesc_systemCamera", "label": "Allow an application or service access to system cameras to take pictures and videos", "label_ptr": "permlab_systemCamera", "name": "android.permission.SYSTEM_CAMERA", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "system|signature|role"}, "android.permission.TABLET_MODE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TABLET_MODE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TEMPORARY_ENABLE_ACCESSIBILITY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TEMPORARY_ENABLE_ACCESSIBILITY", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TEST_BIOMETRIC": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TEST_BIOMETRIC", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TEST_BLACKLISTED_PASSWORD": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TEST_BLACKLISTED_PASSWORD", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TEST_MANAGE_ROLLBACKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TEST_MANAGE_ROLLBACKS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TETHER_PRIVILEGED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TETHER_PRIVILEGED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.TOGGLE_AUTOMOTIVE_PROJECTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TOGGLE_AUTOMOTIVE_PROJECTION", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.TRANSMIT_IR": {"description": "Allows the app to use the phone's infrared transmitter.", "description_ptr": "permdesc_transmitIr", "label": "transmit infrared", "label_ptr": "permlab_transmitIr", "name": "android.permission.TRANSMIT_IR", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.TRIGGER_SHELL_BUGREPORT": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TRIGGER_SHELL_BUGREPORT", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TRIGGER_TIME_ZONE_RULES_CHECK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TRIGGER_TIME_ZONE_RULES_CHECK", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TRUST_LISTENER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TRUST_LISTENER", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.TUNER_RESOURCE_ACCESS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TUNER_RESOURCE_ACCESS", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged"}, "android.permission.TV_INPUT_HARDWARE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TV_INPUT_HARDWARE", "permissionGroup": "", "protectionLevel": "signature|privileged|vendorPrivileged"}, "android.permission.TV_VIRTUAL_REMOTE_CONTROLLER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.TV_VIRTUAL_REMOTE_CONTROLLER", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.UNLIMITED_SHORTCUTS_API_CALLS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UNLIMITED_SHORTCUTS_API_CALLS", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.UNLIMITED_TOASTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UNLIMITED_TOASTS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.UPDATE_APP_OPS_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_APP_OPS_STATS", "permissionGroup": "", "protectionLevel": "signature|privileged|installer|role"}, "android.permission.UPDATE_CONFIG": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_CONFIG", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.UPDATE_DEVICE_STATS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_DEVICE_STATS", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "android.permission.UPDATE_DOMAIN_VERIFICATION_USER_SELECTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_DOMAIN_VERIFICATION_USER_SELECTION", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.UPDATE_FONTS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_FONTS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.UPDATE_LOCK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_LOCK", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.UPDATE_LOCK_TASK_PACKAGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_LOCK_TASK_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.UPDATE_PACKAGES_WITHOUT_USER_ACTION": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_PACKAGES_WITHOUT_USER_ACTION", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.UPDATE_TIME_ZONE_RULES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPDATE_TIME_ZONE_RULES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.UPGRADE_RUNTIME_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UPGRADE_RUNTIME_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.USER_ACTIVITY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.USER_ACTIVITY", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.USE_BIOMETRIC": {"description": "Allows the app to use biometric hardware for authentication", "description_ptr": "permdesc_useBiometric", "label": "use biometric hardware", "label_ptr": "permlab_useBiometric", "name": "android.permission.USE_BIOMETRIC", "permissionGroup": "android.permission-group.SENSORS", "protectionLevel": "normal"}, "android.permission.USE_BIOMETRIC_INTERNAL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.USE_BIOMETRIC_INTERNAL", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.USE_COLORIZED_NOTIFICATIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.USE_COLORIZED_NOTIFICATIONS", "permissionGroup": "", "protectionLevel": "signature|setup"}, "android.permission.USE_CREDENTIALS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.USE_CREDENTIALS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.USE_DATA_IN_BACKGROUND": {"description": "This app can use data in the background. This may increase data usage.", "description_ptr": "permdesc_useDataInBackground", "label": "use data in the background", "label_ptr": "permlab_useDataInBackground", "name": "android.permission.USE_DATA_IN_BACKGROUND", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.USE_FINGERPRINT": {"description": "Allows the app to use fingerprint hardware for authentication", "description_ptr": "permdesc_useFingerprint", "label": "use fingerprint hardware", "label_ptr": "permlab_useFingerprint", "name": "android.permission.USE_FINGERPRINT", "permissionGroup": "android.permission-group.SENSORS", "protectionLevel": "normal"}, "android.permission.USE_FULL_SCREEN_INTENT": {"description": "Allows the app to display notifications as full screen activities on a locked device", "description_ptr": "permdesc_fullScreenIntent", "label": "display notifications as full screen activities on a locked device", "label_ptr": "permlab_fullScreenIntent", "name": "android.permission.USE_FULL_SCREEN_INTENT", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.USE_ICC_AUTH_WITH_DEVICE_IDENTIFIER", "permissionGroup": "", "protectionLevel": "signature|appop"}, "android.permission.USE_RESERVED_DISK": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.USE_RESERVED_DISK", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.USE_SIP": {"description": "Allows the app to make and receive SIP calls.", "description_ptr": "permdesc_use_sip", "label": "make/receive SIP calls", "label_ptr": "permlab_use_sip", "name": "android.permission.USE_SIP", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.UWB_PRIVILEGED": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.UWB_PRIVILEGED", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.UWB_RANGING": {"description": "Allow the app to determine relative position between nearby Ultra-Wideband devices", "description_ptr": "permdesc_uwb_ranging", "label": "determine relative position between nearby Ultra-Wideband devices", "label_ptr": "permlab_uwb_ranging", "name": "android.permission.UWB_RANGING", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.VIBRATE": {"description": "Allows the app to control the vibrator.", "description_ptr": "permdesc_vibrate", "label": "control vibration", "label_ptr": "permlab_vibrate", "name": "android.permission.VIBRATE", "permissionGroup": "", "protectionLevel": "normal|instant"}, "android.permission.VIBRATE_ALWAYS_ON": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.VIBRATE_ALWAYS_ON", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.VIEW_INSTANT_APPS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.VIEW_INSTANT_APPS", "permissionGroup": "", "protectionLevel": "signature|preinstalled"}, "android.permission.VIRTUAL_INPUT_DEVICE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.VIRTUAL_INPUT_DEVICE", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.WAKE_LOCK": {"description": "Allows the app to prevent the phone from going to sleep.", "description_ptr": "permdesc_wakeLock", "label": "prevent phone from sleeping", "label_ptr": "permlab_wakeLock", "name": "android.permission.WAKE_LOCK", "permissionGroup": "", "protectionLevel": "normal|instant"}, "android.permission.WATCH_APPOPS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WATCH_APPOPS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WHITELIST_AUTO_REVOKE_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WHITELIST_AUTO_REVOKE_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.WHITELIST_RESTRICTED_PERMISSIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WHITELIST_RESTRICTED_PERMISSIONS", "permissionGroup": "", "protectionLevel": "signature|installer"}, "android.permission.WIFI_ACCESS_COEX_UNSAFE_CHANNELS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WIFI_ACCESS_COEX_UNSAFE_CHANNELS", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.WIFI_SET_DEVICE_MOBILITY_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WIFI_SET_DEVICE_MOBILITY_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WIFI_UPDATE_COEX_UNSAFE_CHANNELS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WIFI_UPDATE_COEX_UNSAFE_CHANNELS", "permissionGroup": "", "protectionLevel": "signature|role"}, "android.permission.WIFI_UPDATE_USABILITY_STATS_SCORE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WIFI_UPDATE_USABILITY_STATS_SCORE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WRITE_APN_SETTINGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_APN_SETTINGS", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WRITE_BLOCKED_NUMBERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_BLOCKED_NUMBERS", "permissionGroup": "", "protectionLevel": "signature"}, "android.permission.WRITE_CALENDAR": {"description": "This app can add, remove, or change calendar events on your phone. This app can send messages that may appear to come from calendar owners, or change events without notifying their owners.", "description_ptr": "permdesc_writeCalendar", "label": "add or modify calendar events and send email to guests without owners' knowledge", "label_ptr": "permlab_writeCalendar", "name": "android.permission.WRITE_CALENDAR", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.WRITE_CALL_LOG": {"description": "Allows the app to modify your phone's call log, including data about incoming and outgoing calls.\n        Malicious apps may use this to erase or modify your call log.", "description_ptr": "permdesc_writeCallLog", "label": "write call log", "label_ptr": "permlab_writeCallLog", "name": "android.permission.WRITE_CALL_LOG", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.WRITE_CONTACTS": {"description": "Allows the app to modify the data about your contacts stored on your phone.\n      This permission allows apps to delete contact data.", "description_ptr": "permdesc_writeContacts", "label": "modify your contacts", "label_ptr": "permlab_writeContacts", "name": "android.permission.WRITE_CONTACTS", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.WRITE_DEVICE_CONFIG": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_DEVICE_CONFIG", "permissionGroup": "", "protectionLevel": "signature|verifier|configurator"}, "android.permission.WRITE_DREAM_STATE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_DREAM_STATE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_EMBEDDED_SUBSCRIPTIONS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.WRITE_EXTERNAL_STORAGE": {"description": "Allows the app to write the contents of your shared storage.", "description_ptr": "permdesc_sdcardWrite", "label": "modify or delete the contents of your shared storage", "label_ptr": "permlab_sdcardWrite", "name": "android.permission.WRITE_EXTERNAL_STORAGE", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "android.permission.WRITE_GSERVICES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_GSERVICES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WRITE_MEDIA_STORAGE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_MEDIA_STORAGE", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WRITE_OBB": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_OBB", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WRITE_PROFILE": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_PROFILE", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.WRITE_SECURE_SETTINGS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_SECURE_SETTINGS", "permissionGroup": "", "protectionLevel": "signature|privileged|development"}, "android.permission.WRITE_SETTINGS": {"description": "Allows the app to modify the\n        system's settings data. Malicious apps may corrupt your system's\n        configuration.", "description_ptr": "permdesc_writeSettings", "label": "modify system settings", "label_ptr": "permlab_writeSettings", "name": "android.permission.WRITE_SETTINGS", "permissionGroup": "", "protectionLevel": "signature|preinstalled|appop|pre23"}, "android.permission.WRITE_SETTINGS_HOMEPAGE_DATA": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_SETTINGS_HOMEPAGE_DATA", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "android.permission.WRITE_SMS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_SMS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.WRITE_SOCIAL_STREAM": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_SOCIAL_STREAM", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.WRITE_SYNC_SETTINGS": {"description": "Allows an app to modify the sync settings for an account.  For example, this can be used to enable sync of the People app with an account.", "description_ptr": "permdesc_writeSyncSettings", "label": "toggle sync on and off", "label_ptr": "permlab_writeSyncSettings", "name": "android.permission.WRITE_SYNC_SETTINGS", "permissionGroup": "", "protectionLevel": "normal"}, "android.permission.WRITE_USER_DICTIONARY": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "android.permission.WRITE_USER_DICTIONARY", "permissionGroup": "", "protectionLevel": "normal"}, "com.android.alarm.permission.SET_ALARM": {"description": "Allows the app to set an alarm in\n        an installed alarm clock app. Some alarm clock apps may\n        not implement this feature.", "description_ptr": "permdesc_setAlarm", "label": "set an alarm", "label_ptr": "permlab_setAlarm", "name": "com.android.alarm.permission.SET_ALARM", "permissionGroup": "", "protectionLevel": "normal"}, "com.android.browser.permission.READ_HISTORY_BOOKMARKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "com.android.browser.permission.READ_HISTORY_BOOKMARKS", "permissionGroup": "", "protectionLevel": "normal"}, "com.android.browser.permission.WRITE_HISTORY_BOOKMARKS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "com.android.browser.permission.WRITE_HISTORY_BOOKMARKS", "permissionGroup": "", "protectionLevel": "normal"}, "com.android.launcher.permission.INSTALL_SHORTCUT": {"description": "Allows an application to add\n        Homescreen shortcuts without user intervention.", "description_ptr": "permdesc_install_shortcut", "label": "install shortcuts", "label_ptr": "permlab_install_shortcut", "name": "com.android.launcher.permission.INSTALL_SHORTCUT", "permissionGroup": "", "protectionLevel": "normal"}, "com.android.launcher.permission.UNINSTALL_SHORTCUT": {"description": "Allows the application to remove\n        Homescreen shortcuts without user intervention.", "description_ptr": "permdesc_uninstall_shortcut", "label": "uninstall shortcuts", "label_ptr": "permlab_uninstall_shortcut", "name": "com.android.launcher.permission.UNINSTALL_SHORTCUT", "permissionGroup": "", "protectionLevel": "normal"}, "com.android.permission.INSTALL_EXISTING_PACKAGES": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "com.android.permission.INSTALL_EXISTING_PACKAGES", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "com.android.permission.USE_INSTALLER_V2": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "com.android.permission.USE_INSTALLER_V2", "permissionGroup": "", "protectionLevel": "signature|privileged"}, "com.android.permission.USE_SYSTEM_DATA_LOADERS": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "com.android.permission.USE_SYSTEM_DATA_LOADERS", "permissionGroup": "", "protectionLevel": "signature"}, "com.android.voicemail.permission.ADD_VOICEMAIL": {"description": "Allows the app to add messages\n      to your voicemail inbox.", "description_ptr": "permdesc_addVoicemail", "label": "add voicemail", "label_ptr": "permlab_addVoicemail", "name": "com.android.voicemail.permission.ADD_VOICEMAIL", "permissionGroup": "android.permission-group.UNDEFINED", "protectionLevel": "dangerous"}, "com.android.voicemail.permission.READ_VOICEMAIL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "com.android.voicemail.permission.READ_VOICEMAIL", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}, "com.android.voicemail.permission.WRITE_VOICEMAIL": {"description": "", "description_ptr": "", "label": "", "label_ptr": "", "name": "com.android.voicemail.permission.WRITE_VOICEMAIL", "permissionGroup": "", "protectionLevel": "signature|privileged|role"}}}
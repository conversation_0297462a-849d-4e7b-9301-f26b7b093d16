/* --------------------------------- ABOUT -------------------------------------

SECURE OBFUSCATION IMPLEMENTATION
Enhanced version with advanced encryption and security features

Original Author: <PERSON> (base implementation)
Enhanced by: AmpedGems Team
Security Level: HIGH

Obfuscate
Guaranteed compile-time string literal obfuscation library for C++14+
Enhanced with multi-layer encryption and build-time entropy

Usage:
Pass string literals into the AY_OBFUSCATE macro to obfuscate them at compile
time. AY_OBFUSCATE returns a reference to an ay::obfuscated_data object with the
following enhanced traits:
	- Guaranteed obfuscation of string with advanced encryption
	- Multi-layer cipher: XOR + ADD operations with evolving keys
	- Build-time unique keys using multiple entropy sources
	- Enhanced memory protection with secure cleanup
	- Global lifetime with thread safety
	- Implicitly convertible to a char*

Example:
const char* obfuscated_string = AY_OBFUSCATE("Hello World");
std::cout << obfuscated_string << std::endl;

----------------------------------------------------------------------------- */

#pragma once
#include <cstddef>

#if __cplusplus >= 202002L
	#define AY_CONSTEVAL consteval
#else
	#define AY_CONSTEVAL constexpr
#endif

// Workaround for __LINE__ not being constexpr when /ZI (Edit and Continue) is enabled in Visual Studio
#ifdef _MSC_VER
	#define AY_CAT(X,Y) AY_CAT2(X,Y)
	#define AY_CAT2(X,Y) X##Y
	#define AY_LINE int(AY_CAT(__LINE__,U))
#else
	#define AY_LINE __LINE__
#endif

#ifndef AY_OBFUSCATE_DEFAULT_KEY
	// The default 64 bit key to obfuscate strings with.
	// Enhanced with secure key generation
	#define AY_OBFUSCATE_DEFAULT_KEY ay::generate_final_key(AY_LINE)
#endif

namespace ay
{
	using size_type = unsigned long long;
	using key_type = unsigned long long;

	// Enhanced security: Private secret salt unknown to attackers
	constexpr key_type SECRET_SALT = 0xC3E5A7B9D2F1804EULL;

	// libstdc++ has std::remove_cvref_t<T> since C++20, but because not every user will be
	// able or willing to link to the STL, we prefer to do this functionality ourselves here.
	template <typename T>
	struct remove_const_ref {
		using type = T;
	};

	template <typename T>
	struct remove_const_ref<T&> {
		using type = T;
	};

	template <typename T>
	struct remove_const_ref<const T> {
		using type = T;
	};

	template <typename T>
	struct remove_const_ref<const T&> {
		using type = T;
	};

	template <typename T>
	using char_type = typename remove_const_ref<T>::type;

	// Enhanced: FNV-1a hash algorithm for better entropy
	constexpr key_type hash_string(const char* str) {
		key_type hash = 0xCBF29CE484222325ULL; // FNV-1a 64-bit offset basis
		while (*str) {
			hash ^= static_cast<key_type>(*str++);
			hash *= 0x100000001B3ULL; // FNV-1a 64-bit prime
		}
		return hash;
	}

	// Enhanced: Build-time entropy from multiple sources
	constexpr key_type get_device_entropy() {
		return hash_string(__DATE__ __TIME__) ^
			   hash_string(__FILE__) ^
			   0xDEADBEEFCAFEBABEULL; // Hardware-derived constant
	}

	// Enhanced: Secure key generation with multiple entropy sources
	constexpr key_type generate_final_key(key_type line_seed) {
		// Multi-source entropy for unpredictable keys
		constexpr key_type build_key = get_device_entropy();
		constexpr key_type salted_key = build_key ^ SECRET_SALT;
		return salted_key + line_seed;
	}

	// Enhanced: Advanced encryption with XOR + ADD operations and evolving key
	constexpr void encrypt_cipher(char* data, size_type size, key_type key) {
		key_type temp_key = key;
		for (size_type i = 0; i < size; i++) {
			// XOR with rotating key
			data[i] ^= char(temp_key >> ((i % 8) * 8));
			// ADD with different key rotation
			data[i] += char(temp_key >> (((i + 4) % 8) * 8));
			// Evolve the key using Linear Congruential Generator
			temp_key = (temp_key * 48271ULL + 12345ULL);
		}
	}

	// Enhanced: Secure decryption (reverse of encryption)
	constexpr void decrypt_cipher(char* data, size_type size, key_type key) {
		key_type temp_key = key;
		for (size_type i = 0; i < size; i++) {
			// SUB with different key rotation (reverse of ADD)
			data[i] -= char(temp_key >> (((i + 4) % 8) * 8));
			// XOR with rotating key (reverse of XOR)
			data[i] ^= char(temp_key >> ((i % 8) * 8));
			// Use the same LCG formula to regenerate key sequence
			temp_key = (temp_key * 48271ULL + 12345ULL);
		}
	}

	// Legacy compatibility: Keep old function name but use new implementation
	template <typename CHAR_TYPE>
	constexpr void cipher(CHAR_TYPE* data, size_type size, key_type key)
	{
		// Use enhanced decryption for backward compatibility
		decrypt_cipher(reinterpret_cast<char*>(data), size, key);
	}

	// Enhanced: Secure compile-time obfuscator with advanced encryption
	template <size_type N, key_type KEY, typename CHAR_TYPE = char>
	class obfuscator
	{
	public:
		// Enhanced: Obfuscates the string with advanced encryption on construction
		AY_CONSTEVAL obfuscator(const CHAR_TYPE* data)
		{
			// Copy data
			for (size_type i = 0; i < N; i++)
			{
				m_data[i] = data[i];
			}

			// Enhanced: Use advanced encryption instead of simple XOR
			encrypt_cipher(reinterpret_cast<char*>(m_data), N, KEY);
		}

		constexpr const CHAR_TYPE* data() const
		{
			return &m_data[0];
		}

		AY_CONSTEVAL size_type size() const
		{
			return N;
		}

		AY_CONSTEVAL key_type key() const
		{
			return KEY;
		}

	private:
		CHAR_TYPE m_data[N]{};
	};

	// Enhanced: Secure runtime data handler with advanced memory protection
	template <size_type N, key_type KEY, typename CHAR_TYPE = char>
	class obfuscated_data
	{
	public:
		obfuscated_data(const obfuscator<N, KEY, CHAR_TYPE>& obfuscator)
		{
			// Copy obfuscated data
			for (size_type i = 0; i < N; i++)
			{
				m_data[i] = obfuscator.data()[i];
			}
			m_decrypted = false;
		}

		// Enhanced: Secure memory cleanup with volatile pointer
		~obfuscated_data()
		{
			// Enhanced: Secure memory wipe to prevent recovery
			volatile CHAR_TYPE* p = m_data;
			for (size_type i = 0; i < N; ++i) {
				p[i] = 0;
			}
		}

		// Enhanced: Returns decrypted string with secure decryption
		operator CHAR_TYPE* ()
		{
			decrypt();
			return m_data;
		}

		// Enhanced: Secure decryption using advanced cipher
		void decrypt()
		{
			if (!m_decrypted)
			{
				decrypt_cipher(reinterpret_cast<char*>(m_data), N, KEY);
				m_decrypted = true;
			}
		}

		// Enhanced: Manual re-encryption for additional security
		void encrypt()
		{
			if (m_decrypted)
			{
				encrypt_cipher(reinterpret_cast<char*>(m_data), N, KEY);
				m_decrypted = false;
			}
		}

		// Enhanced: Manual re-encryption method (alias for compatibility)
		void re_encrypt()
		{
			encrypt();
		}

		// Enhanced: Returns true if currently decrypted, false if encrypted
		bool is_encrypted() const
		{
			return !m_decrypted;
		}

	private:
		// Local storage for the string
		CHAR_TYPE m_data[N];
		// Enhanced: Track decryption state
		bool m_decrypted;
	};

	// Enhanced: Helper function to create secure obfuscator
	template <size_type N, key_type KEY = AY_OBFUSCATE_DEFAULT_KEY, typename CHAR_TYPE = char>
	AY_CONSTEVAL auto make_obfuscator(const CHAR_TYPE(&data)[N])
	{
		return obfuscator<N, KEY, CHAR_TYPE>(data);
	}

	// Legacy compatibility: Keep old generate_key function
	AY_CONSTEVAL key_type generate_key(key_type seed)
	{
		return generate_final_key(seed);
	}
}

// Enhanced: Secure obfuscation macro with backward compatibility
// Obfuscates the string 'data' at compile-time and returns a reference to a
// ay::obfuscated_data object with enhanced security features
#define AY_OBFUSCATE(data) AY_OBFUSCATE_KEY(data, AY_OBFUSCATE_DEFAULT_KEY)

// Enhanced: Secure obfuscation with custom key
// Uses advanced encryption with build-time entropy and evolving keys
#define AY_OBFUSCATE_KEY(data, key) \
	[]() -> ay::obfuscated_data<sizeof(data)/sizeof(data[0]), key, ay::char_type<decltype(*data)>>& { \
		static_assert(sizeof(decltype(key)) == sizeof(ay::key_type), "key must be a 64 bit unsigned integer"); \
		using char_type = ay::char_type<decltype(*data)>; \
		constexpr auto n = sizeof(data)/sizeof(data[0]); \
		constexpr auto obfuscator = ay::make_obfuscator<n, key, char_type>(data); \
		thread_local auto obfuscated_data = ay::obfuscated_data<n, key, char_type>(obfuscator); \
		return obfuscated_data; \
	}()

/* -------------------------------- LICENSE ------------------------------------

ENHANCED SECURE OBFUSCATION IMPLEMENTATION
Based on original work by Adam Yaxley (Public Domain)
Enhanced by AmpedGems Team with advanced security features

Original License: Public Domain (http://www.unlicense.org)
Enhancements: Proprietary security improvements

This enhanced version includes:
- Advanced multi-layer encryption (XOR + ADD with evolving keys)
- Build-time entropy from multiple sources
- Secure memory protection with volatile pointers
- Enhanced key generation with secret salt
- Linear Congruential Generator for key evolution
- FNV-1a hash algorithm for better entropy distribution

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS
FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS BE
LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF
CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE
SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.

----------------------------------------------------------------------------- */

{"Landroid/hardware/location/ActivityRecognitionHardware;-disableActivityEvent-(Ljava/lang/String; I)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-enableActivityEvent-(Ljava/lang/String; I J)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-flush-()Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-getSupportedActivities-()[Ljava/lang/String;": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-isActivitySupported-(Ljava/lang/String;)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-registerSink-(Landroid/hardware/location/IActivityRecognitionHardwareSink;)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ActivityRecognitionHardware;-unregisterSink-(Landroid/hardware/location/IActivityRecognitionHardwareSink;)Z": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-findNanoAppOnHub-(I Landroid/hardware/location/NanoAppFilter;)[I": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-getContextHubHandles-()[I": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-getContextHubInfo-(I)Landroid/hardware/location/ContextHubInfo;": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-getNanoAppInstanceInfo-(I)Landroid/hardware/location/NanoAppInstanceInfo;": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-loadNanoApp-(I Landroid/hardware/location/NanoApp;)I": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-registerCallback-(Landroid/hardware/location/IContextHubCallback;)I": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-sendMessage-(I I Landroid/hardware/location/ContextHubMessage;)I": ["android.permission.LOCATION_HARDWARE"], "Landroid/hardware/location/ContextHubService;-unloadNanoApp-(I)I": ["android.permission.LOCATION_HARDWARE"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dp/A2dpService$BluetoothA2dpBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-getAudioConfig-(Landroid/bluetooth/BluetoothDevice;)Landroid/bluetooth/BluetoothAudioConfig;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/a2dpsink/A2dpSinkService$BluetoothA2dpSinkBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getMetadata-(Landroid/bluetooth/BluetoothDevice;)Landroid/media/MediaMetadata;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getPlaybackState-(Landroid/bluetooth/BluetoothDevice;)Landroid/media/session/PlaybackState;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-getPlayerSettings-(Landroid/bluetooth/BluetoothDevice;)Landroid/bluetooth/BluetoothAvrcpPlayerSettings;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-sendGroupNavigationCmd-(Landroid/bluetooth/BluetoothDevice; I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-sendPassThroughCmd-(Landroid/bluetooth/BluetoothDevice; I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/avrcp/AvrcpControllerService$BluetoothAvrcpControllerBinder;-setPlayerApplicationSetting-(Landroid/bluetooth/BluetoothAvrcpPlayerSettings;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-cancelBondProcess-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-configHciSnoopLog-(Z)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-connectSocket-(Landroid/bluetooth/BluetoothDevice; I Landroid/os/ParcelUuid; I I)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-createBond-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-createBondOutOfBand-(Landroid/bluetooth/BluetoothDevice; I Landroid/bluetooth/OobData;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-createSocketChannel-(I Ljava/lang/String; Landroid/os/ParcelUuid; I I)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-disable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-enable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-enableNoAutoConnect-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-factoryReset-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-fetchRemoteUuids-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getAdapterConnectionState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getBondState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getBondedDevices-()[Landroid/bluetooth/BluetoothDevice;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getDiscoverableTimeout-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getMessageAccessPermission-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getPhonebookAccessPermission-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteAlias-(Landroid/bluetooth/BluetoothDevice;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteClass-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteName-(Landroid/bluetooth/BluetoothDevice;)Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteType-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getRemoteUuids-(Landroid/bluetooth/BluetoothDevice;)[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getSimAccessPermission-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isActivityAndEnergyReportingSupported-()Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isMultiAdvertisementSupported-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isOffloadedFilteringSupported-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-isOffloadedScanBatchingSupported-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-removeBond-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-reportActivityInfo-()Landroid/bluetooth/BluetoothActivityEnergyInfo;": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-requestActivityInfo-(Landroid/os/ResultReceiver;)V": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-sdpSearch-(Landroid/bluetooth/BluetoothDevice; Landroid/os/ParcelUuid;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-sendConnectionStateChange-(Landroid/bluetooth/BluetoothDevice; I I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setDiscoverableTimeout-(I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setMessageAccessPermission-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPairingConfirmation-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPasskey-(Landroid/bluetooth/BluetoothDevice; Z I [B)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPhonebookAccessPermission-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setPin-(Landroid/bluetooth/BluetoothDevice; Z I [B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setRemoteAlias-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setScanMode-(I I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-setSimAccessPermission-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/btservice/AdapterService$AdapterServiceBinder;-startDiscovery-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addCharacteristic-(I Landroid/os/ParcelUuid; I I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addDescriptor-(I Landroid/os/ParcelUuid; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-addIncludedService-(I I I Landroid/os/ParcelUuid;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-beginReliableWrite-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-beginServiceDeclaration-(I I I I Landroid/os/ParcelUuid; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clearServices-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clientConnect-(I Ljava/lang/String; Z I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-clientDisconnect-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-configureMTU-(I Ljava/lang/String; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-connectionParameterUpdate-(I Ljava/lang/String; I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-disconnectAll-()V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-discoverServices-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-endReliableWrite-(I Ljava/lang/String; Z)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-endServiceDeclaration-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-numHwTrackFiltersAvailable-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readCharacteristic-(I Ljava/lang/String; I I)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readDescriptor-(I Ljava/lang/String; I I)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-readRemoteRssi-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-refreshDevice-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerClient-(Landroid/os/ParcelUuid; Landroid/bluetooth/IBluetoothGattCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerForNotification-(I Ljava/lang/String; I Z)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-registerServer-(Landroid/os/ParcelUuid; Landroid/bluetooth/IBluetoothGattServerCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-removeService-(I I I Landroid/os/ParcelUuid;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-sendNotification-(I Ljava/lang/String; I I Landroid/os/ParcelUuid; I Landroid/os/ParcelUuid; Z [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-sendResponse-(I Ljava/lang/String; I I I [B)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-serverConnect-(I Ljava/lang/String; Z I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-serverDisconnect-(I Ljava/lang/String;)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-startMultiAdvertising-(I Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseSettings;)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-startScan-(I Z Landroid/bluetooth/le/ScanSettings; Ljava/util/List; Landroid/os/WorkSource; Ljava/util/List; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.BLUETOOTH_ADMIN", "android.permission.BLUETOOTH_PRIVILEGED", "android.permission.PEERS_MAC_ADDRESS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-stopMultiAdvertising-(I)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-stopScan-(I Z)V": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-unregAll-()V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-unregisterClient-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-unregisterServer-(I)V": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-writeCharacteristic-(I Ljava/lang/String; I I I [B)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/gatt/GattService$BluetoothGattBinder;-writeDescriptor-(I Ljava/lang/String; I I I [B)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_PRIVILEGED"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-connectChannelToSink-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getConnectedHealthDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getHealthDeviceConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getHealthDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-registerAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration; Landroid/bluetooth/IBluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hdp/HealthService$BluetoothHealthBinder;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-bindResponse-(I Z)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-clccResponse-(I I I I Z Ljava/lang/String; I)V": ["android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_PHONE_STATE"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-connectAudio-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disableWBS-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-disconnectAudio-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-enableWBS-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-isAudioConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-isAudioOn-()Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-phoneStateChanged-(I I I Ljava/lang/String; I)V": ["android.permission.BLUETOOTH_ADMIN", "android.permission.MODIFY_PHONE_STATE"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-sendVendorSpecificResultCode-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfp/HeadsetService$BluetoothHeadsetBinder;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-acceptCall-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-connectAudio-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-dial-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-dialMemory-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-disconnectAudio-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-enterPrivateMode-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-explicitCallTransfer-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getCurrentAgEvents-(Landroid/bluetooth/BluetoothDevice;)Landroid/os/Bundle;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getCurrentAgFeatures-(Landroid/bluetooth/BluetoothDevice;)Landroid/os/Bundle;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getCurrentCalls-(Landroid/bluetooth/BluetoothDevice;)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getLastVoiceTagNumber-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-holdCall-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-redial-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-rejectCall-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-sendDTMF-(Landroid/bluetooth/BluetoothDevice; B)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hfpclient/HeadsetClientService$BluetoothHeadsetClientBinder;-terminateCall-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getProtocolMode-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-getReport-(Landroid/bluetooth/BluetoothDevice; B B I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-sendData-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setProtocolMode-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-setReport-(Landroid/bluetooth/BluetoothDevice; B Ljava/lang/String;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/hid/HidService$BluetoothInputDeviceBinder;-virtualUnplug-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getClient-()Landroid/bluetooth/BluetoothDevice;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-getState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-isConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/map/BluetoothMapService$BluetoothMapBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pan/PanService$BluetoothPanBinder;-setBluetoothTethering-(Z)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN", "android.permission.TETHER_PRIVILEGED"], "Lcom/android/bluetooth/pbapclient/PbapClientService$BluetoothPbapClientBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/pbapclient/PbapClientService$BluetoothPbapClientBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/pbapclient/PbapClientService$BluetoothPbapClientBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pbapclient/PbapClientService$BluetoothPbapClientBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pbapclient/PbapClientService$BluetoothPbapClientBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/pbapclient/PbapClientService$BluetoothPbapClientBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/pbapclient/PbapClientService$BluetoothPbapClientBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-connect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-disconnect-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-getClient-()Landroid/bluetooth/BluetoothDevice;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-getPriority-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-getState-()I": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-isConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Lcom/android/bluetooth/sap/SapService$SapBinder;-setPriority-(Landroid/bluetooth/BluetoothDevice; I)Z": ["android.permission.BLUETOOTH"], "Lcom/android/car/CarRadioService;-setPreset-(Landroid/car/hardware/radio/CarRadioPreset;)Z": ["android.car.permission.CAR_RADIO"], "Lcom/android/car/ICarImpl;-getCarService-(Ljava/lang/String;)Landroid/os/IBinder;": ["android.car.permission.CAR_CAMERA", "android.car.permission.CAR_HVAC", "android.car.permission.CAR_MOCK_VEHICLE_HAL", "android.car.permission.CAR_NAVIGATION_MANAGER", "android.car.permission.CAR_PROJECTION", "android.car.permission.CAR_RADIO"], "Lcom/android/car/pm/CarPackageManagerService;-setAppBlockingPolicy-(Ljava/lang/String; Landroid/car/content/pm/CarAppBlockingPolicy; I)V": ["android.car.permission.CONTROL_APP_BLOCKING"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getCompleteVoiceMailNumber-()Ljava/lang/String;": ["android.permission.CALL_PRIVILEGED"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getCompleteVoiceMailNumberForSubscriber-(I)Ljava/lang/String;": ["android.permission.CALL_PRIVILEGED"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getDeviceId-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getDeviceIdForPhone-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getDeviceSvn-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getDeviceSvnUsingSubId-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getGroupIdLevel1-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getGroupIdLevel1ForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIccSerialNumber-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIccSerialNumberForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIccSimChallengeResponse-(I I I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getImeiForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIsimChallengeResponse-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIsimDomain-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIsimImpi-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIsimImpu-()[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIsimIst-()Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getIsimPcscf-()[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getLine1AlphaTag-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getLine1AlphaTagForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getLine1Number-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getLine1NumberForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getMsisdn-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getMsisdnForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getNaiForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getSubscriberId-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getSubscriberIdForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getVoiceMailAlphaTag-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getVoiceMailAlphaTagForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getVoiceMailNumber-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/PhoneSubInfoController;-getVoiceMailNumberForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-addSubInfoRecord-(Ljava/lang/String; I)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-clearDefaultsForInactiveSubIds-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-clearSubInfo-()I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getActiveSubInfoCount-(Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getActiveSubscriptionInfo-(I Ljava/lang/String;)Landroid/telephony/SubscriptionInfo;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getActiveSubscriptionInfoForIccId-(Ljava/lang/String; Ljava/lang/String;)Landroid/telephony/SubscriptionInfo;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getActiveSubscriptionInfoForSimSlotIndex-(I Ljava/lang/String;)Landroid/telephony/SubscriptionInfo;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getActiveSubscriptionInfoList-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getAllSubInfoCount-(Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getAllSubInfoList-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-getSubscriptionProperty-(I Ljava/lang/String; Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDataRoaming-(I I)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDefaultDataSubId-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDefaultSmsSubId-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDefaultVoiceSubId-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDisplayName-(Ljava/lang/String; I)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDisplayNameUsingSrc-(Ljava/lang/String; I J)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setDisplayNumber-(Ljava/lang/String; I)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setIconTint-(I I)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/SubscriptionController;-setSubscriptionProperty-(I Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/internal/telephony/UiccPhoneBookController;-getAdnRecordsInEf-(I)Ljava/util/List;": ["android.permission.READ_CONTACTS"], "Lcom/android/internal/telephony/UiccPhoneBookController;-getAdnRecordsInEfForSubscriber-(I I)Ljava/util/List;": ["android.permission.READ_CONTACTS"], "Lcom/android/internal/telephony/UiccPhoneBookController;-updateAdnRecordsInEfByIndex-(I Ljava/lang/String; Ljava/lang/String; I Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/UiccPhoneBookController;-updateAdnRecordsInEfByIndexForSubscriber-(I I Ljava/lang/String; Ljava/lang/String; I Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/UiccPhoneBookController;-updateAdnRecordsInEfBySearch-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/UiccPhoneBookController;-updateAdnRecordsInEfBySearchForSubscriber-(I I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.WRITE_CONTACTS"], "Lcom/android/internal/telephony/UiccSmsController;-copyMessageToIccEfForSubscriber-(I Ljava/lang/String; I [B [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-disableCellBroadcastForSubscriber-(I I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-disableCellBroadcastRangeForSubscriber-(I I I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-enableCellBroadcastForSubscriber-(I I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-enableCellBroadcastRangeForSubscriber-(I I I I)Z": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-getAllMessagesFromIccEfForSubscriber-(I Ljava/lang/String;)Ljava/util/List;": ["android.permission.RECEIVE_SMS"], "Lcom/android/internal/telephony/UiccSmsController;-sendDataForSubscriber-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; I [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendDataForSubscriberWithSelfPermissions-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; I [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendMultipartTextForSubscriber-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/util/List; Ljava/util/List; Ljava/util/List; Z)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendStoredMultipartText-(I Ljava/lang/String; Landroid/net/Uri; Ljava/lang/String; Ljava/util/List; Ljava/util/List;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendStoredText-(I Ljava/lang/String; Landroid/net/Uri; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendTextForSubscriber-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent; Z)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-sendTextForSubscriberWithSelfPermissions-(I Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent; Z)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS", "android.permission.SEND_SMS_NO_CONFIRMATION"], "Lcom/android/internal/telephony/UiccSmsController;-updateMessageOnIccEfForSubscriber-(I Ljava/lang/String; I I [B)Z": ["android.permission.RECEIVE_SMS", "android.permission.SEND_SMS"], "Lcom/android/phone/CarrierConfigLoader;-getConfigForSubId-(I)Landroid/os/PersistableBundle;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/CarrierConfigLoader;-notifyConfigChangedForSubId-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/CarrierConfigLoader;-updateConfigForPhoneId-(I Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-answerRingingCall-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-answerRingingCallForSubscriber-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-call-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-carrierActionSetMeteredApnsEnabled-(I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-carrierActionSetRadioEnabled-(I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-disableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-disableLocationUpdatesForSubscriber-(I)V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableDataConnectivity-()Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-enableLocationUpdates-()V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableLocationUpdatesForSubscriber-(I)V": ["android.permission.CONTROL_LOCATION_UPDATES"], "Lcom/android/phone/PhoneInterfaceManager;-enableVideoCalling-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-endCall-()Z": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-endCallForSubscriber-(I)Z": ["android.permission.CALL_PHONE"], "Lcom/android/phone/PhoneInterfaceManager;-factoryReset-(I)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getAidForAppType-(I I)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getAllCellInfo-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/phone/PhoneInterfaceManager;-getAllowedCarriers-(I)Ljava/util/List;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCalculatedPreferredNetworkType-(Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaEriIconIndex-(Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaEriIconIndexForSubscriber-(I Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaEriIconMode-(Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaEriIconModeForSubscriber-(I Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaEriText-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaEriTextForSubscriber-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaMdn-(I)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaMin-(I)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCdmaPrlVersion-(I)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getCellLocation-(Ljava/lang/String;)Landroid/os/Bundle;": ["android.permission.ACCESS_FINE_LOCATION", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/phone/PhoneInterfaceManager;-getCellNetworkScanResults-(I)Lcom/android/internal/telephony/CellNetworkScanResult;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getDataEnabled-(I)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getDataNetworkType-(Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getDataNetworkTypeForSubscriber-(I Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getDeviceId-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getDeviceSoftwareVersionForSlot-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getEsn-(I)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getImeiForSlot-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getLine1AlphaTagForDisplay-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getLine1NumberForDisplay-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getLteOnCdmaMode-(Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getLteOnCdmaModeForSubscriber-(I Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getMergedSubscriberIds-(Ljava/lang/String;)[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getNeighboringCellInfo-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/phone/PhoneInterfaceManager;-getNetworkTypeForSubscriber-(I Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getPcscfAddress-(Ljava/lang/String; Ljava/lang/String;)[Ljava/lang/String;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getPreferredNetworkType-(I)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getRadioAccessFamily-(I Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getServiceStateForSubscriber-(I Ljava/lang/String;)Landroid/telephony/ServiceState;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getSystemVisualVoicemailSmsFilterSettings-(Ljava/lang/String; I)Landroid/telephony/VisualVoicemailSmsFilterSettings;": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getTelephonyHistograms-()Ljava/util/List;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getTetherApnRequired-()I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getVoiceNetworkTypeForSubscriber-(I Ljava/lang/String;)I": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-getVtDataUsage-()J": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/phone/PhoneInterfaceManager;-handlePinMmi-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-handlePinMmiForSubscriber-(I Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccCloseLogicalChannel-(I I)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccExchangeSimIO-(I I I I I I Ljava/lang/String;)[B": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccOpenLogicalChannel-(I Ljava/lang/String;)Landroid/telephony/IccOpenLogicalChannelResponse;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccTransmitApduBasicChannel-(I I I I I I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-iccTransmitApduLogicalChannel-(I I I I I I I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-invokeOemRilRequestRaw-([B [B)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isIdle-(Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isIdleForSubscriber-(I Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isOffhook-(Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isOffhookForSubscriber-(I Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isRadioOn-(Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isRadioOnForSubscriber-(I Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isRinging-(Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isRingingForSubscriber-(I Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isVideoCallingEnabled-(Ljava/lang/String;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-isVisualVoicemailEnabled-(Ljava/lang/String; Landroid/telecom/PhoneAccountHandle;)Z": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvReadItem-(I)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvResetConfig-(I)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvWriteCdmaPrl-([B)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-nvWriteItem-(I Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-requestModemActivityInfo-(Landroid/os/ResultReceiver;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-sendEnvelopeWithStatus-(I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setAllowedCarriers-(I Ljava/util/List;)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setDataEnabled-(I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setImsRegistrationState-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setNetworkSelectionModeAutomatic-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setNetworkSelectionModeManual-(I Lcom/android/internal/telephony/OperatorInfo; Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setPolicyDataEnabled-(Z I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setPreferredNetworkType-(I I)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadio-(Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadioForSubscriber-(I Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setRadioPower-(Z)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-setVisualVoicemailEnabled-(Ljava/lang/String; Landroid/telecom/PhoneAccountHandle; Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-shutdownMobileRadios-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPin-(Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPinForSubscriber-(I Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPinReportResult-(Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPinReportResultForSubscriber-(I Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPuk-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPukForSubscriber-(I Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPukReportResult-(Ljava/lang/String; Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-supplyPukReportResultForSubscriber-(I Ljava/lang/String; Ljava/lang/String;)[I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-toggleRadioOnOff-()V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/phone/PhoneInterfaceManager;-toggleRadioOnOffForSubscriber-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/providers/contacts/ContactsProvider2;-getType-(Landroid/net/Uri;)Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/AppOpsService;-checkAudioOperation-(I I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-checkOperation-(I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-finishOperation-(Landroid/os/IBinder; I I Ljava/lang/String;)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-getOpsForPackage-(I Ljava/lang/String; [I)Ljava/util/List;": ["android.permission.GET_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-getPackagesForOps-([I)Ljava/util/List;": ["android.permission.GET_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-noteOperation-(I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-resetAllModes-(I Ljava/lang/String;)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-setAudioRestriction-(I I I I [Ljava/lang/String;)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-setMode-(I I Ljava/lang/String; I)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-setUidMode-(I I I)V": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/AppOpsService;-setUserRestriction-(I Z Landroid/os/IBinder; I [Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_APP_OPS_RESTRICTIONS"], "Lcom/android/server/AppOpsService;-startOperation-(Landroid/os/IBinder; I I Ljava/lang/String;)I": ["android.permission.UPDATE_APP_OPS_STATS"], "Lcom/android/server/BluetoothManagerService;-disable-(Z)Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-enable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-enableNoAutoConnect-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Lcom/android/server/BluetoothManagerService;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH", "android.permission.LOCAL_MAC_ADDRESS"], "Lcom/android/server/BluetoothManagerService;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-registerStateChangeCallback-(Landroid/bluetooth/IBluetoothStateChangeCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-unregisterAdapter-(Landroid/bluetooth/IBluetoothManagerCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/BluetoothManagerService;-unregisterStateChangeCallback-(Landroid/bluetooth/IBluetoothStateChangeCallback;)V": ["android.permission.BLUETOOTH"], "Lcom/android/server/ConnectivityService;-factoryReset-()V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL", "android.permission.CONTROL_VPN", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.TETHER_PRIVILEGED"], "Lcom/android/server/ConnectivityService;-getActiveLinkProperties-()Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetwork-()Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkForUid-(I Z)Landroid/net/Network;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getActiveNetworkInfoForUid-(I Z)Landroid/net/NetworkInfo;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getActiveNetworkQuotaInfo-()Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllNetworkState-()[Landroid/net/NetworkState;": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getAllNetworks-()[Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getAllVpnInfo-()[Lcom/android/internal/net/VpnInfo;": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getAlwaysOnVpnPackage-(I)Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.CONTROL_VPN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/ConnectivityService;-getDefaultNetworkCapabilitiesForUser-(I)[Landroid/net/NetworkCapabilities;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLastTetherError-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLegacyVpnInfo-(I)Lcom/android/internal/net/LegacyVpnInfo;": ["android.permission.CONTROL_VPN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/ConnectivityService;-getLinkProperties-(Landroid/net/Network;)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getLinkPropertiesForType-(I)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getMobileProvisioningUrl-()Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getNetworkCapabilities-(Landroid/net/Network;)Landroid/net/NetworkCapabilities;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkForType-(I)Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getNetworkInfoForUid-(Landroid/net/Network; I Z)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableBluetoothRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableUsbRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetherableWifiRegexs-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheredDhcpRanges-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-getTetheredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getTetheringErroredIfaces-()[Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-getVpnConfig-(I)Lcom/android/internal/net/VpnConfig;": ["android.permission.CONTROL_VPN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/ConnectivityService;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isNetworkSupported-(I)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-isTetheringSupported-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-listenForNetwork-(Landroid/net/NetworkCapabilities; Landroid/os/Messenger; Landroid/os/IBinder;)Landroid/net/NetworkRequest;": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-pendingListenForNetwork-(Landroid/net/NetworkCapabilities; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-pendingRequestForNetwork-(Landroid/net/NetworkCapabilities; Landroid/app/PendingIntent;)Landroid/net/NetworkRequest;": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL", "android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS"], "Lcom/android/server/ConnectivityService;-prepareVpn-(Ljava/lang/String; Ljava/lang/String; I)Z": ["android.permission.CONTROL_VPN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/ConnectivityService;-registerNetworkAgent-(Landroid/os/Messenger; Landroid/net/NetworkInfo; Landroid/net/LinkProperties; Landroid/net/NetworkCapabilities; I Landroid/net/NetworkMisc;)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-registerNetworkFactory-(Landroid/os/Messenger; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-reportInetCondition-(I I)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET"], "Lcom/android/server/ConnectivityService;-reportNetworkConnectivity-(Landroid/net/Network; Z)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET"], "Lcom/android/server/ConnectivityService;-requestBandwidthUpdate-(Landroid/net/Network;)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ConnectivityService;-requestNetwork-(Landroid/net/NetworkCapabilities; Landroid/os/Messenger; I Landroid/os/IBinder; I)Landroid/net/NetworkRequest;": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL", "android.permission.CONNECTIVITY_USE_RESTRICTED_NETWORKS"], "Lcom/android/server/ConnectivityService;-requestRouteToHostAddress-(I [B)Z": ["android.permission.CHANGE_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setAcceptUnvalidated-(Landroid/net/Network; Z Z)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setAirplaneMode-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setAlwaysOnVpnPackage-(I Ljava/lang/String; Z)Z": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.CONTROL_VPN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/ConnectivityService;-setAvoidUnvalidated-(Landroid/net/Network;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setGlobalProxy-(Landroid/net/ProxyInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setProvisioningNotificationVisible-(Z I Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-setUsbTethering-(Z)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.TETHER_PRIVILEGED"], "Lcom/android/server/ConnectivityService;-setVpnPackageAuthorization-(Ljava/lang/String; I Z)V": ["android.permission.CONTROL_VPN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/ConnectivityService;-startLegacyVpn-(Lcom/android/internal/net/VpnProfile;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONTROL_VPN"], "Lcom/android/server/ConnectivityService;-startNattKeepalive-(Landroid/net/Network; I Landroid/os/Messenger; Landroid/os/IBinder; Ljava/lang/String; I Ljava/lang/String;)V": ["android.permission.PACKET_KEEPALIVE_OFFLOAD"], "Lcom/android/server/ConnectivityService;-startTethering-(I Landroid/os/ResultReceiver; Z)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.TETHER_PRIVILEGED"], "Lcom/android/server/ConnectivityService;-stopTethering-(I)V": ["android.permission.TETHER_PRIVILEGED"], "Lcom/android/server/ConnectivityService;-tether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.TETHER_PRIVILEGED"], "Lcom/android/server/ConnectivityService;-unregisterNetworkFactory-(Landroid/os/Messenger;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConnectivityService;-untether-(Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.TETHER_PRIVILEGED"], "Lcom/android/server/ConnectivityService;-updateLockdownVpn-()Z": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/ConsumerIrService;-getCarrierFrequencies-()[I": ["android.permission.TRANSMIT_IR"], "Lcom/android/server/ConsumerIrService;-transmit-(Ljava/lang/String; I [I)V": ["android.permission.TRANSMIT_IR"], "Lcom/android/server/DeviceIdleController$BinderService;-addPowerSaveTempWhitelistApp-(Ljava/lang/String; J I Ljava/lang/String;)V": ["android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST"], "Lcom/android/server/DeviceIdleController$BinderService;-addPowerSaveTempWhitelistAppForMms-(Ljava/lang/String; I Ljava/lang/String;)J": ["android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST"], "Lcom/android/server/DeviceIdleController$BinderService;-addPowerSaveTempWhitelistAppForSms-(Ljava/lang/String; I Ljava/lang/String;)J": ["android.permission.CHANGE_DEVICE_IDLE_TEMP_WHITELIST"], "Lcom/android/server/DeviceIdleController$BinderService;-addPowerSaveWhitelistApp-(Ljava/lang/String;)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/DeviceIdleController$BinderService;-exitIdle-(Ljava/lang/String;)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/DeviceIdleController$BinderService;-removePowerSaveWhitelistApp-(Ljava/lang/String;)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/InputMethodManagerService;-addClient-(Lcom/android/internal/view/IInputMethodClient; Lcom/android/internal/view/IInputContext; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-clearLastInputMethodWindowForTransition-(Landroid/os/IBinder;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-createInputContentUriToken-(Landroid/os/IBinder; Landroid/net/Uri; Ljava/lang/String;)Lcom/android/internal/inputmethod/IInputContentUriToken;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getCurrentInputMethodSubtype-()Landroid/view/inputmethod/InputMethodSubtype;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getEnabledInputMethodList-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getEnabledInputMethodSubtypeList-(Ljava/lang/String; Z)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getInputMethodList-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-getLastInputMethodSubtype-()Landroid/view/inputmethod/InputMethodSubtype;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-hideMySoftInput-(Landroid/os/IBinder; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-hideSoftInput-(Lcom/android/internal/view/IInputMethodClient; I Landroid/os/ResultReceiver;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-notifySuggestionPicked-(Landroid/text/style/SuggestionSpan; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-registerSuggestionSpansForNotification-([Landroid/text/style/SuggestionSpan;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-removeClient-(Lcom/android/internal/view/IInputMethodClient;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setAdditionalInputMethodSubtypes-(Ljava/lang/String; [Landroid/view/inputmethod/InputMethodSubtype;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setCurrentInputMethodSubtype-(Landroid/view/inputmethod/InputMethodSubtype;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-setInputMethod-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodAndSubtype-(Landroid/os/IBinder; Ljava/lang/String; Landroid/view/inputmethod/InputMethodSubtype;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-setInputMethodEnabled-(Ljava/lang/String; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-shouldOfferSwitchingToNextInputMethod-(Landroid/os/IBinder;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showInputMethodAndSubtypeEnablerFromClient-(Lcom/android/internal/view/IInputMethodClient; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_EXTERNAL_STORAGE"], "Lcom/android/server/InputMethodManagerService;-showInputMethodPickerFromClient-(Lcom/android/internal/view/IInputMethodClient; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showMySoftInput-(Landroid/os/IBinder; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-showSoftInput-(Lcom/android/internal/view/IInputMethodClient; I Landroid/os/ResultReceiver;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-startInputOrWindowGainedFocus-(I Lcom/android/internal/view/IInputMethodClient; Landroid/os/IBinder; I I I Landroid/view/inputmethod/EditorInfo; Lcom/android/internal/view/IInputContext; I)Lcom/android/internal/view/InputBindResult;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/InputMethodManagerService;-switchToLastInputMethod-(Landroid/os/IBinder;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/InputMethodManagerService;-switchToNextInputMethod-(Landroid/os/IBinder; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/LocationManagerService;-addGnssMeasurementsListener-(Landroid/location/IGnssMeasurementsListener; Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-addGnssNavigationMessageListener-(Landroid/location/IGnssNavigationMessageListener; Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getLastLocation-(Landroid/location/LocationRequest; Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviderProperties-(Ljava/lang/String;)Lcom/android/internal/location/ProviderProperties;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-registerGnssStatusCallback-(Landroid/location/IGnssStatusListener; Ljava/lang/String;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-removeUpdates-(Landroid/location/ILocationListener; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-reportLocation-(Landroid/location/Location; Z)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.INSTALL_LOCATION_PROVIDER"], "Lcom/android/server/LocationManagerService;-requestGeofence-(Landroid/location/LocationRequest; Landroid/location/Geofence; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Lcom/android/server/LocationManagerService;-requestLocationUpdates-(Landroid/location/LocationRequest; Landroid/location/ILocationListener; Landroid/app/PendingIntent; Ljava/lang/String;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.UPDATE_APP_OPS_STATS", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/LocationManagerService;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Lcom/android/server/LockSettingsService;-checkPassword-(Ljava/lang/String; I Lcom/android/internal/widget/ICheckCredentialProgressCallback;)Lcom/android/internal/widget/VerifyCredentialResponse;": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-checkPattern-(Ljava/lang/String; I Lcom/android/internal/widget/ICheckCredentialProgressCallback;)Lcom/android/internal/widget/VerifyCredentialResponse;": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-checkVoldPassword-(I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-getBoolean-(Ljava/lang/String; Z I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE", "android.permission.READ_CONTACTS"], "Lcom/android/server/LockSettingsService;-getLong-(Ljava/lang/String; J I)J": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE", "android.permission.READ_CONTACTS"], "Lcom/android/server/LockSettingsService;-getSeparateProfileChallengeEnabled-(I)Z": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE", "android.permission.READ_CONTACTS"], "Lcom/android/server/LockSettingsService;-getString-(Ljava/lang/String; Ljava/lang/String; I)Ljava/lang/String;": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE", "android.permission.READ_CONTACTS"], "Lcom/android/server/LockSettingsService;-getStrongAuthForUser-(I)I": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-registerStrongAuthTracker-(Landroid/app/trust/IStrongAuthTracker;)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-requireStrongAuth-(I I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-resetKeyStore-(I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setBoolean-(Ljava/lang/String; Z I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLockPassword-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLockPattern-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setLong-(Ljava/lang/String; J I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setSeparateProfileChallengeEnabled-(I Z Ljava/lang/String;)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-setString-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-systemReady-()V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE", "android.permission.READ_CONTACTS"], "Lcom/android/server/LockSettingsService;-unregisterStrongAuthTracker-(Landroid/app/trust/IStrongAuthTracker;)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-userPresent-(I)V": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-verifyPassword-(Ljava/lang/String; J I)Lcom/android/internal/widget/VerifyCredentialResponse;": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-verifyPattern-(Ljava/lang/String; J I)Lcom/android/internal/widget/VerifyCredentialResponse;": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/LockSettingsService;-verifyTiedProfileChallenge-(Ljava/lang/String; Z J I)Lcom/android/internal/widget/VerifyCredentialResponse;": ["android.permission.ACCESS_KEYGUARD_SECURE_STORAGE"], "Lcom/android/server/MmsServiceBroker$BinderService;-downloadMessage-(I Ljava/lang/String; Ljava/lang/String; Landroid/net/Uri; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.RECEIVE_MMS"], "Lcom/android/server/MmsServiceBroker$BinderService;-sendMessage-(I Ljava/lang/String; Landroid/net/Uri; Ljava/lang/String; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Lcom/android/server/MountService;-addUserKeyAuth-(I I [B [B)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-benchmark-(Ljava/lang/String;)J": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-changeEncryptionPassword-(I Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-clearPassword-()V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-createSecureContainer-(Ljava/lang/String; I Ljava/lang/String; Ljava/lang/String; I Z)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-createUserKey-(I I Z)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-decryptStorage-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-destroySecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_DESTROY"], "Lcom/android/server/MountService;-destroyUserKey-(I)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-destroyUserStorage-(Ljava/lang/String; I I)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-encryptStorage-(I Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-finalizeSecureContainer-(Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-fixPermissionsSecureContainer-(Ljava/lang/String; I Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-fixateNewestUserKeyAuth-(I)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-forgetAllVolumes-()V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-forgetVolume-(Ljava/lang/String;)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-format-(Ljava/lang/String;)V": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-formatVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-getEncryptionState-()I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/MountService;-getField-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-getPassword-()Ljava/lang/String;": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-getPasswordType-()I": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-getPrimaryStorageUuid-()Ljava/lang/String;": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-getSecureContainerFilesystemPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerList-()[Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getSecureContainerPath-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-getStorageUsers-(Ljava/lang/String;)[I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-isConvertibleToFBE-()Z": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-isSecureContainerMounted-(Ljava/lang/String;)Z": ["android.permission.ASEC_ACCESS"], "Lcom/android/server/MountService;-lockUserKey-(I)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-mount-(Ljava/lang/String;)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-mountSecureContainer-(Ljava/lang/String; Ljava/lang/String; I Z)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-mountVolume-(Ljava/lang/String;)I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-partitionMixed-(Ljava/lang/String; I)V": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-partitionPrivate-(Ljava/lang/String;)V": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-partitionPublic-(Ljava/lang/String;)V": ["android.permission.MOUNT_FORMAT_FILESYSTEMS"], "Lcom/android/server/MountService;-prepareUserStorage-(Ljava/lang/String; I I I)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-renameSecureContainer-(Ljava/lang/String; Ljava/lang/String;)I": ["android.permission.ASEC_RENAME"], "Lcom/android/server/MountService;-resizeSecureContainer-(Ljava/lang/String; I Ljava/lang/String;)I": ["android.permission.ASEC_CREATE"], "Lcom/android/server/MountService;-runMaintenance-()V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-setDebugFlags-(I I)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-setField-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-setPrimaryStorageUuid-(Ljava/lang/String; Landroid/content/pm/IPackageMoveObserver;)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-setVolumeNickname-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-setVolumeUserFlags-(Ljava/lang/String; I I)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-shutdown-(Landroid/os/storage/IMountShutdownObserver;)V": ["android.permission.SHUTDOWN"], "Lcom/android/server/MountService;-unlockUserKey-(I I [B [B)V": ["android.permission.STORAGE_INTERNAL"], "Lcom/android/server/MountService;-unmount-(Ljava/lang/String;)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-unmountSecureContainer-(Ljava/lang/String; Z)I": ["android.permission.ASEC_MOUNT_UNMOUNT"], "Lcom/android/server/MountService;-unmountVolume-(Ljava/lang/String; Z Z)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/MountService;-verifyEncryptionPassword-(Ljava/lang/String;)I": ["android.permission.CRYPT_KEEPER"], "Lcom/android/server/NetworkManagementService;-addIdleTimer-(Ljava/lang/String; I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addInterfaceToLocalNetwork-(Ljava/lang/String; Ljava/util/List;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addInterfaceToNetwork-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addLegacyRouteForNetId-(I Landroid/net/RouteInfo; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addRoute-(I Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-addVpnUidRanges-(I [Landroid/net/UidRange;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-allowProtect-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-attachPppd-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearDefaultNetId-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearInterfaceAddresses-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-clearPermission-([I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-createPhysicalNetwork-(I Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-createVirtualNetwork-(I Z Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-denyProtect-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-detachPppd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-disableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableIpv6-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-enableNat-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getDnsForwarders-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getInterfaceConfig-(Ljava/lang/String;)Landroid/net/InterfaceConfiguration;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getIpForwardingEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsDetail-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryDev-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsSummaryXt-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsTethering-()Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-getNetworkStatsUidDetail-(I)Landroid/net/NetworkStats;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isBandwidthControlEnabled-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isClatdStarted-(Ljava/lang/String;)Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-isTetheringStarted-()Z": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTetheredInterfaces-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-listTtys-()[Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-registerObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeIdleTimer-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceAlert-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceFromLocalNetwork-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceFromNetwork-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeInterfaceQuota-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeNetwork-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeRoute-(I Landroid/net/RouteInfo;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeRoutesFromLocalNetwork-(Ljava/util/List;)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-removeVpnUidRanges-(I [Landroid/net/UidRange;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDefaultNetId-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsConfigurationForNetwork-(I [Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsForwarders-(Landroid/net/Network; [Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setDnsServersForNetwork-(I [Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setGlobalAlert-(J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceAlert-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceConfig-(Ljava/lang/String; Landroid/net/InterfaceConfiguration;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceDown-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceIpv6NdOffload-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceIpv6PrivacyExtensions-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceQuota-(Ljava/lang/String; J)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setInterfaceUp-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setIpForwardingEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setMtu-(Ljava/lang/String; I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setNetworkPermission-(I Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setPermission-(Ljava/lang/String; [I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setUidCleartextNetworkPolicy-(I I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setUidMeteredNetworkBlacklist-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-setUidMeteredNetworkWhitelist-(I Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-shutdown-()V": ["android.permission.SHUTDOWN"], "Lcom/android/server/NetworkManagementService;-startAccessPoint-(Landroid/net/wifi/WifiConfiguration; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startClatd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startInterfaceForwarding-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-startTethering-([Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopAccessPoint-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopClatd-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopInterfaceForwarding-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-stopTethering-()V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-tetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-unregisterObserver-(Landroid/net/INetworkManagementEventObserver;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-untetherInterface-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkManagementService;-wifiFirmwareReload-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/NetworkScoreService;-clearScores-()Z": ["android.permission.BROADCAST_NETWORK_PRIVILEGED", "android.permission.SCORE_NETWORKS"], "Lcom/android/server/NetworkScoreService;-disableScoring-()V": ["android.permission.SCORE_NETWORKS"], "Lcom/android/server/NetworkScoreService;-registerNetworkScoreCache-(I Landroid/net/INetworkScoreCache;)V": ["android.permission.BROADCAST_NETWORK_PRIVILEGED"], "Lcom/android/server/NetworkScoreService;-setActiveScorer-(Ljava/lang/String;)Z": ["android.permission.SCORE_NETWORKS"], "Lcom/android/server/NetworkScoreService;-updateScores-([Landroid/net/ScoredNetwork;)Z": ["android.permission.SCORE_NETWORKS"], "Lcom/android/server/NsdService;-getMessenger-()Landroid/os/Messenger;": ["android.permission.INTERNET"], "Lcom/android/server/NsdService;-setEnabled-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/RecoverySystemService$BinderService;-clearBcb-()Z": ["android.permission.RECOVERY"], "Lcom/android/server/RecoverySystemService$BinderService;-rebootRecoveryWithCommand-(Ljava/lang/String;)V": ["android.permission.RECOVERY"], "Lcom/android/server/RecoverySystemService$BinderService;-setupBcb-(Ljava/lang/String;)Z": ["android.permission.RECOVERY"], "Lcom/android/server/RecoverySystemService$BinderService;-uncrypt-(Ljava/lang/String; Landroid/os/IRecoverySystemProgressListener;)Z": ["android.permission.RECOVERY"], "Lcom/android/server/SerialService;-getSerialPorts-()[Ljava/lang/String;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/SerialService;-openSerialPort-(Ljava/lang/String;)Landroid/os/ParcelFileDescriptor;": ["android.permission.SERIAL_PORT"], "Lcom/android/server/TelephonyRegistry;-addOnSubscriptionsChangedListener-(Ljava/lang/String; Lcom/android/internal/telephony/IOnSubscriptionsChangedListener;)V": ["android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-listen-(Ljava/lang/String; Lcom/android/internal/telephony/IPhoneStateListener; I Z)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE", "android.permission.READ_PRECISE_PHONE_STATE", "android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-listenForSubscriber-(I Ljava/lang/String; Lcom/android/internal/telephony/IPhoneStateListener; I Z)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE", "android.permission.READ_PRECISE_PHONE_STATE", "android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallForwardingChanged-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallForwardingChangedForSubscriber-(I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallState-(I Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCallStateForPhoneId-(I I I Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCarrierNetworkChange-(Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellInfo-(Ljava/util/List;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellInfoForSubscriber-(I Ljava/util/List;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellLocation-(Landroid/os/Bundle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyCellLocationForSubscriber-(I Landroid/os/Bundle;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataActivity-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataActivityForSubscriber-(I I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnection-(I Z Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/net/LinkProperties; Landroid/net/NetworkCapabilities; I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionFailed-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionFailedForSubscriber-(I Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDataConnectionForSubscriber-(I I Z Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/net/LinkProperties; Landroid/net/NetworkCapabilities; I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyDisconnectCause-(I I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyMessageWaitingChangedForPhoneId-(I I Z)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyOemHookRawEventForSubscriber-(I [B)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyOtaspChanged-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyPreciseCallState-(I I I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyPreciseDataConnectionFailed-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyServiceStateForPhoneId-(I I Landroid/telephony/ServiceState;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifySignalStrengthForPhoneId-(I I Landroid/telephony/SignalStrength;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TelephonyRegistry;-notifyVoLteServiceStateChanged-(Landroid/telephony/VoLteServiceState;)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellChecker-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setCurrentSpellCheckerSubtype-(Ljava/lang/String; I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/TextServicesManagerService;-setSpellCheckerEnabled-(Z)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/UpdateLockService;-acquireUpdateLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/UpdateLockService;-releaseUpdateLock-(Landroid/os/IBinder;)V": ["android.permission.UPDATE_LOCK"], "Lcom/android/server/VibratorService;-cancelVibrate-(Landroid/os/IBinder;)V": ["android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibrate-(I Ljava/lang/String; J I Landroid/os/IBinder;)V": ["android.permission.UPDATE_APP_OPS_STATS", "android.permission.VIBRATE"], "Lcom/android/server/VibratorService;-vibratePattern-(I Ljava/lang/String; [J I I Landroid/os/IBinder;)V": ["android.permission.UPDATE_APP_OPS_STATS", "android.permission.VIBRATE"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfoByAccessibilityId-(I J I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; I J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfosByText-(I J Ljava/lang/String; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findAccessibilityNodeInfosByViewId-(I J Ljava/lang/String; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-findFocus-(I J I I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-focusSearch-(I J I I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getMagnificationCenterX-()F": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getMagnificationCenterY-()F": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getMagnificationRegion-()Landroid/graphics/Region;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getMagnificationScale-()F": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getWindow-(I)Landroid/view/accessibility/AccessibilityWindowInfo;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-getWindows-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-performAccessibilityAction-(I J I Landroid/os/Bundle; I Landroid/view/accessibility/IAccessibilityInteractionConnectionCallback; J)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-performGlobalAction-(I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-resetMagnification-(Z)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-setMagnificationScaleAndCenter-(F F F Z)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService$Service;-setSoftKeyboardShowMode-(I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-addAccessibilityInteractionConnection-(Landroid/view/IWindow; Landroid/view/accessibility/IAccessibilityInteractionConnection; I)I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-addClient-(Landroid/view/accessibility/IAccessibilityManagerClient; I)I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-disableAccessibilityService-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-enableAccessibilityService-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getEnabledAccessibilityServiceList-(I I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getInstalledAccessibilityServiceList-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-getWindowToken-(I I)Landroid/os/IBinder;": ["getWindowToken"], "Lcom/android/server/accessibility/AccessibilityManagerService;-interrupt-(I)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-removeAccessibilityInteractionConnection-(Landroid/view/IWindow;)V": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-sendAccessibilityEvent-(Landroid/view/accessibility/AccessibilityEvent; I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/accessibility/AccessibilityManagerService;-temporaryEnableAccessibilityStateUntilKeyguardRemoved-(Landroid/content/ComponentName; Z)V": ["temporaryEnableAccessibilityStateUntilKeyguardRemoved"], "Lcom/android/server/accounts/AccountManagerService;-addAccountAsUser-(Landroid/accounts/IAccountManagerResponse; Ljava/lang/String; Ljava/lang/String; [Ljava/lang/String; Z Landroid/os/Bundle; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-addSharedAccountsFromParentUser-(I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-confirmCredentialsAsUser-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Landroid/os/Bundle; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-copyAccountToUser-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-finishSessionAsUser-(Landroid/accounts/IAccountManagerResponse; Landroid/os/Bundle; Z Landroid/os/Bundle; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccounts-(Ljava/lang/String; Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsAsUser-(Ljava/lang/String; I Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsByTypeForPackage-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAccountsForPackage-(Ljava/lang/String; I Ljava/lang/String;)[Landroid/accounts/Account;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-getAuthenticatorTypes-(I)[Landroid/accounts/AuthenticatorDescription;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-removeAccount-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/accounts/AccountManagerService;-removeAccountAsUser-(Landroid/accounts/IAccountManagerResponse; Landroid/accounts/Account; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-appNotRespondingViaProvider-(Landroid/os/IBinder;)V": ["android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-bindBackupAgent-(Ljava/lang/String; I I)Z": ["android.permission.CONFIRM_FULL_BACKUP", "android.permission.START_ANY_ACTIVITY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ActivityManagerService;-bindService-(Landroid/app/IApplicationThread; Landroid/os/IBinder; Landroid/content/Intent; Ljava/lang/String; Landroid/app/IServiceConnection; I Ljava/lang/String; I)I": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.START_ANY_ACTIVITY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ActivityManagerService;-bootAnimationComplete-()V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-clearGrantedUriPermissions-(Ljava/lang/String; I)V": ["android.permission.CLEAR_APP_GRANTED_URI_PERMISSIONS"], "Lcom/android/server/am/ActivityManagerService;-clearPendingBackup-()V": ["android.permission.BACKUP"], "Lcom/android/server/am/ActivityManagerService;-crashApplication-(I I Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-createStackOnDisplay-(I)Landroid/app/IActivityContainer;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-createVirtualActivityContainer-(Landroid/os/IBinder; Landroid/app/IActivityContainerCallback;)Landroid/app/IActivityContainer;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-deleteActivityContainer-(Landroid/app/IActivityContainer;)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-dumpHeap-(Ljava/lang/String; I Z Ljava/lang/String; Landroid/os/ParcelFileDescriptor;)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-finishHeavyWeightApp-()V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-forceStopPackage-(Ljava/lang/String; I)V": ["android.permission.FORCE_STOP_PACKAGES"], "Lcom/android/server/am/ActivityManagerService;-getAllStackInfos-()Ljava/util/List;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getAssistContextExtras-(I)Landroid/os/Bundle;": ["android.permission.GET_TOP_ACTIVITY_INFO"], "Lcom/android/server/am/ActivityManagerService;-getContentProviderExternal-(Ljava/lang/String; I Landroid/os/IBinder;)Landroid/app/IActivityManager$ContentProviderHolder;": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY"], "Lcom/android/server/am/ActivityManagerService;-getCurrentUser-()Landroid/content/pm/UserInfo;": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-getGrantedUriPermissions-(Ljava/lang/String; I)Landroid/content/pm/ParceledListSlice;": ["android.permission.GET_APP_GRANTED_URI_PERMISSIONS"], "Lcom/android/server/am/ActivityManagerService;-getIntentForIntentSender-(Landroid/content/IIntentSender;)Landroid/content/Intent;": ["android.permission.GET_INTENT_SENDER_INTENT"], "Lcom/android/server/am/ActivityManagerService;-getPackageProcessState-(Ljava/lang/String; Ljava/lang/String;)I": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/ActivityManagerService;-getRecentTasks-(I I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.GET_DETAILED_TASKS", "android.permission.GET_TASKS", "android.permission.REAL_GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-getRunningAppProcesses-()Ljava/util/List;": ["android.permission.GET_TASKS", "android.permission.REAL_GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-getRunningExternalApplications-()Ljava/util/List;": ["android.permission.GET_TASKS", "android.permission.REAL_GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-getRunningUserIds-()[I": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-getStackInfo-(I)Landroid/app/ActivityManager$StackInfo;": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getTaskBounds-(I)Landroid/graphics/Rect;": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-getTaskDescriptionIcon-(Ljava/lang/String; I)Landroid/graphics/Bitmap;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-getTaskThumbnail-(I)Landroid/app/ActivityManager$TaskThumbnail;": ["android.permission.BROADCAST_STICKY", "android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/am/ActivityManagerService;-getTasks-(I I)Ljava/util/List;": ["android.permission.GET_TASKS", "android.permission.REAL_GET_TASKS"], "Lcom/android/server/am/ActivityManagerService;-hang-(Landroid/os/IBinder; Z)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-inputDispatchingTimedOut-(I Z Ljava/lang/String;)J": ["android.permission.FILTER_EVENTS"], "Lcom/android/server/am/ActivityManagerService;-isInHomeStack-(I)Z": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-isUserRunning-(I I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/am/ActivityManagerService;-killAllBackgroundProcesses-()V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-killBackgroundProcesses-(Ljava/lang/String; I)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-killPackageDependents-(Ljava/lang/String; I)V": ["android.permission.KILL_UID"], "Lcom/android/server/am/ActivityManagerService;-killUid-(I I Ljava/lang/String;)V": ["android.permission.KILL_UID"], "Lcom/android/server/am/ActivityManagerService;-launchAssistIntent-(Landroid/content/Intent; I Ljava/lang/String; I Landroid/os/Bundle;)Z": ["android.permission.GET_TOP_ACTIVITY_INFO"], "Lcom/android/server/am/ActivityManagerService;-moveTaskBackwards-(I)V": ["android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToDockedStack-(I I Z Z Landroid/graphics/Rect; Z)Z": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS"], "Lcom/android/server/am/ActivityManagerService;-moveTaskToStack-(I I Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-moveTasksToFullscreenStack-(I Z)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-moveTopActivityToPinnedStack-(I Landroid/graphics/Rect;)Z": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-navigateUpTo-(Landroid/os/IBinder; Landroid/content/Intent; I Landroid/content/Intent;)Z": ["android.permission.SET_DEBUG_APP", "android.permission.START_ANY_ACTIVITY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ActivityManagerService;-performIdleMaintenance-()V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-positionTaskInStack-(I I I)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-profileControl-(Ljava/lang/String; I Z Landroid/app/ProfilerInfo; I)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-registerProcessObserver-(Landroid/app/IProcessObserver;)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-registerTaskStackListener-(Landroid/app/ITaskStackListener;)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-registerUidObserver-(Landroid/app/IUidObserver; I)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-registerUserSwitchObserver-(Landroid/app/IUserSwitchObserver; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-removeContentProviderExternal-(Ljava/lang/String; Landroid/os/IBinder;)V": ["android.permission.ACCESS_CONTENT_PROVIDERS_EXTERNALLY"], "Lcom/android/server/am/ActivityManagerService;-removeStack-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-removeTask-(I)Z": ["android.permission.BROADCAST_STICKY", "android.permission.REMOVE_TASKS"], "Lcom/android/server/am/ActivityManagerService;-requestAssistContextExtras-(I Lcom/android/internal/os/IResultReceiver; Landroid/os/Bundle; Landroid/os/IBinder; Z Z)Z": ["android.permission.GET_TOP_ACTIVITY_INFO"], "Lcom/android/server/am/ActivityManagerService;-requestBugReport-(I)V": ["android.permission.DUMP"], "Lcom/android/server/am/ActivityManagerService;-resizeDockedStack-(Landroid/graphics/Rect; Landroid/graphics/Rect; Landroid/graphics/Rect; Landroid/graphics/Rect; Landroid/graphics/Rect;)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-resizePinnedStack-(Landroid/graphics/Rect; Landroid/graphics/Rect;)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-resizeStack-(I Landroid/graphics/Rect; Z Z Z I)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-resizeTask-(I Landroid/graphics/Rect; I)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-restart-()V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-resumeAppSwitches-()V": ["android.permission.STOP_APP_SWITCHES"], "Lcom/android/server/am/ActivityManagerService;-sendIdleJobTrigger-()V": ["android.permission.BROADCAST_STICKY", "android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-setActivityController-(Landroid/app/IActivityController; Z)V": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-setAlwaysFinish-(Z)V": ["android.permission.SET_ALWAYS_FINISH"], "Lcom/android/server/am/ActivityManagerService;-setDebugApp-(Ljava/lang/String; Z Z)V": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-setDumpHeapDebugLimit-(Ljava/lang/String; I J Ljava/lang/String;)V": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-setFocusedStack-(I)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-setFocusedTask-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-setFrontActivityScreenCompatMode-(I)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setHasTopUi-(Z)V": ["android.permission.INTERNAL_SYSTEM_WINDOW"], "Lcom/android/server/am/ActivityManagerService;-setLenientBackgroundCheck-(Z)V": ["android.permission.SET_PROCESS_LIMIT"], "Lcom/android/server/am/ActivityManagerService;-setLockScreenShown-(Z Z)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/am/ActivityManagerService;-setPackageAskScreenCompat-(Ljava/lang/String; Z)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setPackageScreenCompatMode-(Ljava/lang/String; I)V": ["android.permission.SET_SCREEN_COMPATIBILITY"], "Lcom/android/server/am/ActivityManagerService;-setProcessForeground-(Landroid/os/IBinder; I Z)V": ["android.permission.SET_PROCESS_LIMIT"], "Lcom/android/server/am/ActivityManagerService;-setProcessLimit-(I)V": ["android.permission.SET_PROCESS_LIMIT"], "Lcom/android/server/am/ActivityManagerService;-setTaskDescription-(Landroid/os/IBinder; Landroid/app/ActivityManager$TaskDescription;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-shutdown-(I)Z": ["android.permission.SHUTDOWN"], "Lcom/android/server/am/ActivityManagerService;-signalPersistentProcesses-(I)V": ["android.permission.SIGNAL_PERSISTENT_PROCESSES"], "Lcom/android/server/am/ActivityManagerService;-startActivities-(Landroid/app/IApplicationThread; Ljava/lang/String; [Landroid/content/Intent; [Ljava/lang/String; Landroid/os/IBinder; Landroid/os/Bundle; I)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivity-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle;)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityAndWait-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle; I)Landroid/app/IActivityManager$WaitResult;": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityAsCaller-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle; Z I)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityAsUser-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/app/ProfilerInfo; Landroid/os/Bundle; I)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startActivityFromRecents-(I Landroid/os/Bundle;)I": ["android.permission.BROADCAST_STICKY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ActivityManagerService;-startActivityWithConfig-(Landroid/app/IApplicationThread; Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; Landroid/os/IBinder; Ljava/lang/String; I I Landroid/content/res/Configuration; Landroid/os/Bundle; I)I": ["android.permission.SET_DEBUG_APP"], "Lcom/android/server/am/ActivityManagerService;-startBinderTracking-()Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-startConfirmDeviceCredentialIntent-(Landroid/content/Intent;)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-startSystemLockTaskMode-(I)V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-startUserInBackground-(I)Z": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-startVoiceActivity-(Ljava/lang/String; I I Landroid/content/Intent; Ljava/lang/String; Landroid/service/voice/IVoiceInteractionSession; Lcom/android/internal/app/IVoiceInteractor; I Landroid/app/ProfilerInfo; Landroid/os/Bundle; I)I": ["android.permission.BIND_VOICE_INTERACTION"], "Lcom/android/server/am/ActivityManagerService;-stopAppSwitches-()V": ["android.permission.BROADCAST_STICKY", "android.permission.STOP_APP_SWITCHES"], "Lcom/android/server/am/ActivityManagerService;-stopBinderTrackingAndDump-(Landroid/os/ParcelFileDescriptor;)Z": ["android.permission.SET_ACTIVITY_WATCHER"], "Lcom/android/server/am/ActivityManagerService;-stopLockTaskMode-()V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-stopService-(Landroid/app/IApplicationThread; Landroid/content/Intent; Ljava/lang/String; I)I": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.START_ANY_ACTIVITY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ActivityManagerService;-stopServiceToken-(Landroid/content/ComponentName; Landroid/os/IBinder; I)Z": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.START_ANY_ACTIVITY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ActivityManagerService;-stopSystemLockTaskMode-()V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-stopUser-(I Z Landroid/app/IStopUserCallback;)I": ["android.permission.BROADCAST_STICKY", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-suppressResizeConfigChanges-(Z)V": ["android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-swapDockedAndFullscreenStack-()V": ["android.permission.BROADCAST_STICKY", "android.permission.MANAGE_ACTIVITY_STACKS"], "Lcom/android/server/am/ActivityManagerService;-unbindService-(Landroid/app/IServiceConnection;)Z": ["android.permission.BROADCAST_STICKY", "android.permission.SET_DEBUG_APP", "android.permission.START_ANY_ACTIVITY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ActivityManagerService;-unbroadcastIntent-(Landroid/app/IApplicationThread; Landroid/content/Intent; I)V": ["android.permission.BROADCAST_STICKY"], "Lcom/android/server/am/ActivityManagerService;-unhandledBack-()V": ["android.permission.FORCE_BACK"], "Lcom/android/server/am/ActivityManagerService;-unlockUser-(I [B [B Landroid/os/IProgressListener;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/am/ActivityManagerService;-updateConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.CHANGE_CONFIGURATION"], "Lcom/android/server/am/ActivityManagerService;-updatePersistentConfiguration-(Landroid/content/res/Configuration;)V": ["android.permission.CHANGE_CONFIGURATION"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimeBattery-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getAwakeTimePlugged-()J": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getStatistics-()[B": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-getStatisticsStream-()Landroid/os/ParcelFileDescriptor;": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBleScanStarted-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBleScanStopped-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteBluetoothControllerActivity-(Landroid/bluetooth/BluetoothActivityEnergyInfo;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteChangeWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteConnectivityChanged-(I Ljava/lang/String;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteDeviceIdleMode-(I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteEvent-(I Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFlashlightOff-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFlashlightOn-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquired-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockAcquiredFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleased-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteFullWifiLockReleasedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteInteractive-(Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteJobFinish-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteJobStart-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteLongPartialWakelockFinish-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteLongPartialWakelockStart-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteMobileRadioPowerState-(I J I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteModemControllerActivity-(Landroid/telephony/ModemActivityInfo;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteNetworkInterfaceType-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteNetworkStatsEnabled-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneDataConnectionState-(I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneSignalStrength-(Landroid/telephony/SignalStrength;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-notePhoneState-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteResetAudio-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteResetBleScan-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteResetCamera-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteResetFlashlight-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteResetVideo-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenBrightness-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteScreenState-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartAudio-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartCamera-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartVideo-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelock-(I I Ljava/lang/String; Ljava/lang/String; I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStartWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopAudio-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopCamera-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopGps-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopSensor-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopVideo-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelock-(I I Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteStopWakelockFromSource-(Landroid/os/WorkSource; I Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteSyncFinish-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteSyncStart-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteUserActivity-(I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteVibratorOff-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteVibratorOn-(I J)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWakeUp-(Ljava/lang/String; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiBatchedScanStartedFromSource-(Landroid/os/WorkSource; I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiBatchedScanStoppedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiControllerActivity-(Landroid/net/wifi/WifiActivityEnergyInfo;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastDisabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabled-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiMulticastEnabledFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOff-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiOn-()V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRadioPowerState-(I J I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRssiChanged-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunning-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiRunningChanged-(Landroid/os/WorkSource; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStarted-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStartedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStopped-(I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiScanStoppedFromSource-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiState-(I Ljava/lang/String;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiStopped-(Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-noteWifiSupplicantStateChanged-(I Z)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-setBatteryState-(I I I I I I I)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/am/BatteryStatsService;-takeUidSnapshot-(I)Landroid/os/health/HealthStatsParceler;": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/BatteryStatsService;-takeUidSnapshots-([I)[Landroid/os/health/HealthStatsParceler;": ["android.permission.BATTERY_STATS"], "Lcom/android/server/am/PendingIntentRecord;-send-(I Landroid/content/Intent; Ljava/lang/String; Landroid/content/IIntentReceiver; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.START_ANY_ACTIVITY", "android.permission.START_TASKS_FROM_RECENTS"], "Lcom/android/server/am/ProcessStatsService;-getCurrentStats-(Ljava/util/List;)[B": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/am/ProcessStatsService;-getStatsOverTime-(J)Landroid/os/ParcelFileDescriptor;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-bindAppWidgetId-(Ljava/lang/String; I I Landroid/content/ComponentName; Landroid/os/Bundle;)Z": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-bindRemoteViewsService-(Ljava/lang/String; I Landroid/content/Intent; Landroid/os/IBinder;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-createAppWidgetConfigIntentSender-(Ljava/lang/String; I I)Landroid/content/IntentSender;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-deleteAppWidgetId-(Ljava/lang/String; I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-getAppWidgetInfo-(Ljava/lang/String; I)Landroid/appwidget/AppWidgetProviderInfo;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-getAppWidgetOptions-(Ljava/lang/String; I)Landroid/os/Bundle;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-getAppWidgetViews-(Ljava/lang/String; I)Landroid/widget/RemoteViews;": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-hasBindAppWidgetPermission-(Ljava/lang/String; I)Z": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-notifyAppWidgetViewDataChanged-(Ljava/lang/String; [I I)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-partiallyUpdateAppWidgetIds-(Ljava/lang/String; [I Landroid/widget/RemoteViews;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-setBindAppWidgetPermission-(Ljava/lang/String; I Z)V": ["android.permission.MODIFY_APPWIDGET_BIND_PERMISSIONS"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-unbindRemoteViewsService-(Ljava/lang/String; I Landroid/content/Intent;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-updateAppWidgetIds-(Ljava/lang/String; [I Landroid/widget/RemoteViews;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/appwidget/AppWidgetServiceImpl;-updateAppWidgetOptions-(Ljava/lang/String; I Landroid/os/Bundle;)V": ["android.permission.BIND_APPWIDGET"], "Lcom/android/server/audio/AudioService;-disableSafeMediaVolume-(Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/audio/AudioService;-forceRemoteSubmixFullVolume-(Z Landroid/os/IBinder;)V": ["android.permission.CAPTURE_AUDIO_OUTPUT"], "Lcom/android/server/audio/AudioService;-notifyVolumeControllerVisible-(Landroid/media/IVolumeController; Z)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/audio/AudioService;-registerAudioPolicy-(Landroid/media/audiopolicy/AudioPolicyConfig; Landroid/media/audiopolicy/IAudioPolicyCallback; Z)Ljava/lang/String;": ["android.permission.MODIFY_AUDIO_ROUTING"], "Lcom/android/server/audio/AudioService;-requestAudioFocus-(Landroid/media/AudioAttributes; I Landroid/os/IBinder; Landroid/media/IAudioFocusDispatcher; Ljava/lang/String; Ljava/lang/String; I Landroid/media/audiopolicy/IAudioPolicyCallback;)I": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/audio/AudioService;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/server/audio/AudioService;-setFocusPropertiesForPolicy-(I Landroid/media/audiopolicy/IAudioPolicyCallback;)I": ["android.permission.MODIFY_AUDIO_ROUTING"], "Lcom/android/server/audio/AudioService;-setMasterMute-(Z I Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/audio/AudioService;-setMicrophoneMute-(Z Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/server/audio/AudioService;-setMode-(I Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/server/audio/AudioService;-setRingerModeInternal-(I Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/audio/AudioService;-setRingtonePlayer-(Landroid/media/IRingtonePlayer;)V": ["android.permission.REMOTE_AUDIO_PLAYBACK"], "Lcom/android/server/audio/AudioService;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/server/audio/AudioService;-setVolumeController-(Landroid/media/IVolumeController;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/audio/AudioService;-setVolumePolicy-(Landroid/media/VolumePolicy;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/audio/AudioService;-startBluetoothSco-(Landroid/os/IBinder; I)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/server/audio/AudioService;-startBluetoothScoVirtualCall-(Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/server/audio/AudioService;-stopBluetoothSco-(Landroid/os/IBinder;)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-getAvailableRestoreSets-(Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-restoreAll-(J Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-restorePackage-(Ljava/lang/String; Landroid/app/backup/IRestoreObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/BackupManagerService$ActiveRestoreSession;-restoreSome-(J Landroid/app/backup/IRestoreObserver; [Ljava/lang/String;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-acknowledgeFullBackupOrRestore-(I Z Ljava/lang/String; Ljava/lang/String; Landroid/app/backup/IFullBackupRestoreObserver;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-backupNow-()V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-beginRestoreSession-(Ljava/lang/String; Ljava/lang/String;)Landroid/app/backup/IRestoreSession;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-clearBackupData-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-dataChanged-(Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-fullBackup-(Landroid/os/ParcelFileDescriptor; Z Z Z Z Z Z Z [Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-fullRestore-(Landroid/os/ParcelFileDescriptor;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-fullTransportBackup-([Ljava/lang/String;)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-getAvailableRestoreToken-(Ljava/lang/String;)J": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-getConfigurationIntent-(Ljava/lang/String;)Landroid/content/Intent;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-getCurrentTransport-()Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-getDataManagementIntent-(Ljava/lang/String;)Landroid/content/Intent;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-getDataManagementLabel-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-getDestinationString-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-hasBackupPassword-()Z": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-isAppEligibleForBackup-(Ljava/lang/String;)Z": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-isBackupEnabled-()Z": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-listAllTransports-()[Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-requestBackup-([Ljava/lang/String; Landroid/app/backup/IBackupObserver;)I": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-restoreAtInstall-(Ljava/lang/String; I)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-selectBackupTransport-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-setAutoRestore-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-setBackupEnabled-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-setBackupPassword-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BACKUP"], "Lcom/android/server/backup/Trampoline;-setBackupProvisioned-(Z)V": ["android.permission.BACKUP"], "Lcom/android/server/connectivity/IpConnectivityMetrics$Impl;-logEvent-(Landroid/net/ConnectivityMetricsEvent;)I": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/connectivity/MetricsLoggerService$MetricsLoggerImpl;-getEvents-(Landroid/net/ConnectivityMetricsEvent$Reference;)[Landroid/net/ConnectivityMetricsEvent;": ["android.permission.DUMP"], "Lcom/android/server/connectivity/MetricsLoggerService$MetricsLoggerImpl;-logEvent-(Landroid/net/ConnectivityMetricsEvent;)J": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/connectivity/MetricsLoggerService$MetricsLoggerImpl;-logEvents-([Landroid/net/ConnectivityMetricsEvent;)J": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/connectivity/MetricsLoggerService$MetricsLoggerImpl;-register-(Landroid/app/PendingIntent;)Z": ["android.permission.DUMP"], "Lcom/android/server/connectivity/MetricsLoggerService$MetricsLoggerImpl;-unregister-(Landroid/app/PendingIntent;)V": ["android.permission.DUMP"], "Lcom/android/server/content/ContentService;-addPeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle; J)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-cancelSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-cancelSyncAsUser-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getCache-(Ljava/lang/String; Landroid/net/Uri; I)Landroid/os/Bundle;": ["android.permission.CACHE_CONTENT", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getCurrentSyncs-()Ljava/util/List;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-getCurrentSyncsAsUser-(I)Ljava/util/List;": ["android.permission.GET_ACCOUNTS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-getIsSyncable-(Landroid/accounts/Account; Ljava/lang/String;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getIsSyncableAsUser-(Landroid/accounts/Account; Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getMasterSyncAutomatically-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getMasterSyncAutomaticallyAsUser-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getPeriodicSyncs-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncAdapterPackagesForAuthorityAsUser-(Ljava/lang/String; I)[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getSyncAdapterTypes-()[Landroid/content/SyncAdapterType;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getSyncAdapterTypesAsUser-(I)[Landroid/content/SyncAdapterType;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-getSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncAutomaticallyAsUser-(Landroid/accounts/Account; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-getSyncStatus-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Landroid/content/SyncStatusInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-getSyncStatusAsUser-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName; I)Landroid/content/SyncStatusInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncActive-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Z": ["android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncPending-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-isSyncPendingAsUser-(Landroid/accounts/Account; Ljava/lang/String; Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.READ_SYNC_STATS"], "Lcom/android/server/content/ContentService;-putCache-(Ljava/lang/String; Landroid/net/Uri; Landroid/os/Bundle; I)V": ["android.permission.CACHE_CONTENT", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-registerContentObserver-(Landroid/net/Uri; Z Landroid/database/IContentObserver; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-removePeriodicSync-(Landroid/accounts/Account; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setIsSyncable-(Landroid/accounts/Account; Ljava/lang/String; I)V": ["android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setMasterSyncAutomatically-(Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setMasterSyncAutomaticallyAsUser-(Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setSyncAutomatically-(Landroid/accounts/Account; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-setSyncAutomaticallyAsUser-(Landroid/accounts/Account; Ljava/lang/String; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.WRITE_SYNC_SETTINGS"], "Lcom/android/server/content/ContentService;-sync-(Landroid/content/SyncRequest;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/content/ContentService;-syncAsUser-(Landroid/content/SyncRequest; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-addCrossProfileIntentFilter-(Landroid/content/ComponentName; Landroid/content/IntentFilter; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-addCrossProfileWidgetProvider-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-addPersistentPreferredActivity-(Landroid/content/ComponentName; Landroid/content/IntentFilter; Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-approveCaCert-(Ljava/lang/String; I Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-choosePrivateKeyAlias-(I Landroid/net/Uri; Ljava/lang/String; Landroid/os/IBinder;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-clearCrossProfileIntentFilters-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-clearDeviceOwner-(Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-clearPackagePersistentPreferredActivities-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-clearProfileOwner-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-createAndManageUser-(Landroid/content/ComponentName; Ljava/lang/String; Landroid/content/ComponentName; Landroid/os/PersistableBundle; I)Landroid/os/UserHandle;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS", "android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-enableSystemApp-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-enableSystemAppWithIntent-(Landroid/content/ComponentName; Landroid/content/Intent;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-enforceCanManageCaCerts-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-forceRemoveActiveAdmin-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getAccountTypesWithManagementDisabled-()[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getAccountTypesWithManagementDisabledAsUser-(I)[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getActiveAdmins-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getAlwaysOnVpnPackage-(Landroid/content/ComponentName;)Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getApplicationRestrictions-(Landroid/content/ComponentName; Ljava/lang/String;)Landroid/os/Bundle;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getApplicationRestrictionsManagingPackage-(Landroid/content/ComponentName;)Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getAutoTimeRequired-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getBluetoothContactSharingDisabled-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getBluetoothContactSharingDisabledForUser-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCameraDisabled-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCertInstallerPackage-(Landroid/content/ComponentName;)Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileCallerIdDisabled-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileCallerIdDisabledForUser-(I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileContactsSearchDisabled-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileContactsSearchDisabledForUser-(I)Z": ["android.permission.INTERACT_ACROSS_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCrossProfileWidgetProviders-(Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getCurrentFailedPasswordAttempts-(I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getDeviceOwnerComponent-(Z)Landroid/content/ComponentName;": ["android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getDeviceOwnerName-()Ljava/lang/String;": ["android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getDeviceOwnerUserId-()I": ["android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getDoNotAskCredentialsOnBoot-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.QUERY_DO_NOT_ASK_CREDENTIALS_ON_BOOT"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getForceEphemeralUsers-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getGlobalProxyAdmin-(I)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getKeepUninstalledPackages-(Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getKeyguardDisabledFeatures-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getLockTaskPackages-(Landroid/content/ComponentName;)[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getLongSupportMessage-(Landroid/content/ComponentName;)Ljava/lang/CharSequence;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getLongSupportMessageForUser-(Landroid/content/ComponentName; I)Ljava/lang/CharSequence;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getMaximumFailedPasswordsForWipe-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getMaximumTimeToLock-(Landroid/content/ComponentName; I Z)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getMaximumTimeToLockForUserAndProfiles-(I)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getOrganizationColor-(Landroid/content/ComponentName;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getOrganizationColorForUser-(I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getOrganizationName-(Landroid/content/ComponentName;)Ljava/lang/CharSequence;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getOrganizationNameForUser-(I)Ljava/lang/CharSequence;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordExpiration-(Landroid/content/ComponentName; I Z)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordExpirationTimeout-(Landroid/content/ComponentName; I Z)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordHistoryLength-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumLength-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumLetters-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumLowerCase-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumNonLetter-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumNumeric-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumSymbols-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordMinimumUpperCase-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPasswordQuality-(Landroid/content/ComponentName; I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermissionGrantState-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermissionPolicy-(Landroid/content/ComponentName;)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedAccessibilityServices-(Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedAccessibilityServicesForUser-(I)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedInputMethods-(Landroid/content/ComponentName;)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getPermittedInputMethodsForCurrentUser-()Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getProfileOwnerName-(I)Ljava/lang/String;": ["android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getProfileWithMinimumFailedPasswordsForWipe-(I Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getRemoveWarning-(Landroid/content/ComponentName; Landroid/os/RemoteCallback; I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getRequiredStrongAuthTimeout-(Landroid/content/ComponentName; I Z)J": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getRestrictionsProvider-(I)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getScreenCaptureDisabled-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getShortSupportMessage-(Landroid/content/ComponentName;)Ljava/lang/CharSequence;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getShortSupportMessageForUser-(Landroid/content/ComponentName; I)Ljava/lang/CharSequence;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getStorageEncryption-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getStorageEncryptionStatus-(Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getTrustAgentConfiguration-(Landroid/content/ComponentName; Landroid/content/ComponentName; I Z)Ljava/util/List;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getUserProvisioningState-()I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getUserRestrictions-(Landroid/content/ComponentName;)Landroid/os/Bundle;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-getWifiMacAddress-(Landroid/content/ComponentName;)Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-hasGrantedPolicy-(Landroid/content/ComponentName; I I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-hasUserSetupCompleted-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-installCaCert-(Landroid/content/ComponentName; [B)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-installKeyPair-(Landroid/content/ComponentName; [B [B [B Ljava/lang/String; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isAccessibilityServicePermittedByAdmin-(Landroid/content/ComponentName; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isActivePasswordSufficient-(I Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isAdminActive-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isAffiliatedUser-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isApplicationHidden-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isBackupServiceEnabled-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isCaCertApproved-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isCallerApplicationRestrictionsManagingPackage-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isDeviceProvisioningConfigApplied-()Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isInputMethodPermittedByAdmin-(Landroid/content/ComponentName; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isLockTaskPermitted-(Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isManagedProfile-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isMasterVolumeMuted-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isPackageSuspended-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isProfileActivePasswordSufficientForParent-(I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isProvisioningAllowed-(Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isRemovingAdmin-(Landroid/content/ComponentName; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isSecurityLoggingEnabled-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isSystemOnlyUser-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isUninstallBlocked-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-isUninstallInQueue-(Ljava/lang/String;)Z": ["android.permission.MANAGE_DEVICE_ADMINS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-lockNow-(Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-notifyLockTaskModeChanged-(Z Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-notifyPendingSystemUpdate-(J)V": ["android.permission.NOTIFY_PENDING_SYSTEM_UPDATE"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-packageHasActiveAdmins-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reboot-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-removeActiveAdmin-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-removeCrossProfileWidgetProvider-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-removeKeyPair-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-removeUser-(Landroid/content/ComponentName; Landroid/os/UserHandle;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportFailedFingerprintAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportFailedPasswordAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportKeyguardDismissed-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportKeyguardSecured-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportSuccessfulFingerprintAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-reportSuccessfulPasswordAttempt-(I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-requestBugreport-(Landroid/content/ComponentName;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-resetPassword-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-retrievePreRebootSecurityLogs-(Landroid/content/ComponentName;)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-retrieveSecurityLogs-(Landroid/content/ComponentName;)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setAccountManagementDisabled-(Landroid/content/ComponentName; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setActiveAdmin-(Landroid/content/ComponentName; Z I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setActivePasswordState-(I I I I I I I I I)V": ["android.permission.BIND_DEVICE_ADMIN", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setAffiliationIds-(Landroid/content/ComponentName; Ljava/util/List;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setAlwaysOnVpnPackage-(Landroid/content/ComponentName; Ljava/lang/String; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setApplicationHidden-(Landroid/content/ComponentName; Ljava/lang/String; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setApplicationRestrictions-(Landroid/content/ComponentName; Ljava/lang/String; Landroid/os/Bundle;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setApplicationRestrictionsManagingPackage-(Landroid/content/ComponentName; Ljava/lang/String;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setAutoTimeRequired-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setBackupServiceEnabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setBluetoothContactSharingDisabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setCameraDisabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setCertInstallerPackage-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setCrossProfileCallerIdDisabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setCrossProfileContactsSearchDisabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setDeviceOwner-(Landroid/content/ComponentName; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setDeviceOwnerLockScreenInfo-(Landroid/content/ComponentName; Ljava/lang/CharSequence;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setDeviceProvisioningConfigApplied-()V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setForceEphemeralUsers-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setGlobalProxy-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String;)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setGlobalSetting-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setKeepUninstalledPackages-(Landroid/content/ComponentName; Ljava/util/List;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setKeyguardDisabled-(Landroid/content/ComponentName; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setKeyguardDisabledFeatures-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setLockTaskPackages-(Landroid/content/ComponentName; [Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setLongSupportMessage-(Landroid/content/ComponentName; Ljava/lang/CharSequence;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setMasterVolumeMuted-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setMaximumFailedPasswordsForWipe-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setMaximumTimeToLock-(Landroid/content/ComponentName; J Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setOrganizationColor-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setOrganizationColorForUser-(I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setOrganizationName-(Landroid/content/ComponentName; Ljava/lang/CharSequence;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPackagesSuspended-(Landroid/content/ComponentName; [Ljava/lang/String; Z)[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordExpirationTimeout-(Landroid/content/ComponentName; J Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordHistoryLength-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumLength-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumLetters-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumLowerCase-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumNonLetter-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumNumeric-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumSymbols-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordMinimumUpperCase-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPasswordQuality-(Landroid/content/ComponentName; I Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPermissionGrantState-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPermissionPolicy-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPermittedAccessibilityServices-(Landroid/content/ComponentName; Ljava/util/List;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setPermittedInputMethods-(Landroid/content/ComponentName; Ljava/util/List;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setProfileEnabled-(Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setProfileName-(Landroid/content/ComponentName; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setProfileOwner-(Landroid/content/ComponentName; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setRecommendedGlobalProxy-(Landroid/content/ComponentName; Landroid/net/ProxyInfo;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setRequiredStrongAuthTimeout-(Landroid/content/ComponentName; J Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setRestrictionsProvider-(Landroid/content/ComponentName; Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setScreenCaptureDisabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setSecureSetting-(Landroid/content/ComponentName; Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setSecurityLoggingEnabled-(Landroid/content/ComponentName; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setShortSupportMessage-(Landroid/content/ComponentName; Ljava/lang/CharSequence;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setStatusBarDisabled-(Landroid/content/ComponentName; Z)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setStorageEncryption-(Landroid/content/ComponentName; Z)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setSystemUpdatePolicy-(Landroid/content/ComponentName; Landroid/app/admin/SystemUpdatePolicy;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setTrustAgentConfiguration-(Landroid/content/ComponentName; Landroid/content/ComponentName; Landroid/os/PersistableBundle; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setUninstallBlocked-(Landroid/content/ComponentName; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setUserIcon-(Landroid/content/ComponentName; Landroid/graphics/Bitmap;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setUserProvisioningState-(I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_PROFILE_AND_DEVICE_OWNERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-setUserRestriction-(Landroid/content/ComponentName; Ljava/lang/String; Z)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-switchUser-(Landroid/content/ComponentName; Landroid/os/UserHandle;)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-uninstallCaCerts-(Landroid/content/ComponentName; [Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_CA_CERTIFICATES"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-uninstallPackageWithActiveAdmins-(Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_DEVICE_ADMINS", "android.permission.MANAGE_USERS"], "Lcom/android/server/devicepolicy/DevicePolicyManagerService;-wipeData-(I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/display/DisplayManagerService$BinderService;-connectWifiDisplay-(Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-createVirtualDisplay-(Landroid/hardware/display/IVirtualDisplayCallback; Landroid/media/projection/IMediaProjection; Ljava/lang/String; Ljava/lang/String; I I I Landroid/view/Surface; I)I": ["android.permission.CAPTURE_SECURE_VIDEO_OUTPUT", "android.permission.CAPTURE_VIDEO_OUTPUT"], "Lcom/android/server/display/DisplayManagerService$BinderService;-forgetWifiDisplay-(Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-pauseWifiDisplay-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-renameWifiDisplay-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-requestColorMode-(I I)V": ["android.permission.CONFIGURE_DISPLAY_COLOR_MODE"], "Lcom/android/server/display/DisplayManagerService$BinderService;-resumeWifiDisplay-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-startWifiDisplayScan-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/display/DisplayManagerService$BinderService;-stopWifiDisplayScan-()V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-awaken-()V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-dream-()V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-getDefaultDreamComponent-()Landroid/content/ComponentName;": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-getDreamComponents-()[Landroid/content/ComponentName;": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-isDreaming-()Z": ["android.permission.READ_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-setDreamComponents-([Landroid/content/ComponentName;)V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/dreams/DreamManagerService$BinderService;-testDream-(Landroid/content/ComponentName;)V": ["android.permission.WRITE_DREAM_STATE"], "Lcom/android/server/ethernet/EthernetServiceImpl;-addListener-(Landroid/net/IEthernetServiceListener;)V": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ethernet/EthernetServiceImpl;-getConfiguration-()Landroid/net/IpConfiguration;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ethernet/EthernetServiceImpl;-isAvailable-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ethernet/EthernetServiceImpl;-removeListener-(Landroid/net/IEthernetServiceListener;)V": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/ethernet/EthernetServiceImpl;-setConfiguration-(Landroid/net/IpConfiguration;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-authenticate-(Landroid/os/IBinder; J I Landroid/hardware/fingerprint/IFingerprintServiceReceiver; I Ljava/lang/String;)V": ["android.permission.MANAGE_FINGERPRINT", "android.permission.USE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-cancelAuthentication-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.USE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-cancelEnrollment-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-enroll-(Landroid/os/IBinder; [B I Landroid/hardware/fingerprint/IFingerprintServiceReceiver; I Ljava/lang/String;)V": ["android.permission.MANAGE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-getEnrolledFingerprints-(I Ljava/lang/String;)Ljava/util/List;": ["android.permission.USE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-hasEnrolledFingerprints-(I Ljava/lang/String;)Z": ["android.permission.USE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-isHardwareDetected-(J Ljava/lang/String;)Z": ["android.permission.USE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-postEnroll-(Landroid/os/IBinder;)I": ["android.permission.MANAGE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-preEnroll-(Landroid/os/IBinder;)J": ["android.permission.MANAGE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-remove-(Landroid/os/IBinder; I I I Landroid/hardware/fingerprint/IFingerprintServiceReceiver;)V": ["android.permission.MANAGE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-rename-(I I Ljava/lang/String;)V": ["android.permission.MANAGE_FINGERPRINT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-resetTimeout-([B)V": ["android.permission.RESET_FINGERPRINT_LOCKOUT"], "Lcom/android/server/fingerprint/FingerprintService$FingerprintServiceWrapper;-setActiveUser-(I)V": ["android.permission.MANAGE_FINGERPRINT"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addDeviceEventListener-(Landroid/hardware/hdmi/IHdmiDeviceEventListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addHdmiMhlVendorCommandListener-(Landroid/hardware/hdmi/IHdmiMhlVendorCommandListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addHotplugEventListener-(Landroid/hardware/hdmi/IHdmiHotplugEventListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addSystemAudioModeChangeListener-(Landroid/hardware/hdmi/IHdmiSystemAudioModeChangeListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-addVendorCommandListener-(Landroid/hardware/hdmi/IHdmiVendorCommandListener; I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-canChangeSystemAudioMode-()Z": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-clearTimerRecording-(I I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-deviceSelect-(I Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getActiveSource-()Landroid/hardware/hdmi/HdmiDeviceInfo;": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getDeviceList-()Ljava/util/List;": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getInputDevices-()Ljava/util/List;": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getPortInfo-()Ljava/util/List;": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getSupportedTypes-()[I": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-getSystemAudioMode-()Z": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-oneTouchPlay-(Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-portSelect-(I Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-queryDisplayStatus-(Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-removeHotplugEventListener-(Landroid/hardware/hdmi/IHdmiHotplugEventListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-removeSystemAudioModeChangeListener-(Landroid/hardware/hdmi/IHdmiSystemAudioModeChangeListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendKeyEvent-(I I Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendMhlVendorCommand-(I I I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendStandby-(I I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-sendVendorCommand-(I I [B Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setArcMode-(Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setHdmiRecordListener-(Landroid/hardware/hdmi/IHdmiRecordListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setInputChangeListener-(Landroid/hardware/hdmi/IHdmiInputChangeListener;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setProhibitMode-(Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setSystemAudioMode-(Z Landroid/hardware/hdmi/IHdmiControlCallback;)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setSystemAudioMute-(Z)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-setSystemAudioVolume-(I I I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-startOneTouchRecord-(I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-startTimerRecording-(I I [B)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/hdmi/HdmiControlService$BinderService;-stopOneTouchRecord-(I)V": ["android.permission.HDMI_CEC"], "Lcom/android/server/input/InputManagerService;-addKeyboardLayoutForInputDevice-(Landroid/hardware/input/InputDeviceIdentifier; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-isInTabletMode-()I": ["android.permission.TABLET_MODE"], "Lcom/android/server/input/InputManagerService;-registerTabletModeChangedListener-(Landroid/hardware/input/ITabletModeChangedListener;)V": ["android.permission.TABLET_MODE"], "Lcom/android/server/input/InputManagerService;-removeKeyboardLayoutForInputDevice-(Landroid/hardware/input/InputDeviceIdentifier; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-setCurrentKeyboardLayoutForInputDevice-(Landroid/hardware/input/InputDeviceIdentifier; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-setKeyboardLayoutForInputDevice-(Landroid/hardware/input/InputDeviceIdentifier; Landroid/view/inputmethod/InputMethodInfo; Landroid/view/inputmethod/InputMethodSubtype; Ljava/lang/String;)V": ["android.permission.SET_KEYBOARD_LAYOUT"], "Lcom/android/server/input/InputManagerService;-setTouchCalibrationForInputDevice-(Ljava/lang/String; I Landroid/hardware/input/TouchCalibration;)V": ["android.permission.SET_INPUT_CALIBRATION"], "Lcom/android/server/input/InputManagerService;-tryPointerSpeed-(I)V": ["android.permission.SET_POINTER_SPEED"], "Lcom/android/server/job/JobSchedulerService$JobSchedulerStub;-schedule-(Landroid/app/job/JobInfo;)I": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.RECEIVE_BOOT_COMPLETED"], "Lcom/android/server/job/JobSchedulerService$JobSchedulerStub;-scheduleAsPackage-(Landroid/app/job/JobInfo; Ljava/lang/String; I Ljava/lang/String;)I": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/media/MediaRouterService;-registerClientAsUser-(Landroid/media/IMediaRouterClient; Ljava/lang/String; I)V": ["android.permission.CONFIGURE_WIFI_DISPLAY"], "Lcom/android/server/media/MediaSessionRecord$SessionStub;-setFlags-(I)V": ["android.permission.MODIFY_PHONE_STATE"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-addCallback-(Landroid/media/projection/IMediaProjectionWatcherCallback;)V": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-createProjection-(I Ljava/lang/String; I Z)Landroid/media/projection/IMediaProjection;": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-getActiveProjectionInfo-()Landroid/media/projection/MediaProjectionInfo;": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-removeCallback-(Landroid/media/projection/IMediaProjectionWatcherCallback;)V": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/media/projection/MediaProjectionManagerService$BinderService;-stopActiveProjection-()V": ["android.permission.MANAGE_MEDIA_PROJECTION"], "Lcom/android/server/net/NetworkPolicyManagerService;-addRestrictBackgroundWhitelistedUid-(I)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-addUidPolicy-(I I)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-factoryReset-(Ljava/lang/String;)V": ["android.permission.CONNECTIVITY_INTERNAL", "android.permission.MANAGE_NETWORK_POLICY", "android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkPolicies-(Ljava/lang/String;)[Landroid/net/NetworkPolicy;": ["android.permission.MANAGE_NETWORK_POLICY", "android.permission.READ_PRIVILEGED_PHONE_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getNetworkQuotaInfo-(Landroid/net/NetworkState;)Landroid/net/NetworkQuotaInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getRestrictBackground-()Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getRestrictBackgroundByCaller-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkPolicyManagerService;-getRestrictBackgroundWhitelistedUids-()[I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getUidPolicy-(I)I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-getUidsWithPolicy-(I)[I": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-isUidForeground-(I)Z": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-onTetheringChanged-(Ljava/lang/String; Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-registerListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkPolicyManagerService;-removeRestrictBackgroundWhitelistedUid-(I)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-removeUidPolicy-(I I)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setConnectivityListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkPolicyManagerService;-setDeviceIdleMode-(Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setNetworkPolicies-([Landroid/net/NetworkPolicy;)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setRestrictBackground-(Z)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-setUidPolicy-(I I)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-snoozeLimit-(Landroid/net/NetworkTemplate;)V": ["android.permission.MANAGE_NETWORK_POLICY"], "Lcom/android/server/net/NetworkPolicyManagerService;-unregisterListener-(Landroid/net/INetworkPolicyListener;)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/net/NetworkStatsService;-advisePersistThreshold-(J)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-forceUpdate-()V": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-forceUpdateIfaces-()V": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-getDataLayerSnapshotForUid-(I)Landroid/net/NetworkStats;": ["android.permission.ACCESS_NETWORK_STATE"], "Lcom/android/server/net/NetworkStatsService;-getNetworkTotalBytes-(Landroid/net/NetworkTemplate; J J)J": ["android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-incrementOperationCount-(I I I)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/net/NetworkStatsService;-registerUsageCallback-(Ljava/lang/String; Landroid/net/DataUsageRequest; Landroid/os/Messenger; Landroid/os/IBinder;)Landroid/net/DataUsageRequest;": ["android.permission.PACKAGE_USAGE_STATS", "android.permission.READ_NETWORK_USAGE_HISTORY"], "Lcom/android/server/net/NetworkStatsService;-setUidForeground-(I Z)V": ["android.permission.MODIFY_NETWORK_ACCOUNTING"], "Lcom/android/server/pm/PackageInstallerService;-createSession-(Landroid/content/pm/PackageInstaller$SessionParams; Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageInstallerService;-getAllSessions-(I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageInstallerService;-getMySessions-(Ljava/lang/String; I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageInstallerService;-registerCallback-(Landroid/content/pm/IPackageInstallerCallback; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageInstallerService;-setPermissionsResult-(I Z)V": ["android.permission.INSTALL_PACKAGES"], "Lcom/android/server/pm/PackageInstallerService;-uninstall-(Ljava/lang/String; Ljava/lang/String; I Landroid/content/IntentSender; I)V": ["android.permission.DELETE_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-addCrossProfileIntentFilter-(Landroid/content/IntentFilter; Ljava/lang/String; I I I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-addOnPermissionsChangeListener-(Landroid/content/pm/IOnPermissionsChangeListener;)V": ["android.permission.OBSERVE_GRANT_REVOKE_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-addPreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-canForwardTo-(Landroid/content/Intent; Ljava/lang/String; I I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-clearApplicationUserData-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver; I)V": ["android.permission.CLEAR_APP_USER_DATA", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-clearCrossProfileIntentFilters-(I Ljava/lang/String;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-clearPackagePreferredActivities-(Ljava/lang/String;)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-deleteApplicationCacheFiles-(Ljava/lang/String; Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.DELETE_CACHE_FILES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-deleteApplicationCacheFilesAsUser-(Ljava/lang/String; I Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.DELETE_CACHE_FILES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-deletePackage-(Ljava/lang/String; Landroid/content/pm/IPackageDeleteObserver2; I I)V": ["android.permission.DELETE_PACKAGES", "android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-deletePackageAsUser-(Ljava/lang/String; Landroid/content/pm/IPackageDeleteObserver; I I)V": ["android.permission.DELETE_PACKAGES", "android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-extendVerificationTimeout-(I I J)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.PACKAGE_VERIFICATION_AGENT", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-flushPackageRestrictionsAsUser-(I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-freeStorage-(Ljava/lang/String; J Landroid/content/IntentSender;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-freeStorageAndNotify-(Ljava/lang/String; J Landroid/content/pm/IPackageDataObserver;)V": ["android.permission.CLEAR_APP_CACHE"], "Lcom/android/server/pm/PackageManagerService;-getActivityInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ActivityInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getApplicationEnabledSetting-(Ljava/lang/String; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getApplicationHiddenSettingAsUser-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/pm/PackageManagerService;-getApplicationInfo-(Ljava/lang/String; I I)Landroid/content/pm/ApplicationInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getComponentEnabledSetting-(Landroid/content/ComponentName; I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getHomeActivities-(Ljava/util/List;)Landroid/content/ComponentName;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getInstalledPackages-(I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getLastChosenActivity-(Landroid/content/Intent; Ljava/lang/String; I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getMoveStatus-(I)I": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/pm/PackageManagerService;-getPackageGids-(Ljava/lang/String; I I)[I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getPackageInfo-(Ljava/lang/String; I I)Landroid/content/pm/PackageInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getPackageSizeInfo-(Ljava/lang/String; I Landroid/content/pm/IPackageStatsObserver;)V": ["android.permission.GET_PACKAGE_SIZE", "android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-getPackageUid-(Ljava/lang/String; I I)I": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getPermissionFlags-(Ljava/lang/String; Ljava/lang/String; I)I": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-getProviderInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ProviderInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getReceiverInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ActivityInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getServiceInfo-(Landroid/content/ComponentName; I I)Landroid/content/pm/ServiceInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-getVerifierDeviceIdentity-()Landroid/content/pm/VerifierDeviceIdentity;": ["android.permission.PACKAGE_VERIFICATION_AGENT"], "Lcom/android/server/pm/PackageManagerService;-grantRuntimePermission-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-installExistingPackageAsUser-(Ljava/lang/String; I)I": ["android.permission.INSTALL_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-installPackageAsUser-(Ljava/lang/String; Landroid/content/pm/IPackageInstallObserver2; I Ljava/lang/String; I)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INSTALL_GRANT_RUNTIME_PERMISSIONS", "android.permission.INSTALL_PACKAGES", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-isEphemeralApplication-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-isPackageAvailable-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-isPackageSuspendedForUser-(Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-isPermissionRevokedByPolicy-(Ljava/lang/String; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-movePackage-(Ljava/lang/String; Ljava/lang/String;)I": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MOVE_PACKAGE", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-movePrimaryStorage-(Ljava/lang/String;)I": ["android.permission.MOVE_PACKAGE"], "Lcom/android/server/pm/PackageManagerService;-queryIntentActivities-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentActivityOptions-(Landroid/content/ComponentName; [Landroid/content/Intent; [Ljava/lang/String; Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentContentProviders-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentReceivers-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-queryIntentServices-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ParceledListSlice;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-registerMoveCallback-(Landroid/content/pm/IPackageMoveObserver;)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/pm/PackageManagerService;-replacePreferredActivity-(Landroid/content/IntentFilter; I [Landroid/content/ComponentName; Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-resetApplicationPreferences-(I)V": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-resetRuntimePermissions-()V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-resolveIntent-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-resolveService-(Landroid/content/Intent; Ljava/lang/String; I I)Landroid/content/pm/ResolveInfo;": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-revokeRuntimePermission-(Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-setApplicationEnabledSetting-(Ljava/lang/String; I I I Ljava/lang/String;)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setApplicationHiddenSettingAsUser-(Ljava/lang/String; Z I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/pm/PackageManagerService;-setBlockUninstallForUser-(Ljava/lang/String; Z I)Z": ["android.permission.DELETE_PACKAGES"], "Lcom/android/server/pm/PackageManagerService;-setComponentEnabledSetting-(Landroid/content/ComponentName; I I I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setDefaultBrowserPackageName-(Ljava/lang/String; I)Z": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-setHomeActivity-(Landroid/content/ComponentName; I)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-setInstallLocation-(I)Z": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/pm/PackageManagerService;-setLastChosenActivity-(Landroid/content/Intent; Ljava/lang/String; I Landroid/content/IntentFilter; I Landroid/content/ComponentName;)V": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-setPackageStoppedState-(Ljava/lang/String; Z I)V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-setPackagesSuspendedAsUser-([Ljava/lang/String; Z I)[Ljava/lang/String;": ["android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.MANAGE_USERS"], "Lcom/android/server/pm/PackageManagerService;-setPermissionEnforced-(Ljava/lang/String; Z)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-shouldShowRequestPermissionRationale-(Ljava/lang/String; Ljava/lang/String; I)Z": ["android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-systemReady-()V": ["android.permission.CHANGE_COMPONENT_ENABLED_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL"], "Lcom/android/server/pm/PackageManagerService;-unregisterMoveCallback-(Landroid/content/pm/IPackageMoveObserver;)V": ["android.permission.MOUNT_UNMOUNT_FILESYSTEMS"], "Lcom/android/server/pm/PackageManagerService;-updateExternalMediaStatus-(Z Z)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-updateIntentVerificationStatus-(Ljava/lang/String; I I)Z": ["android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/PackageManagerService;-updatePermissionFlags-(Ljava/lang/String; Ljava/lang/String; I I I)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-updatePermissionFlagsForAllApps-(I I I)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.REVOKE_RUNTIME_PERMISSIONS"], "Lcom/android/server/pm/PackageManagerService;-verifyIntentFilter-(I I Ljava/util/List;)V": ["android.permission.INTENT_FILTER_VERIFICATION_AGENT"], "Lcom/android/server/pm/PackageManagerService;-verifyPendingInstall-(I I)V": ["android.permission.GRANT_RUNTIME_PERMISSIONS", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.PACKAGE_VERIFICATION_AGENT", "android.permission.REVOKE_RUNTIME_PERMISSIONS", "android.permission.SET_PREFERRED_APPLICATIONS"], "Lcom/android/server/pm/ShortcutService;-onApplicationActive-(Ljava/lang/String; I)V": ["android.permission.RESET_SHORTCUT_MANAGER_THROTTLING"], "Lcom/android/server/power/PowerManagerService$BinderService;-acquireWakeLock-(Landroid/os/IBinder; I Ljava/lang/String; Ljava/lang/String; Landroid/os/WorkSource; Ljava/lang/String;)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-acquireWakeLockWithUid-(Landroid/os/IBinder; I Ljava/lang/String; Ljava/lang/String; I)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-boostScreenBrightness-(J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-crash-(Ljava/lang/String;)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService$BinderService;-goToSleep-(J I I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-nap-(J)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-powerHint-(I I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-reboot-(Z Ljava/lang/String; Z)V": ["android.permission.REBOOT", "android.permission.RECOVERY"], "Lcom/android/server/power/PowerManagerService$BinderService;-rebootSafeMode-(Z Z)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService$BinderService;-releaseWakeLock-(Landroid/os/IBinder; I)V": ["android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-setAttentionLight-(Z I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-setPowerSaveMode-(Z)Z": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-setTemporaryScreenAutoBrightnessAdjustmentSettingOverride-(F)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-setTemporaryScreenBrightnessSettingOverride-(I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-shutdown-(Z Ljava/lang/String; Z)V": ["android.permission.REBOOT"], "Lcom/android/server/power/PowerManagerService$BinderService;-updateWakeLockUids-(Landroid/os/IBinder; [I)V": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-updateWakeLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource; Ljava/lang/String;)V": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/power/PowerManagerService$BinderService;-userActivity-(J I I)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/power/PowerManagerService$BinderService;-wakeUp-(J Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-addPrintJobStateChangeListener-(Landroid/print/IPrintJobStateChangeListener; I I)V": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-cancelPrintJob-(Landroid/print/PrintJobId; I I)V": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-getPrintJobInfo-(Landroid/print/PrintJobId; I I)Landroid/print/PrintJobInfo;": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-getPrintJobInfos-(I I)Ljava/util/List;": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-print-(Ljava/lang/String; Landroid/print/IPrintDocumentAdapter; Landroid/print/PrintAttributes; Ljava/lang/String; I I)Landroid/os/Bundle;": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/print/PrintManagerService$PrintManagerImpl;-restartPrintJob-(Landroid/print/PrintJobId; I I)V": ["com.android.printspooler.permission.ACCESS_ALL_PRINT_JOBS"], "Lcom/android/server/sip/SipService;-close-(Ljava/lang/String; Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-createSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/ISipSessionListener; Ljava/lang/String;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getListOfProfiles-(Ljava/lang/String;)[Landroid/net/sip/SipProfile;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-getPendingSession-(Ljava/lang/String; Ljava/lang/String;)Landroid/net/sip/ISipSession;": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isOpened-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-isRegistered-(Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open-(Landroid/net/sip/SipProfile; Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-open3-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/ISipSessionListener; Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Lcom/android/server/sip/SipService;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/ISipSessionListener; Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Lcom/android/server/soundtrigger/SoundTriggerService$SoundTriggerServiceStub;-deleteSoundModel-(Landroid/os/ParcelUuid;)V": ["android.permission.MANAGE_SOUND_TRIGGER"], "Lcom/android/server/soundtrigger/SoundTriggerService$SoundTriggerServiceStub;-getSoundModel-(Landroid/os/ParcelUuid;)Landroid/hardware/soundtrigger/SoundTrigger$GenericSoundModel;": ["android.permission.MANAGE_SOUND_TRIGGER"], "Lcom/android/server/soundtrigger/SoundTriggerService$SoundTriggerServiceStub;-startRecognition-(Landroid/os/ParcelUuid; Landroid/hardware/soundtrigger/IRecognitionStatusCallback; Landroid/hardware/soundtrigger/SoundTrigger$RecognitionConfig;)I": ["android.permission.MANAGE_SOUND_TRIGGER"], "Lcom/android/server/soundtrigger/SoundTriggerService$SoundTriggerServiceStub;-stopRecognition-(Landroid/os/ParcelUuid; Landroid/hardware/soundtrigger/IRecognitionStatusCallback;)I": ["android.permission.MANAGE_SOUND_TRIGGER"], "Lcom/android/server/soundtrigger/SoundTriggerService$SoundTriggerServiceStub;-updateSoundModel-(Landroid/hardware/soundtrigger/SoundTrigger$GenericSoundModel;)V": ["android.permission.MANAGE_SOUND_TRIGGER"], "Lcom/android/server/statusbar/StatusBarManagerService;-addTile-(Landroid/content/ComponentName;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-clearNotificationEffects-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-clickTile-(Landroid/content/ComponentName;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-collapsePanels-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-disable-(I Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-disable2-(I Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-disable2ForUser-(I Landroid/os/IBinder; Ljava/lang/String; I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-disableForUser-(I Landroid/os/IBinder; Ljava/lang/String; I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-expandNotificationsPanel-()V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-expandSettingsPanel-(Ljava/lang/String;)V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-handleSystemNavigationKey-(I)V": ["android.permission.EXPAND_STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-onClearAllNotifications-(I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationActionClick-(Ljava/lang/String; I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationClear-(Ljava/lang/String; Ljava/lang/String; I I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationClick-(Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationError-(Ljava/lang/String; Ljava/lang/String; I I I Ljava/lang/String; I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationExpansionChanged-(Ljava/lang/String; Z Z)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onNotificationVisibilityChanged-([Lcom/android/internal/statusbar/NotificationVisibility; [Lcom/android/internal/statusbar/NotificationVisibility;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onPanelHidden-()V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-onPanelRevealed-(Z I)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-registerStatusBar-(Lcom/android/internal/statusbar/IStatusBar; Ljava/util/List; Ljava/util/List; [I Ljava/util/List; Landroid/graphics/Rect; Landroid/graphics/Rect;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/statusbar/StatusBarManagerService;-remTile-(Landroid/content/ComponentName;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-removeIcon-(Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setIcon-(Ljava/lang/String; Ljava/lang/String; I I Ljava/lang/String;)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setIconVisibility-(Ljava/lang/String; Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setImeWindowStatus-(Landroid/os/IBinder; I I Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/statusbar/StatusBarManagerService;-setSystemUiVisibility-(I I Ljava/lang/String;)V": ["android.permission.STATUS_BAR_SERVICE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-acquireTvInputHardware-(I Landroid/media/tv/ITvInputHardwareCallback; Landroid/media/tv/TvInputInfo; I)Landroid/media/tv/ITvInputHardware;": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-addBlockedRating-(Ljava/lang/String; I)V": ["android.permission.MODIFY_PARENTAL_CONTROLS"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-captureFrame-(Ljava/lang/String; Landroid/view/Surface; Landroid/media/tv/TvStreamConfig; I)Z": ["android.permission.CAPTURE_TV_INPUT"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-getAvailableTvStreamConfigList-(Ljava/lang/String; I)Ljava/util/List;": ["android.permission.CAPTURE_TV_INPUT"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-getDvbDeviceList-()Ljava/util/List;": ["android.permission.DVB_DEVICE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-getHardwareList-()Ljava/util/List;": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-openDvbDevice-(Landroid/media/tv/DvbDeviceInfo; I)Landroid/os/ParcelFileDescriptor;": ["android.permission.DVB_DEVICE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-releaseTvInputHardware-(I Landroid/media/tv/ITvInputHardware; I)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-removeBlockedRating-(Ljava/lang/String; I)V": ["android.permission.MODIFY_PARENTAL_CONTROLS"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-setParentalControlsEnabled-(Z I)V": ["android.permission.MODIFY_PARENTAL_CONTROLS"], "Lcom/android/server/tv/TvInputManagerService$BinderService;-unblockContent-(Landroid/os/IBinder; Ljava/lang/String; I)V": ["android.permission.MODIFY_PARENTAL_CONTROLS"], "Lcom/android/server/tv/TvInputManagerService$ServiceCallback;-addHardwareInput-(I Landroid/media/tv/TvInputInfo;)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$ServiceCallback;-addHdmiInput-(I Landroid/media/tv/TvInputInfo;)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/tv/TvInputManagerService$ServiceCallback;-removeHardwareInput-(Ljava/lang/String;)V": ["android.permission.TV_INPUT_HARDWARE"], "Lcom/android/server/usage/UsageStatsService$BinderService;-onCarrierPrivilegedAppsChanged-()V": ["android.permission.BIND_CARRIER_SERVICES"], "Lcom/android/server/usage/UsageStatsService$BinderService;-queryConfigurationStats-(I J J Ljava/lang/String;)Landroid/content/pm/ParceledListSlice;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/usage/UsageStatsService$BinderService;-queryEvents-(J J Ljava/lang/String;)Landroid/app/usage/UsageEvents;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/usage/UsageStatsService$BinderService;-queryUsageStats-(I J J Ljava/lang/String;)Landroid/content/pm/ParceledListSlice;": ["android.permission.PACKAGE_USAGE_STATS"], "Lcom/android/server/usage/UsageStatsService$BinderService;-setAppInactive-(Ljava/lang/String; Z I)V": ["android.permission.CHANGE_APP_IDLE_STATE"], "Lcom/android/server/usb/UsbService;-allowUsbDebugging-(Z Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-clearDefaults-(Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-clearUsbDebuggingKeys-()V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-denyUsbDebugging-()V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-getPortStatus-(Ljava/lang/String;)Landroid/hardware/usb/UsbPortStatus;": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-getPorts-()[Landroid/hardware/usb/UsbPort;": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantAccessoryPermission-(Landroid/hardware/usb/UsbAccessory; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-grantDevicePermission-(Landroid/hardware/usb/UsbDevice; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-hasDefaults-(Ljava/lang/String; I)Z": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-isFunctionEnabled-(Ljava/lang/String;)Z": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setAccessoryPackage-(Landroid/hardware/usb/UsbAccessory; Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setCurrentFunction-(Ljava/lang/String;)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setDevicePackage-(Landroid/hardware/usb/UsbDevice; Ljava/lang/String; I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setPortRoles-(Ljava/lang/String; I I)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/usb/UsbService;-setUsbDataUnlocked-(Z)V": ["android.permission.MANAGE_USB"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-activeServiceSupportsAssist-()Z": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-activeServiceSupportsLaunchFromKeyguard-()Z": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-deleteKeyphraseSoundModel-(I Ljava/lang/String;)I": ["android.permission.MANAGE_VOICE_KEYPHRASES"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-getActiveServiceComponentName-()Landroid/content/ComponentName;": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-getKeyphraseSoundModel-(I Ljava/lang/String;)Landroid/hardware/soundtrigger/SoundTrigger$KeyphraseSoundModel;": ["android.permission.MANAGE_VOICE_KEYPHRASES"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-hideCurrentSession-()V": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-isSessionRunning-()Z": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-launchVoiceAssistFromKeyguard-()V": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-onLockscreenShown-()V": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-registerVoiceInteractionSessionListener-(Lcom/android/internal/app/IVoiceInteractionSessionListener;)V": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-showSessionForActiveService-(Landroid/os/Bundle; I Lcom/android/internal/app/IVoiceInteractionSessionShowCallback; Landroid/os/IBinder;)Z": ["android.permission.ACCESS_VOICE_INTERACTION_SERVICE"], "Lcom/android/server/voiceinteraction/VoiceInteractionManagerService$VoiceInteractionManagerServiceStub;-updateKeyphraseSoundModel-(Landroid/hardware/soundtrigger/SoundTrigger$KeyphraseSoundModel;)I": ["android.permission.MANAGE_VOICE_KEYPHRASES"], "Lcom/android/server/wallpaper/WallpaperManagerService;-clearWallpaper-(Ljava/lang/String; I I)V": ["android.permission.SET_WALLPAPER"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setDimensionHints-(I I Ljava/lang/String;)V": ["android.permission.SET_WALLPAPER_HINTS"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setDisplayPadding-(Landroid/graphics/Rect; Ljava/lang/String;)V": ["android.permission.SET_WALLPAPER_HINTS"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setLockWallpaperCallback-(Landroid/app/IWallpaperManagerCallback;)Z": ["android.permission.INTERNAL_SYSTEM_WINDOW"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setWallpaper-(Ljava/lang/String; Ljava/lang/String; Landroid/graphics/Rect; Z Landroid/os/Bundle; I Landroid/app/IWallpaperManagerCallback; I)Landroid/os/ParcelFileDescriptor;": ["android.permission.SET_WALLPAPER"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setWallpaperComponent-(Landroid/content/ComponentName;)V": ["android.permission.SET_WALLPAPER_COMPONENT"], "Lcom/android/server/wallpaper/WallpaperManagerService;-setWallpaperComponentChecked-(Landroid/content/ComponentName; Ljava/lang/String; I)V": ["android.permission.SET_WALLPAPER_COMPONENT"], "Lcom/android/server/webkit/WebViewUpdateService$BinderService;-changeProviderAndSetting-(Ljava/lang/String;)Ljava/lang/String;": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/webkit/WebViewUpdateService$BinderService;-enableFallbackLogic-(Z)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wifi/WifiServiceImpl;-acquireMulticastLock-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-acquireWifiLock-(Landroid/os/IBinder; I Ljava/lang/String; Landroid/os/WorkSource;)Z": ["android.permission.UPDATE_DEVICE_STATS", "android.permission.WAKE_LOCK"], "Lcom/android/server/wifi/WifiServiceImpl;-addOrUpdateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-addToBlacklist-(Ljava/lang/String;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-clearBlacklist-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-disableEphemeralNetwork-(Ljava/lang/String;)V": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-disconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-enableAggressiveHandover-(I)V": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-enableVerboseLogging-(I)V": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-enableWifiConnectivityManager-(Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-factoryReset-()V": ["android.permission.CHANGE_WIFI_STATE", "android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-getAggressiveHandover-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getAllowScansWithTraffic-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConfigFile-()Ljava/lang/String;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getConnectionStatistics-()Landroid/net/wifi/WifiConnectionStatistics;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.READ_WIFI_CREDENTIAL"], "Lcom/android/server/wifi/WifiServiceImpl;-getCountryCode-()Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-getCurrentNetwork-()Landroid/net/Network;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getEnableAutoJoinWhenAssociated-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getFrequencyBand-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getMatchingWifiConfig-(Landroid/net/wifi/ScanResult;)Landroid/net/wifi/WifiConfiguration;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getPrivilegedConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.READ_WIFI_CREDENTIAL"], "Lcom/android/server/wifi/WifiServiceImpl;-getScanResults-(Ljava/lang/String;)Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.INTERACT_ACROSS_USERS_FULL", "android.permission.PEERS_MAC_ADDRESS", "android.permission.SCORE_NETWORKS"], "Lcom/android/server/wifi/WifiServiceImpl;-getSupportedFeatures-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getVerboseLoggingLevel-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiApConfiguration-()Landroid/net/wifi/WifiConfiguration;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiApEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiEnabledState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWifiServiceMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-getWpsNfcConfigurationToken-(I)Ljava/lang/String;": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-initializeMulticastFiltering-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-isMulticastEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-isScanAlwaysAvailable-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-reassociate-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-reconnect-()V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-releaseMulticastLock-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-releaseWifiLock-(Landroid/os/IBinder;)Z": ["android.permission.WAKE_LOCK"], "Lcom/android/server/wifi/WifiServiceImpl;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-reportActivityInfo-()Landroid/net/wifi/WifiActivityEnergyInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-requestActivityInfo-(Landroid/os/ResultReceiver;)V": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setAllowScansWithTraffic-(I)V": ["android.permission.ACCESS_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setCountryCode-(Ljava/lang/String; Z)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wifi/WifiServiceImpl;-setEnableAutoJoinWhenAssociated-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setFrequencyBand-(I Z)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setWifiApConfiguration-(Landroid/net/wifi/WifiConfiguration;)V": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-setWifiApEnabled-(Landroid/net/wifi/WifiConfiguration; Z)V": ["android.permission.CHANGE_WIFI_STATE", "android.permission.TETHER_PRIVILEGED"], "Lcom/android/server/wifi/WifiServiceImpl;-setWifiEnabled-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/WifiServiceImpl;-startScan-(Landroid/net/wifi/ScanSettings; Landroid/os/WorkSource;)V": ["android.permission.CHANGE_WIFI_STATE", "android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wifi/WifiServiceImpl;-updateWifiLockWorkSource-(Landroid/os/IBinder; Landroid/os/WorkSource;)V": ["android.permission.UPDATE_DEVICE_STATS"], "Lcom/android/server/wifi/p2p/WifiP2pServiceImpl;-getMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Lcom/android/server/wifi/p2p/WifiP2pServiceImpl;-getP2pStateMachineMessenger-()Landroid/os/Messenger;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE", "android.permission.CONNECTIVITY_INTERNAL", "android.permission.LOCATION_HARDWARE"], "Lcom/android/server/wifi/p2p/WifiP2pServiceImpl;-setMiracastMode-(I)V": ["android.permission.CONNECTIVITY_INTERNAL"], "Lcom/android/server/wm/WindowManagerService;-addAppToken-(I Landroid/view/IApplicationToken; I I I Z Z I I Z Z Landroid/graphics/Rect; Landroid/content/res/Configuration; I Z Z I I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-addWindowToken-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-clearForcedDisplayDensityForUser-(I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-clearForcedDisplaySize-(I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-clearWindowContentFrameStats-(Landroid/os/IBinder;)Z": ["android.permission.FRAME_STATS"], "Lcom/android/server/wm/WindowManagerService;-disableKeyguard-(Landroid/os/IBinder; Ljava/lang/String;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-dismissKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-executeAppTransition-()V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-exitKeyguardSecurely-(Landroid/view/IOnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-freezeRotation-(I)V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-getWindowContentFrameStats-(Landroid/os/IBinder;)Landroid/view/WindowContentFrameStats;": ["android.permission.FRAME_STATS"], "Lcom/android/server/wm/WindowManagerService;-isViewServerRunning-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-keyguardGoingAway-(I)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-lockNow-(Landroid/os/Bundle;)V": ["android.permission.DEVICE_POWER"], "Lcom/android/server/wm/WindowManagerService;-notifyAppResumed-(Landroid/os/IBinder; Z Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-notifyAppStopped-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-pauseKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-prepareAppTransition-(I Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-reenableKeyguard-(Landroid/os/IBinder;)V": ["android.permission.DISABLE_KEYGUARD"], "Lcom/android/server/wm/WindowManagerService;-registerDockedStackListener-(Landroid/view/IDockedStackListener;)V": ["android.permission.REGISTER_WINDOW_MANAGER_LISTENERS"], "Lcom/android/server/wm/WindowManagerService;-registerShortcutKey-(J Lcom/android/internal/policy/IShortcutService;)V": ["android.permission.REGISTER_WINDOW_MANAGER_LISTENERS"], "Lcom/android/server/wm/WindowManagerService;-removeAppToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-removeWindowToken-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-requestAssistScreenshot-(Lcom/android/internal/app/IAssistScreenshotReceiver;)Z": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/wm/WindowManagerService;-resumeKeyDispatching-(Landroid/os/IBinder;)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-screenshotApplications-(Landroid/os/IBinder; I I I F)Landroid/graphics/Bitmap;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/wm/WindowManagerService;-screenshotWallpaper-()Landroid/graphics/Bitmap;": ["android.permission.READ_FRAME_BUFFER"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScale-(I F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAnimationScales-([F)V": ["android.permission.SET_ANIMATION_SCALE"], "Lcom/android/server/wm/WindowManagerService;-setAppOrientation-(Landroid/view/IApplicationToken; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppStartingWindow-(Landroid/os/IBinder; Ljava/lang/String; I Landroid/content/res/CompatibilityInfo; Ljava/lang/CharSequence; I I I I Landroid/os/IBinder; Z)Z": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppTask-(Landroid/os/IBinder; I I Landroid/graphics/Rect; Landroid/content/res/Configuration; I Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setAppVisibility-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setEventDispatching-(Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setFocusedApp-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setForcedDisplayDensityForUser-(I I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setForcedDisplayScalingMode-(I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setForcedDisplaySize-(I I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setNewConfiguration-(Landroid/content/res/Configuration;)[I": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-setOverscan-(I I I I I)V": ["android.permission.WRITE_SECURE_SETTINGS"], "Lcom/android/server/wm/WindowManagerService;-setRecentsVisibility-(Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/wm/WindowManagerService;-setTvPipVisibility-(Z)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/wm/WindowManagerService;-startAppFreezingScreen-(Landroid/os/IBinder; I)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-startFreezingScreen-(I I)V": ["android.permission.FREEZE_SCREEN"], "Lcom/android/server/wm/WindowManagerService;-startViewServer-(I)Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-statusBarVisibilityChanged-(I)V": ["android.permission.STATUS_BAR"], "Lcom/android/server/wm/WindowManagerService;-stopAppFreezingScreen-(Landroid/os/IBinder; Z)V": ["android.permission.MANAGE_APP_TOKENS"], "Lcom/android/server/wm/WindowManagerService;-stopFreezingScreen-()V": ["android.permission.FREEZE_SCREEN"], "Lcom/android/server/wm/WindowManagerService;-stopViewServer-()Z": ["android.permission.DUMP"], "Lcom/android/server/wm/WindowManagerService;-thawRotation-()V": ["android.permission.SET_ORIENTATION"], "Lcom/android/server/wm/WindowManagerService;-updateOrientationFromAppTokens-(Landroid/content/res/Configuration; Landroid/os/IBinder;)Landroid/content/res/Configuration;": ["android.permission.MANAGE_APP_TOKENS"], "Landroid/accounts/AccountAuthenticatorActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/accounts/AccountAuthenticatorActivity;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/accounts/AccountAuthenticatorActivity;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Activity;-stopLockTask-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Activity;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ActivityGroup;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityGroup;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ActivityManager;-getRecentTasks-(I I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-getRunningAppProcesses-()Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-getRunningTasks-(I)Ljava/util/List;": ["android.permission.GET_TASKS"], "Landroid/app/ActivityManager;-killBackgroundProcesses-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-moveTaskToFront-(I I Landroid/os/Bundle;)V": ["android.permission.BROADCAST_STICKY", "android.permission.REORDER_TASKS"], "Landroid/app/ActivityManager;-restartPackage-(Ljava/lang/String;)V": ["android.permission.KILL_BACKGROUND_PROCESSES"], "Landroid/app/AliasActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/AliasActivity;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/AliasActivity;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/Application;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Application;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ExpandableListActivity;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ExpandableListActivity;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/JobSchedulerImpl;-schedule-(Landroid/app/job/JobInfo;)I": ["android.permission.RECEIVE_BOOT_COMPLETED"], "Landroid/app/KeyguardManager$KeyguardLock;-disableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager$KeyguardLock;-reenableKeyguard-()V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/KeyguardManager;-exitKeyguardSecurely-(Landroid/app/KeyguardManager$OnKeyguardExitResult;)V": ["android.permission.DISABLE_KEYGUARD"], "Landroid/app/ListActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/ListActivity;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/ListActivity;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/NativeActivity;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/NativeActivity;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Service;-stopSelf-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Service;-stopSelf-(I)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/Service;-stopSelfResult-(I)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/TabActivity;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/TabActivity;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/WallpaperManager;-clear-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-clear-(I)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setBitmap-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setBitmap-(Landroid/graphics/Bitmap; Landroid/graphics/Rect; Z)I": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setBitmap-(Landroid/graphics/Bitmap; Landroid/graphics/Rect; Z I)I": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setResource-(I)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setResource-(I I)I": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setStream-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setStream-(Ljava/io/InputStream; Landroid/graphics/Rect; Z)I": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-setStream-(Ljava/io/InputStream; Landroid/graphics/Rect; Z I)I": ["android.permission.SET_WALLPAPER"], "Landroid/app/WallpaperManager;-suggestDesiredDimensions-(I I)V": ["android.permission.SET_WALLPAPER_HINTS"], "Landroid/app/admin/DevicePolicyManager;-getWifiMacAddress-(Landroid/content/ComponentName;)Ljava/lang/String;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/app/backup/BackupAgentHelper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/app/backup/BackupAgentHelper;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupAgentHelper;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/app/backup/BackupManager;-dataChanged-()V": ["android.permission.RECEIVE_BOOT_COMPLETED"], "Landroid/app/backup/BackupManager;-dataChanged-(Ljava/lang/String;)V": ["android.permission.RECEIVE_BOOT_COMPLETED"], "Landroid/bluetooth/BluetoothA2dp;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothA2dp;-isA2dpPlaying-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-cancelDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-closeProfileProxy-(I Landroid/bluetooth/BluetoothProfile;)V": ["android.permission.BLUETOOTH", "android.permission.BROADCAST_STICKY"], "Landroid/bluetooth/BluetoothAdapter;-disable-()Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-enable-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-getAddress-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBluetoothLeAdvertiser-()Landroid/bluetooth/le/BluetoothLeAdvertiser;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBluetoothLeScanner-()Landroid/bluetooth/le/BluetoothLeScanner;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getBondedDevices-()Ljava/util/Set;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getProfileConnectionState-(I)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getProfileProxy-(Landroid/content/Context; Landroid/bluetooth/BluetoothProfile$ServiceListener; I)Z": ["android.permission.BLUETOOTH", "android.permission.BROADCAST_STICKY"], "Landroid/bluetooth/BluetoothAdapter;-getScanMode-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-getState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isDiscovering-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isEnabled-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isMultipleAdvertisementSupported-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isOffloadedFilteringSupported-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-isOffloadedScanBatchingSupported-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingInsecureRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-listenUsingRfcommWithServiceRecord-(Ljava/lang/String; Ljava/util/UUID;)Landroid/bluetooth/BluetoothServerSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-setName-(Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-startDiscovery-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothAdapter;-startLeScan-([Ljava/util/UUID; Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-startLeScan-(Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothAdapter;-stopLeScan-(Landroid/bluetooth/BluetoothAdapter$LeScanCallback;)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-connectGatt-(Landroid/content/Context; Z Landroid/bluetooth/BluetoothGattCallback;)Landroid/bluetooth/BluetoothGatt;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-connectGatt-(Landroid/content/Context; Z Landroid/bluetooth/BluetoothGattCallback; I)Landroid/bluetooth/BluetoothGatt;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-createBond-()Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothDevice;-createInsecureRfcommSocketToServiceRecord-(Ljava/util/UUID;)Landroid/bluetooth/BluetoothSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-createRfcommSocketToServiceRecord-(Ljava/util/UUID;)Landroid/bluetooth/BluetoothSocket;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-fetchUuidsWithSdp-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBluetoothClass-()Landroid/bluetooth/BluetoothClass;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getBondState-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getName-()Ljava/lang/String;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getType-()I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-getUuids-()[Landroid/os/ParcelUuid;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothDevice;-setPin-([B)Z": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothGatt;-abortReliableWrite-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-abortReliableWrite-(Landroid/bluetooth/BluetoothDevice;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-beginReliableWrite-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-close-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-connect-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-disconnect-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-discoverServices-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-executeReliableWrite-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readCharacteristic-(Landroid/bluetooth/BluetoothGattCharacteristic;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readDescriptor-(Landroid/bluetooth/BluetoothGattDescriptor;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-readRemoteRssi-()Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-requestConnectionPriority-(I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-requestMtu-(I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-setCharacteristicNotification-(Landroid/bluetooth/BluetoothGattCharacteristic; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-writeCharacteristic-(Landroid/bluetooth/BluetoothGattCharacteristic;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGatt;-writeDescriptor-(Landroid/bluetooth/BluetoothGattDescriptor;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-addService-(Landroid/bluetooth/BluetoothGattService;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-cancelConnection-(Landroid/bluetooth/BluetoothDevice;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-clearServices-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-close-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-connect-(Landroid/bluetooth/BluetoothDevice; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-notifyCharacteristicChanged-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothGattCharacteristic; Z)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-removeService-(Landroid/bluetooth/BluetoothGattService;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothGattServer;-sendResponse-(Landroid/bluetooth/BluetoothDevice; I I I [B)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-isAudioConnected-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHeadset;-sendVendorSpecificResultCode-(Landroid/bluetooth/BluetoothDevice; Ljava/lang/String; Ljava/lang/String;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHeadset;-startVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHeadset;-stopVoiceRecognition-(Landroid/bluetooth/BluetoothDevice;)Z": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/BluetoothHealth;-connectChannelToSource-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-disconnectChannel-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration; I)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getConnectedDevices-()Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getConnectionState-(Landroid/bluetooth/BluetoothDevice;)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getDevicesMatchingConnectionStates-([I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-getMainChannelFd-(Landroid/bluetooth/BluetoothDevice; Landroid/bluetooth/BluetoothHealthAppConfiguration;)Landroid/os/ParcelFileDescriptor;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-registerSinkAppConfiguration-(Ljava/lang/String; I Landroid/bluetooth/BluetoothHealthCallback;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothHealth;-unregisterAppConfiguration-(Landroid/bluetooth/BluetoothHealthAppConfiguration;)Z": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getConnectedDevices-(I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getConnectionState-(Landroid/bluetooth/BluetoothDevice; I)I": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-getDevicesMatchingConnectionStates-(I [I)Ljava/util/List;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothManager;-openGattServer-(Landroid/content/Context; Landroid/bluetooth/BluetoothGattServerCallback;)Landroid/bluetooth/BluetoothGattServer;": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/BluetoothSocket;-connect-()V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeAdvertiser;-startAdvertising-(Landroid/bluetooth/le/AdvertiseSettings; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeAdvertiser;-startAdvertising-(Landroid/bluetooth/le/AdvertiseSettings; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseData; Landroid/bluetooth/le/AdvertiseCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeAdvertiser;-stopAdvertising-(Landroid/bluetooth/le/AdvertiseCallback;)V": ["android.permission.BLUETOOTH_ADMIN"], "Landroid/bluetooth/le/BluetoothLeScanner;-flushPendingScanResults-(Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeScanner;-startScan-(Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeScanner;-startScan-(Ljava/util/List; Landroid/bluetooth/le/ScanSettings; Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH"], "Landroid/bluetooth/le/BluetoothLeScanner;-stopScan-(Landroid/bluetooth/le/ScanCallback;)V": ["android.permission.BLUETOOTH", "android.permission.BLUETOOTH_ADMIN"], "Landroid/content/ContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/ContextWrapper;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/content/ContextWrapper;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/content/MutableContextWrapper;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/content/MutableContextWrapper;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/hardware/ConsumerIrManager;-getCarrierFrequencies-()[Landroid/hardware/ConsumerIrManager$CarrierFrequencyRange;": ["android.permission.TRANSMIT_IR"], "Landroid/hardware/ConsumerIrManager;-transmit-(I [I)V": ["android.permission.TRANSMIT_IR"], "Landroid/hardware/fingerprint/FingerprintManager;-authenticate-(Landroid/hardware/fingerprint/FingerprintManager$CryptoObject; Landroid/os/CancellationSignal; I Landroid/hardware/fingerprint/FingerprintManager$AuthenticationCallback; Landroid/os/Handler;)V": ["android.permission.USE_FINGERPRINT"], "Landroid/hardware/fingerprint/FingerprintManager;-hasEnrolledFingerprints-()Z": ["android.permission.USE_FINGERPRINT"], "Landroid/hardware/fingerprint/FingerprintManager;-isHardwareDetected-()Z": ["android.permission.USE_FINGERPRINT"], "Landroid/inputmethodservice/InputMethodService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/inputmethodservice/InputMethodService;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/inputmethodservice/InputMethodService;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/location/LocationManager;-addGpsStatusListener-(Landroid/location/GpsStatus$Listener;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addNmeaListener-(Landroid/location/GpsStatus$NmeaListener;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addNmeaListener-(Landroid/location/OnNmeaMessageListener;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addNmeaListener-(Landroid/location/OnNmeaMessageListener; Landroid/os/Handler;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-addProximityAlert-(D D F J Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getBestProvider-(Landroid/location/Criteria; Z)Ljava/lang/String;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getLastKnownLocation-(Ljava/lang/String;)Landroid/location/Location;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProvider-(Ljava/lang/String;)Landroid/location/LocationProvider;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Landroid/location/Criteria; Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-getProviders-(Z)Ljava/util/List;": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-registerGnssStatusCallback-(Landroid/location/GnssStatus$Callback;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-registerGnssStatusCallback-(Landroid/location/GnssStatus$Callback; Landroid/os/Handler;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeUpdates-(Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-removeUpdates-(Landroid/location/LocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(Ljava/lang/String; J F Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestLocationUpdates-(J F Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Landroid/location/Criteria; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-requestSingleUpdate-(Ljava/lang/String; Landroid/location/LocationListener; Landroid/os/Looper;)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION"], "Landroid/location/LocationManager;-sendExtraCommand-(Ljava/lang/String; Ljava/lang/String; Landroid/os/Bundle;)Z": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.ACCESS_FINE_LOCATION", "android.permission.ACCESS_LOCATION_EXTRA_COMMANDS"], "Landroid/media/AsyncPlayer;-play-(Landroid/content/Context; Landroid/net/Uri; Z Landroid/media/AudioAttributes;)V": ["android.permission.WAKE_LOCK"], "Landroid/media/AsyncPlayer;-play-(Landroid/content/Context; Landroid/net/Uri; Z I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/AsyncPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/AudioManager;-adjustStreamVolume-(I I I)V": ["android.permission.BLUETOOTH"], "Landroid/media/AudioManager;-setBluetoothScoOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setMicrophoneMute-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setMode-(I)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setSpeakerphoneOn-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-setStreamMute-(I Z)V": ["android.permission.BLUETOOTH"], "Landroid/media/AudioManager;-setStreamVolume-(I I I)V": ["android.permission.BLUETOOTH"], "Landroid/media/AudioManager;-startBluetoothSco-()V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/AudioManager;-stopBluetoothSco-()V": ["android.permission.BLUETOOTH", "android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/media/MediaPlayer;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-reset-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-setWakeMode-(Landroid/content/Context; I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaPlayer;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/MediaRouter$RouteGroup;-requestSetVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/MediaRouter$RouteGroup;-requestUpdateVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/MediaRouter$RouteInfo;-requestSetVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/MediaRouter$RouteInfo;-requestUpdateVolume-(I)V": ["android.permission.BLUETOOTH"], "Landroid/media/MediaScannerConnection;-disconnect-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/media/Ringtone;-play-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-setAudioAttributes-(Landroid/media/AudioAttributes;)V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-setStreamType-(I)V": ["android.permission.WAKE_LOCK"], "Landroid/media/Ringtone;-stop-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(Landroid/content/Context; Landroid/net/Uri;)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-getRingtone-(I)Landroid/media/Ringtone;": ["android.permission.WAKE_LOCK"], "Landroid/media/RingtoneManager;-stopPreviousRingtone-()V": ["android.permission.WAKE_LOCK"], "Landroid/media/browse/MediaBrowser;-disconnect-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/ConnectivityManager;-getActiveNetwork-()Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getActiveNetworkInfo-()Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getAllNetworkInfo-()[Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getAllNetworks-()[Landroid/net/Network;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getLinkProperties-(Landroid/net/Network;)Landroid/net/LinkProperties;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkCapabilities-(Landroid/net/Network;)Landroid/net/NetworkCapabilities;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkInfo-(Landroid/net/Network;)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getNetworkInfo-(I)Landroid/net/NetworkInfo;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-getRestrictBackgroundStatus-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-isActiveNetworkMetered-()Z": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-registerDefaultNetworkCallback-(Landroid/net/ConnectivityManager$NetworkCallback;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-registerNetworkCallback-(Landroid/net/NetworkRequest; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE"], "Landroid/net/ConnectivityManager;-registerNetworkCallback-(Landroid/net/NetworkRequest; Landroid/net/ConnectivityManager$NetworkCallback;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-reportBadNetwork-(Landroid/net/Network;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET"], "Landroid/net/ConnectivityManager;-reportNetworkConnectivity-(Landroid/net/Network; Z)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.INTERNET"], "Landroid/net/ConnectivityManager;-requestBandwidthUpdate-(Landroid/net/Network;)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-requestNetwork-(Landroid/net/NetworkRequest; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-requestNetwork-(Landroid/net/NetworkRequest; Landroid/net/ConnectivityManager$NetworkCallback;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-requestRouteToHost-(I I)Z": ["android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/ConnectivityManager;-startUsingNetworkFeature-(I Ljava/lang/String;)I": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_NETWORK_STATE"], "Landroid/net/VpnService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-onRevoke-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/net/VpnService;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/net/VpnService;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/net/sip/SipAudioCall;-close-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-endCall-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/sip/SipAudioCall;-setSpeakerMode-(Z)V": ["android.permission.MODIFY_AUDIO_SETTINGS"], "Landroid/net/sip/SipAudioCall;-startAudio-()V": ["android.permission.ACCESS_WIFI_STATE", "android.permission.WAKE_LOCK"], "Landroid/net/sip/SipManager;-close-(Ljava/lang/String;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-createSipSession-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipSession$Listener;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-getSessionFor-(Landroid/content/Intent;)Landroid/net/sip/SipSession;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isOpened-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-isRegistered-(Ljava/lang/String;)Z": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipProfile; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-makeAudioCall-(Ljava/lang/String; Ljava/lang/String; Landroid/net/sip/SipAudioCall$Listener; I)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-open-(Landroid/net/sip/SipProfile; Landroid/app/PendingIntent; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-register-(Landroid/net/sip/SipProfile; I Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-setRegistrationListener-(Ljava/lang/String; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-takeAudioCall-(Landroid/content/Intent; Landroid/net/sip/SipAudioCall$Listener;)Landroid/net/sip/SipAudioCall;": ["android.permission.USE_SIP"], "Landroid/net/sip/SipManager;-unregister-(Landroid/net/sip/SipProfile; Landroid/net/sip/SipRegistrationListener;)V": ["android.permission.USE_SIP"], "Landroid/net/wifi/WifiManager$MulticastLock;-acquire-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$MulticastLock;-release-()V": ["android.permission.CHANGE_WIFI_MULTICAST_STATE"], "Landroid/net/wifi/WifiManager$WifiLock;-acquire-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager$WifiLock;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/net/wifi/WifiManager;-addNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-cancelWps-(Landroid/net/wifi/WifiManager$WpsCallback;)V": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disableNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-disconnect-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-enableNetwork-(I Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConfiguredNetworks-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getConnectionInfo-()Landroid/net/wifi/WifiInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getDhcpInfo-()Landroid/net/DhcpInfo;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getScanResults-()Ljava/util/List;": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-getWifiState-()I": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-is5GHzBandSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isDeviceToApRttSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isEnhancedPowerReportingSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isP2pSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isPreferredNetworkOffloadSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isScanAlwaysAvailable-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isTdlsSupported-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-isWifiEnabled-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-pingSupplicant-()Z": ["android.permission.ACCESS_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reassociate-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-reconnect-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-removeNetwork-(I)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-saveConfiguration-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-setWifiEnabled-(Z)Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-startScan-()Z": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-startWps-(Landroid/net/wifi/WpsInfo; Landroid/net/wifi/WifiManager$WpsCallback;)V": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/WifiManager;-updateNetwork-(Landroid/net/wifi/WifiConfiguration;)I": ["android.permission.CHANGE_WIFI_STATE"], "Landroid/net/wifi/p2p/WifiP2pManager;-initialize-(Landroid/content/Context; Landroid/os/Looper; Landroid/net/wifi/p2p/WifiP2pManager$ChannelListener;)Landroid/net/wifi/p2p/WifiP2pManager$Channel;": ["android.permission.ACCESS_WIFI_STATE", "android.permission.CHANGE_WIFI_STATE"], "Landroid/os/PowerManager$WakeLock;-acquire-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-acquire-(J)V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-release-()V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-release-(I)V": ["android.permission.WAKE_LOCK"], "Landroid/os/PowerManager$WakeLock;-setWorkSource-(Landroid/os/WorkSource;)V": ["android.permission.WAKE_LOCK"], "Landroid/os/SystemVibrator;-cancel-()V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(I Ljava/lang/String; [J I Landroid/media/AudioAttributes;)V": ["android.permission.VIBRATE"], "Landroid/os/SystemVibrator;-vibrate-(I Ljava/lang/String; J Landroid/media/AudioAttributes;)V": ["android.permission.VIBRATE"], "Landroid/security/KeyChain;-getCertificateChain-(Landroid/content/Context; Ljava/lang/String;)[Ljava/security/cert/X509Certificate;": ["android.permission.BROADCAST_STICKY"], "Landroid/security/KeyChain;-getPrivateKey-(Landroid/content/Context; Ljava/lang/String;)Ljava/security/PrivateKey;": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/service/dreams/DreamService;-dispatchGenericMotionEvent-(Landroid/view/MotionEvent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-dispatchKeyEvent-(Landroid/view/KeyEvent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-dispatchKeyShortcutEvent-(Landroid/view/KeyEvent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-dispatchTouchEvent-(Landroid/view/MotionEvent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-dispatchTrackballEvent-(Landroid/view/MotionEvent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-finish-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-onWakeUp-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/dreams/DreamService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/dreams/DreamService;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/dreams/DreamService;-wakeUp-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/quicksettings/TileService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/service/quicksettings/TileService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/quicksettings/TileService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/quicksettings/TileService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/quicksettings/TileService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/quicksettings/TileService;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/quicksettings/TileService;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/service/voice/VoiceInteractionService;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/voice/VoiceInteractionService;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/service/voice/VoiceInteractionService;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/service/voice/VoiceInteractionService;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/SpeechRecognizer;-destroy-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-getAvailableLanguages-()Ljava/util/Set;": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-getDefaultLanguage-()Ljava/util/Locale;": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-getDefaultVoice-()Landroid/speech/tts/Voice;": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-getFeatures-(Ljava/util/Locale;)Ljava/util/Set;": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-getLanguage-()Ljava/util/Locale;": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-getVoice-()Landroid/speech/tts/Voice;": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-getVoices-()Ljava/util/Set;": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-isLanguageAvailable-(Ljava/util/Locale;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-isSpeaking-()Z": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-playEarcon-(Ljava/lang/String; I Landroid/os/Bundle; Ljava/lang/String;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-playEarcon-(Ljava/lang/String; I Ljava/util/HashMap;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-playSilence-(J I Ljava/util/HashMap;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-playSilentUtterance-(J I Ljava/lang/String;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-setLanguage-(Ljava/util/Locale;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-setVoice-(Landroid/speech/tts/Voice;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-shutdown-()V": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-speak-(Ljava/lang/CharSequence; I Landroid/os/Bundle; Ljava/lang/String;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-speak-(Ljava/lang/String; I Ljava/util/HashMap;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-stop-()I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-synthesizeToFile-(Ljava/lang/CharSequence; Landroid/os/Bundle; Ljava/io/File; Ljava/lang/String;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/speech/tts/TextToSpeech;-synthesizeToFile-(Ljava/lang/String; Ljava/util/HashMap; Ljava/lang/String;)I": ["android.permission.BROADCAST_STICKY"], "Landroid/telephony/PhoneNumberUtils;-isVoiceMailNumber-(Ljava/lang/String;)Z": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/SmsManager;-divideMessage-(Ljava/lang/String;)Ljava/util/ArrayList;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/SmsManager;-downloadMultimediaMessage-(Landroid/content/Context; Ljava/lang/String; Landroid/net/Uri; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.RECEIVE_MMS"], "Landroid/telephony/SmsManager;-injectSmsPdu-([B Ljava/lang/String; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/SmsManager;-sendDataMessage-(Ljava/lang/String; Ljava/lang/String; S [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendMultimediaMessage-(Landroid/content/Context; Landroid/net/Uri; Ljava/lang/String; Landroid/os/Bundle; Landroid/app/PendingIntent;)V": ["android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendMultipartTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/util/ArrayList; Ljava/util/ArrayList; Ljava/util/ArrayList;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS"], "Landroid/telephony/SmsManager;-sendTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS"], "Landroid/telephony/TelephonyManager;-getAllCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getCellLocation-()Landroid/telephony/CellLocation;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getDeviceId-(I)Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getGroupIdLevel1-()Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getIccAuthentication-(I I Ljava/lang/String;)Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getLine1Number-()Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getNeighboringCellInfo-()Ljava/util/List;": ["android.permission.ACCESS_FINE_LOCATION"], "Landroid/telephony/TelephonyManager;-getPhoneCount-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getSimSerialNumber-()Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getSimState-()I": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getSubscriberId-()Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailAlphaTag-()Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-getVoiceMailNumber-()Ljava/lang/String;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/TelephonyManager;-listen-(Landroid/telephony/PhoneStateListener; I)V": ["android.permission.ACCESS_COARSE_LOCATION", "android.permission.READ_PHONE_STATE"], "Landroid/telephony/gsm/SmsManager;-divideMessage-(Ljava/lang/String;)Ljava/util/ArrayList;": ["android.permission.ACCESS_NETWORK_STATE"], "Landroid/telephony/gsm/SmsManager;-sendDataMessage-(Ljava/lang/String; Ljava/lang/String; S [B Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.ACCESS_NETWORK_STATE", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS"], "Landroid/telephony/gsm/SmsManager;-sendMultipartTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/util/ArrayList; Ljava/util/ArrayList; Ljava/util/ArrayList;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS"], "Landroid/telephony/gsm/SmsManager;-sendTextMessage-(Ljava/lang/String; Ljava/lang/String; Ljava/lang/String; Landroid/app/PendingIntent; Landroid/app/PendingIntent;)V": ["android.permission.READ_EXTERNAL_STORAGE", "android.permission.SEND_SMS"], "Landroid/test/IsolatedContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/IsolatedContext;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/test/IsolatedContext;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/RenamingDelegatingContext;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/test/RenamingDelegatingContext;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/test/mock/MockApplication;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/test/mock/MockApplication;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-clearWallpaper-()V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-removeStickyBroadcast-(Landroid/content/Intent;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-removeStickyBroadcastAsUser-(Landroid/content/Intent; Landroid/os/UserHandle;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Landroid/graphics/Bitmap;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-setWallpaper-(Ljava/io/InputStream;)V": ["android.permission.SET_WALLPAPER"], "Landroid/view/ContextThemeWrapper;-stopService-(Landroid/content/Intent;)Z": ["android.permission.BROADCAST_STICKY"], "Landroid/view/ContextThemeWrapper;-unbindService-(Landroid/content/ServiceConnection;)V": ["android.permission.BROADCAST_STICKY"], "Landroid/view/inputmethod/InputMethodManager;-showInputMethodAndSubtypeEnabler-(Ljava/lang/String;)V": ["android.permission.READ_EXTERNAL_STORAGE"], "Landroid/widget/VideoView;-getAudioSessionId-()I": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-onKeyDown-(I Landroid/view/KeyEvent;)Z": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-pause-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-resume-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoPath-(Ljava/lang/String;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoURI-(Landroid/net/Uri;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-setVideoURI-(Landroid/net/Uri; Ljava/util/Map;)V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-start-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-stopPlayback-()V": ["android.permission.WAKE_LOCK"], "Landroid/widget/VideoView;-suspend-()V": ["android.permission.WAKE_LOCK"]}
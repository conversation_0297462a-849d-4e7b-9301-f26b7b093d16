.class public Lcom/android/support/Menu;
.super Ljava/lang/Object;
.source "Menu.java"


# static fields
.field public static final TAG:Ljava/lang/String; = "Mod_Menu"


# instance fields
.field BTN_COLOR:I

.field BtnOFF:I

.field BtnON:I

.field CategoryBG:I

.field CheckBoxColor:I

.field ICON_ALPHA:F

.field ICON_SIZE:I

.field MENU_BG_COLOR:I

.field MENU_CORNER:F

.field MENU_FEATURE_BG_COLOR:I

.field MENU_HEIGHT:I

.field MENU_WIDTH:I

.field NumberTxtColor:Ljava/lang/String;

.field POS_X:I

.field POS_Y:I

.field RadioColor:I

.field SeekBarColor:I

.field SeekBarProgressColor:I

.field TEXT_COLOR:I

.field TEXT_COLOR_2:I

.field TEXT_COLOR_3:I

.field TEXT_COLOR_4:I

.field TEXT_COLOR_5:I

.field TEXT_COLOR_6:I

.field ToggleOFF:I

.field ToggleON:I

.field categorymaintext:Landroid/widget/TextView;

.field categorytitle:Landroid/widget/TextView;

.field getContext:Landroid/content/Context;

.field icon_back:Landroid/widget/TextView;

.field mCollapse:Landroid/widget/LinearLayout;

.field mCollapsed:Landroid/widget/RelativeLayout;

.field mExpanded:Landroid/widget/LinearLayout;

.field mRootContainer:Landroid/widget/RelativeLayout;

.field mSettings:Landroid/widget/LinearLayout;

.field mWindowManager:Landroid/view/WindowManager;

.field mods:Landroid/widget/LinearLayout;

.field overlayRequired:Z

.field rootFrame:Landroid/widget/FrameLayout;

.field scrlLL:Landroid/widget/LinearLayout$LayoutParams;

.field scrlLLExpanded:Landroid/widget/LinearLayout$LayoutParams;

.field scrollView:Landroid/widget/ScrollView;

.field startimage:Landroid/widget/ImageView;

.field stopChecking:Z

.field title:Landroid/widget/TextView;

.field vmParams:Landroid/view/WindowManager$LayoutParams;


# direct methods
.method static bridge native synthetic -$$Nest$mButton(Lcom/android/support/Menu;Landroid/widget/LinearLayout;ILjava/lang/String;)V
.end method

.method static bridge native synthetic -$$Nest$mfeatureList(Lcom/android/support/Menu;[Ljava/lang/String;Landroid/widget/LinearLayout;)V
.end method

.method static bridge native synthetic -$$Nest$mfromHtml(Lcom/android/support/Menu;Ljava/lang/String;)Ljava/lang/CharSequence;
.end method

.method static bridge native synthetic -$$Nest$misViewCollapsed(Lcom/android/support/Menu;)Z
.end method

.method public constructor <init>(Landroid/content/Context;)V
    .locals 3
    .param p1, "context"    # Landroid/content/Context;

    .line 165
    invoke-direct {p0}, Ljava/lang/Object;-><init>()V

    .line 91
    const-string v0, "#82CAFD"

    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lcom/android/support/Menu;->TEXT_COLOR:I

    .line 92
    const-string v0, "#FFFFFF"

    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_2:I

    .line 93
    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_3:I

    .line 94
    const-string v1, "#9932cc"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_4:I

    .line 95
    const-string v1, "#482d55"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_5:I

    .line 96
    const-string v1, "#000000"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->TEXT_COLOR_6:I

    .line 97
    const-string v1, "#1C262D"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->BTN_COLOR:I

    .line 98
    const-string v1, "#EE1C2A35"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->MENU_BG_COLOR:I

    .line 99
    const-string v1, "#DD141C22"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->MENU_FEATURE_BG_COLOR:I

    .line 100
    const/16 v1, 0x122

    iput v1, p0, Lcom/android/support/Menu;->MENU_WIDTH:I

    .line 101
    const/16 v1, 0xd2

    iput v1, p0, Lcom/android/support/Menu;->MENU_HEIGHT:I

    .line 102
    const/4 v1, 0x0

    iput v1, p0, Lcom/android/support/Menu;->POS_X:I

    .line 103
    const/16 v1, 0x64

    iput v1, p0, Lcom/android/support/Menu;->POS_Y:I

    .line 104
    const/high16 v1, 0x40800000    # 4.0f

    iput v1, p0, Lcom/android/support/Menu;->MENU_CORNER:F

    .line 105
    const/16 v1, 0x2d

    iput v1, p0, Lcom/android/support/Menu;->ICON_SIZE:I

    .line 106
    const v1, 0x3f333333    # 0.7f

    iput v1, p0, Lcom/android/support/Menu;->ICON_ALPHA:F

    .line 107
    const v1, -0xff0100

    iput v1, p0, Lcom/android/support/Menu;->ToggleON:I

    .line 108
    const/high16 v1, -0x10000

    iput v1, p0, Lcom/android/support/Menu;->ToggleOFF:I

    .line 109
    const-string v1, "#1b5e20"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->BtnON:I

    .line 110
    const-string v1, "#7f0000"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->BtnOFF:I

    .line 111
    const-string v1, "#2F3D4C"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->CategoryBG:I

    .line 112
    const-string v1, "#80CBC4"

    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    iput v2, p0, Lcom/android/support/Menu;->SeekBarColor:I

    .line 113
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v2

    iput v2, p0, Lcom/android/support/Menu;->SeekBarProgressColor:I

    .line 114
    invoke-static {v1}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v1

    iput v1, p0, Lcom/android/support/Menu;->CheckBoxColor:I

    .line 115
    invoke-static {v0}, Landroid/graphics/Color;->parseColor(Ljava/lang/String;)I

    move-result v0

    iput v0, p0, Lcom/android/support/Menu;->RadioColor:I

    .line 116
    const-string v0, "#41c300"

    iput-object v0, p0, Lcom/android/support/Menu;->NumberTxtColor:Ljava/lang/String;

    .line 167
    iput-object p1, p0, Lcom/android/support/Menu;->getContext:Landroid/content/Context;

    .line 168
    sput-object p1, Lcom/android/support/Preferences;->context:Landroid/content/Context;

    .line 171
    invoke-direct {p0, p1}, Lcom/android/support/Menu;->initializeMenu(Landroid/content/Context;)V

    .line 172
    return-void
.end method

.method private native AddColor(Landroid/view/View;IIIIIIIIIIIII)V
.end method

.method private native Button(Landroid/widget/LinearLayout;ILjava/lang/String;)V
.end method

.method private native ButtonLink(Ljava/lang/String;Ljava/lang/String;)Landroid/view/View;
.end method

.method private native ButtonOnOff(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
.end method

.method private native Category(Landroid/widget/LinearLayout;ZLjava/lang/String;)V
.end method

.method private native CheckBox(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
.end method

.method private native Collapse(Landroid/widget/LinearLayout;Ljava/lang/String;)V
.end method

.method private native InputNum(Landroid/widget/LinearLayout;ILjava/lang/String;I)V
.end method

.method private native InputText(Landroid/widget/LinearLayout;ILjava/lang/String;)V
.end method

.method private native RadioButton(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V
.end method

.method private native SeekBar(Landroid/widget/LinearLayout;ILjava/lang/String;II)V
.end method

.method private native Spinner(Landroid/widget/LinearLayout;ILjava/lang/String;Ljava/lang/String;)V
.end method

.method private native Switch(Landroid/widget/LinearLayout;ILjava/lang/String;Z)V
.end method

.method private native TextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V
.end method

.method private native WebTextView(Landroid/widget/LinearLayout;Ljava/lang/String;)V
.end method

.method private native convertDipToPixels(I)I
.end method

.method private native dp(I)I
.end method

.method private native featureList([Ljava/lang/String;Landroid/widget/LinearLayout;)V
.end method

.method private native fromHtml(Ljava/lang/String;)Ljava/lang/CharSequence;
.end method

.method private native initializeMenu(Landroid/content/Context;)V
.end method

.method private native isViewCollapsed()Z
.end method

.method private native onTouchListener()Landroid/view/View$OnTouchListener;
.end method


# virtual methods
.method native GetFeatureList()[Ljava/lang/String;
.end method

.method native Icon()Ljava/lang/String;
.end method

.method native IconWebViewData()Ljava/lang/String;
.end method

.method native Init(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V
.end method

.method native IsGameLibLoaded()Z
.end method

.method public native SetWindowManagerActivity()V
.end method

.method public native SetWindowManagerWindowService()V
.end method

.method native SettingsList()[Ljava/lang/String;
.end method

.method public native ShowMenu()V
.end method

.method public native onDestroy()V
.end method

.method public native setVisibility(I)V
.end method

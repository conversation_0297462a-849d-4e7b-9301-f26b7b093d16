{"tools": {"python": {"executable": "..\\work_files\\Installation_Files\\Amped_env\\Scripts\\python.exe", "description": "Python interpreter with required packages"}, "java": {"executable": "..\\work_files\\Java\\jdk-21\\bin\\java.exe", "description": "Java JDK for running JAR tools"}, "apktool": {"jar": "tools\\apktool.jar", "description": "APK decompilation and recompilation tool"}, "zipalign": {"executable": "tools\\zipalign.exe", "description": "APK optimization tool from Android SDK"}, "apksigner": {"jar": "tools\\apksigner.jar", "description": "APK signing tool"}, "manifest_editor": {"jar": "tools\\manifest-editor.jar", "description": "AndroidManifest.xml modification tool"}}, "build": {"ndk": {"directory": "..\\work_files\\SDK\\ndk\\26.3.11579264", "build_command": "ndk-build", "description": "Android NDK for native compilation"}, "architectures": ["arm64-v8a"], "optimization_level": "O2", "debug_symbols": false}, "signing": {"keystore": "keystore\\debug.keystore", "password": "android", "alias": "androiddebugkey", "alias_password": "android", "description": "APK signing configuration"}, "paths": {"temp_directory": ".tmp", "output_directory": "output", "project_directory": "project", "source_archive": "project-source.zip"}, "protection": {"default_filter": "filter.txt", "obfuscation": true, "dynamic_register": false, "skip_synthetic": false, "force_keep_libs": false}, "logging": {"level": "INFO", "format": "[%(levelname)s] %(name)s: %(message)s", "file": null}, "advanced": {"max_dex_methods": 65536, "compilation_threads": 12, "memory_limit_mb": 14096, "timeout_seconds": 360000}}
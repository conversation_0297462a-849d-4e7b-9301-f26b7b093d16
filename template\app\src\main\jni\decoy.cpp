// decoy.cpp
// This file contains unique, fake obfuscated strings using the OLD OBFUSCATE macro.
// Its purpose is to be found by leechers' tools, confirming our trap is working, while wasting their time.

#include "Includes/obfuscate.h"

// Fake class that looks like it handles security
class DecoySecurityManager {
private:
    // These strings look real but are unique to this decoy file.
    const char* decoy_token = OBFUSCATE("TOKEN_live_123_abc_DoNotSteal");
    const char* decoy_hash_algo = OBFUSCATE("sha256_validation_mode");
    bool m_isInitialized = false;

public:
    // A fake initialization that looks like it does something
    void initialize() {
        volatile const char* temp = decoy_token;
        (void)temp; // Use volatile to prevent optimization
        m_isInitialized = true;
    }
    
    // A fake check that depends on the fake initialization
    bool verify_integrity() {
        if (!m_isInitialized) {
            return false; // Looks like a real logic check
        }
        volatile const char* temp = decoy_hash_algo;
        (void)temp;
        return true; // Always passes if "initialized"
    } // <-- THIS IS THE MISSING BRACE FOR THE FUNCTION
}; // <-- THIS IS THE BRACE FOR THE CLASS

// Global fake security instance
static DecoySecurityManager g_decoySecurityManager;

namespace DecoyStrings {
    // Fake game data that looks important but isn't
    const char* decoy_api_key = OBFUSCATE("api_key_live_12345_abcdef");
    const char* decoy_title_string = OBFUSCATE("<b>Title: Amped Gems Mod</b>"); // The perfect trap
    const char* decoy_license_check = OBFUSCATE("https://api.example.com/verify_license");
    
    // Fake anti-cheat strings
    const char* decoy_anticheat_1 = OBFUSCATE("AntiTamper_Check_Module");
    const char* decoy_anticheat_2 = OBFUSCATE("Memory_Scan_Integrity_Check");
    
    // A fake function that calls another fake function
    bool perform_decoy_validation() {
        // This makes the call graph in IDA look more complex
        if (g_decoySecurityManager.verify_integrity()) {
            volatile const char* check = decoy_license_check;
            (void)check;
            return true;
        }
        return false;
    }
}

// A fake initialization function that an attacker might find and think is important.
// You could even call this from JNI_OnLoad to make it more convincing, as it does nothing harmful.
extern "C" void initialize_decoy_systems() {
    g_decoySecurityManager.initialize();
    
    if (DecoyStrings::perform_decoy_validation()) {
        volatile const char* temp = DecoyStrings::decoy_anticheat_1;
        (void)temp;
    }
}
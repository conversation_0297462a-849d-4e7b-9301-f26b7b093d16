#include <list>
#include <cstdlib>
#include <ctime>
#include <thread>
#include <vector>
#include <string.h>
#include <pthread.h>
#include <thread>
#include <cstring>
#include <jni.h>
#include <unistd.h>
#include <fstream>
#include <iostream>
#include <stdbool.h>
#include <dlfcn.h>
#include <math.h> 
#include "Includes/Logger.h"
#include "Includes/my_secure_obfuscate.h"
#include "Includes/Utils.h"
#include "Unity/Vector2.h"
#include "Unity/Vector3.h"
#include "Unity/Color.h"
#include "KittyMemory/MemoryPatch.h"
#include "Menu/Setup.h"
#include "AutoHook/AutoHook.h"
#include "EasyPatch.h"
#include "Includes/MonoString.h"
#define targetLibName SECURE_OBFUSCATE("libil2cpp.so")
#include "Includes/Macros.h"

//identify Here THE Declaration Variables //
DWORD UnityEngineTimegettimeScaleOffset = 0;
DWORD UnityEngineTimesettimeScaleOffset = 0;
bool UnityEngineTimesettimeScaleButton = false;
float fknslider =0;



//identify Here THE Hooking stuff //
void* (*UnityEngineTimesettimeScale)(void *instance, float value) = nullptr;
float (*old_UnityEngineTimegettimeScaleMethod)(void *instance);

float UnityEngineTimegettimeScaleMethod(void *instance) {
    if (instance != NULL) {
        if (UnityEngineTimesettimeScaleButton) {
            UnityEngineTimesettimeScale(instance, fknslider);
        }
    }
    return old_UnityEngineTimegettimeScaleMethod(instance);
}










ProcMap il2cppMap;
void *hack_thread(void *) {

    do {
        il2cppMap = KittyMemory::getLibraryMap("libil2cpp.so");
        sleep(5);
    } while (!il2cppMap.isValid());

    do {
        sleep(5);
    } while (!isLibraryLoaded("libAmpedGems.so"));

//identify Here THE Class Name and Methods //
auto UnityEngineTimeClass = new LoadClass(SECURE_OBFUSCATE("UnityEngine"), SECURE_OBFUSCATE("Time"));

UnityEngineTimegettimeScaleOffset = UnityEngineTimeClass->GetMethodOffsetByName(SECURE_OBFUSCATE("get_timeScale"), 0);
UnityEngineTimesettimeScaleOffset = UnityEngineTimeClass->GetMethodOffsetByName(SECURE_OBFUSCATE("set_timeScale"), 1);

HOOK_AU((void *)UnityEngineTimegettimeScaleOffset, (void *)UnityEngineTimegettimeScaleMethod, old_UnityEngineTimegettimeScaleMethod);

UnityEngineTimesettimeScale = (void*(*)(void*, float))(uintptr_t)UnityEngineTimesettimeScaleOffset;



    return NULL;
}


    
   


  


jobjectArray GetFeatureList(JNIEnv *env, jobject context) {
    jobjectArray ret;

    const char *features[] = {
        SECURE_OBFUSCATE("Category_Make sure to only download from Platinmods.com AmpedGems page, otherwise it is a malware virus"),
        SECURE_OBFUSCATE("ButtonLink_Discord Server_https://discord.gg/mMA8UKnUNV"),

//identify Here THE Buttons //
SECURE_OBFUSCATE("1_ButtonOnOff_speedup"),
SECURE_OBFUSCATE("2_SeekBar_speed_1_50"),



        SECURE_OBFUSCATE("ButtonLink_Platinmods.com_https://platinmods.com/forums/exclusive-android-mods-by-pmt.30/?starter_id=5086032&order=post_date&direction=desc"),
        SECURE_OBFUSCATE("Category_Download from platinmods only - if you downloaded from anywhere else, it is a virus")

    };


    int Total_Feature = (sizeof features / sizeof features[0]);
    ret = (jobjectArray)
            env->NewObjectArray(Total_Feature, env->FindClass(SECURE_OBFUSCATE("java/lang/String")),
                                env->NewStringUTF(""));

    for (int i = 0; i < Total_Feature; i++)
        env->SetObjectArrayElement(ret, i, env->NewStringUTF(features[i]));

    return (ret);
}

void Changes(JNIEnv *env, jclass clazz, jobject obj,
                                        jint featNum, jstring featName, jint value,
                                        jboolean boolean, jstring str) {
    switch (featNum) {

//identify Here THE Cases //
case 1:
        UnityEngineTimesettimeScaleButton = boolean;
        break;
    case 2:
        fknslider = value;
        break;
   
   
   
   
   


    }
}

__attribute__((constructor))
void lib_main() {
    pthread_t ptid;
    pthread_create(&ptid, NULL, hack_thread, NULL);
}

int RegisterMenu(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {SECURE_OBFUSCATE("Icon"), SECURE_OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(Icon)},
            {SECURE_OBFUSCATE("IconWebViewData"),  SECURE_OBFUSCATE("()Ljava/lang/String;"), reinterpret_cast<void *>(IconWebViewData)},
            {SECURE_OBFUSCATE("IsGameLibLoaded"),  SECURE_OBFUSCATE("()Z"), reinterpret_cast<void *>(isGameLibLoaded)},
            {SECURE_OBFUSCATE("Init"),  SECURE_OBFUSCATE("(Landroid/content/Context;Landroid/widget/TextView;Landroid/widget/TextView;)V"), reinterpret_cast<void *>(Init)},
            {SECURE_OBFUSCATE("SettingsList"),  SECURE_OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(SettingsList)},
            {SECURE_OBFUSCATE("GetFeatureList"),  SECURE_OBFUSCATE("()[Ljava/lang/String;"), reinterpret_cast<void *>(GetFeatureList)},
    };

    jclass clazz = env->FindClass(SECURE_OBFUSCATE("com/android/support/Menu"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}

int RegisterPreferences(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {SECURE_OBFUSCATE("Changes"), SECURE_OBFUSCATE("(Landroid/content/Context;ILjava/lang/String;IZLjava/lang/String;)V"), reinterpret_cast<void *>(Changes)},
    };
    jclass clazz = env->FindClass(SECURE_OBFUSCATE("com/android/support/Preferences"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;
    return JNI_OK;
}

int RegisterMain(JNIEnv *env) {
    JNINativeMethod methods[] = {
            {SECURE_OBFUSCATE("CheckOverlayPermission"), SECURE_OBFUSCATE("(Landroid/content/Context;)V"), reinterpret_cast<void *>(CheckOverlayPermission)},
    };
    jclass clazz = env->FindClass(SECURE_OBFUSCATE("com/android/support/Main"));
    if (!clazz)
        return JNI_ERR;
    if (env->RegisterNatives(clazz, methods, sizeof(methods) / sizeof(methods[0])) != 0)
        return JNI_ERR;

    return JNI_OK;
}

extern "C"
JNIEXPORT jint JNICALL
JNI_OnLoad(JavaVM *vm, void *reserved) {
    JNIEnv *env;
    vm->GetEnv((void **) &env, JNI_VERSION_1_6);
    if (RegisterMenu(env) != 0)
        return JNI_ERR;
    if (RegisterPreferences(env) != 0)
        return JNI_ERR;
    if (RegisterMain(env) != 0)
        return JNI_ERR;
    return JNI_VERSION_1_6;
}
